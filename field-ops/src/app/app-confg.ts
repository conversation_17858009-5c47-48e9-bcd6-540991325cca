import { AppConfig } from '@4-sure/ui-platform';
import { fieldOpsConfig } from './pages/field-ops/FieldOpsConfig';
import { manageSpsConfig } from './pages/manage-sp/ManageSPConfig';
import { settingsConfig } from './pages/settings/SettingsConfig';

export async function getAppConfig() {
  return appConfig;
}

export const appConfig: AppConfig = {
  toolBarConfig: {
    modulesList: [{ name: 'Field Ops tool', path: '/field-ops' }],
    buttonText: '',
    username: '',
    email: '',

    menuItems: [
      {
        label: 'Personal Settings',
        menuItems: [
          {
            label: 'My details',
            icon: 'bell-02',
            onClick: [
              {
                type: 'clientAction',
                action: 'navigate',
                payload: ['/settings/personal'],
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            label: 'Logout',
            icon: 'bell-02',
            onClick: [{ type: 'clientAction', action: 'logout', payload: [] }],
          },
        ],
      },
    ],
  },
  fetchCalls: [
    // {key: 'posts', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos'},
  ],
  onEnter: [],
  onLeave: [],
  workflow: {
    defaultWorkflowType: '',
    workflowTypes: {},
  },
  fieldOps: fieldOpsConfig,
  settings: settingsConfig,
  manageSps: manageSpsConfig,
  states: {},
};
