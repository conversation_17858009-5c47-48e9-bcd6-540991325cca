import { bbbeee, ManageSpsConfig, validationRegex, companyProfileStates } from '@4-sure/ui-platform';

export const manageSpsConfig: ManageSpsConfig = {
  defaultState: 'sps',
  states: {
    /*
     * SECTION: SPS STATE
     * SPS state which is the main section of the application
     */
    // #region SPS STATE
    sps: {
      title: { template: '' },
      // #region SPS STATE LEVEL FETCH CALLS
      fetchCalls: [
        {
          key: 'sps',
          method: 'POST',
          body: {},
          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sps',
          slicePath: 'payload',
        },
        {
          key: 'skills',
          method: 'POST',
          body: { enum: 'skills' },
          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_enum',
          slicePath: 'payload.skills',
        },
      ],
      // #endregion
      defaultScreen: 'list-view',
      screens: {
        /*
         * SECTION: SPS LIST VIEW
         * Sps list screen configuration
         */
        // #region SPS LIST VIEW SCREEN
        'list-view': {
          layout: {},
          fetchCalls: [],
          // #region SPS LIST FRAGMENTS
          fragments: [

            // TODO: add filters
            // {
            //   component: 'DefaultWorkflowFilters',
            //   props: {
            //     setFilteredItems: (filteredItems: any[]) => {
            //       console.log({filteredItems})
            //     },
            //     filtersData: [
            //       {
            //         buttonText: 'Skills',
            //         items: [
            //           // `js: { return skills.map(skill => {
            //           //   return {
            //           //     text: skill.name,
            //           //     filterCondition: {
            //           //       name: skill.name,
            //           //       key: 'id',
            //           //       value: skill.id,
            //           //       comparator: 'contains',
            //           //     },
            //           //   }
            //           // }) }`
            //           `js: { ({
            //             text: "name",
            //             filterCondition: {
            //               name: "name",
            //               key: 'id',
            //               value: 4,
            //               comparator: 'contains',
            //             },
            //           }) }`

            //         ],
            //       },
            //     ],

            //   },
            //   layout: {}
            // },

            {
              component: 'TeamMemberCardList',
              props: {
                linkUrl: '/manage-sps/sps/edit/details',
                linkUrlParams: [{ key: 'sp_id', value: 'id' }],
                data: `js:{
                  const companyProfileStates = ${ JSON.stringify(companyProfileStates) };
                  return sps.map(_sp => ({
                    ..._sp,
                    status: companyProfileStates[_sp.onboarding_state],
                    skills: _sp.skills.map(_skill => skills.find(_enum => _enum.id === _skill)?.name).join(", ")
                  }))
                }`,
                config: [
                  {
                    type: 'linkColumn',
                    showAvatar: true,
                    showTitleText: true,
                    showRating: false,
                    showStatusIndicator: false,
                    titleKey: 'name',
                  },
                  { type: 'textColumn', titleKey: 'status' },
                  { type: 'textColumn', titleKey: 'province' },
                  { type: 'textColumn', titleKey: 'skills' },
                  { type: 'textColumn', titleKey: 'contact_number' },
                ],
              },
              layout: {},
            },
          ],
          // #endregion
          navs: [],
        },
        // #endregion
      },
      /*
       * SECTION: SPS ACTION PANELS
       * Add all action panel item configurations here
       */
      // #region SPS ACTION PANELS
      actionPanels: [
        /*
         * SECTION: SPS SCRATCH PAD
         * Configuration for the scratch pad in the SPS state
         */
        {
          icon: 'clipboard',
          title: 'Scratch Pad', //?actionPanel=Messages--bell-02
          // fetchCalls: [],
          layout: {},
          onEnter: [],
          onLeave: [],
          fragments: [
            {
              component: 'ScratchPadView',
              layout: { marginLeft: '10px', marginRight: '10px' },
              props: {
                titlePlaceholder: 'Heading',
                icon: 'trash-01',
                iconHandler: (data: { heading: string; body: string }) =>
                  console.log(
                    'got data: Heading - ' +
                      data.heading +
                      ' Body - ' +
                      data.body
                  ),
                placeHolder: 'Text here...',
              },
            },
          ],
          actionLevel: 'bottomControls',
        },
      ],
      // #endregion
    },
    // #endregion
    'sps/edit': {
      title: { template: '' },
      fetchCalls: [
        // #region SPS EDIT STATE FETCHCALLS
        // fetchcalls that will be made the state level
        {
          key: 'sp_profile',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp',
          body: { sp_id: '$object_id' },
          slicePath: 'payload',
        },
        {
          key: 'company_documentation',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/file_actions/get_files',
          body: {
            with_thumbnails: true,
            sp_id: '$object_id',
            order: '-',
          },
          slicePath: 'payload',
        },
      ],
      // #endregion
      defaultScreen: 'details',
      onLeave: [
        {
          type: 'clientAction',
          action: 'clearStore',
          payload: [
            'sp_profile',
            'company_documentation',
            'documentsNotFound',
            'derivedCompanies',
            'derivedOperationalArea',
            'directors',
            'originalValues',
          ],
        },
      ],
      screens: {
        // #region SPS/EDIT/DETAILS SCREEN
        details: {
          layout: {},
          fetchCalls: [],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Details',
                    path: '../details',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documentation',
                    path: '../documentation',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'ProfileHero',
              layout: {
                display: 'grid',
                justifyContent: 'center',
              },
              props: {
                fullname: '$sp_profile.details.name',
                subText: 'Registration number: ',
                username: '$sp_profile.details.co_reg',
                active: false,
                image: '$sp_profile.company_profile_picture',
                profileType: 'company',
                state: '$sp_profile.details.onboarding_state',
                showImgUpload: false,
              },
            },
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    text: 'Company details',
                    options: {
                      format: 'heading',
                      type: 'section-heading',
                    },
                  },
                ],
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                // width: 'calc(100% - 226px - 56px)',
                paddingTop: '2rem',
                paddingBottom: '2rem',
              },
            },
            {
              component: 'FormBuilder',
              props: {
                bbbeee_certificate:
                  "$company_documentation?find:item.purpose === 'Reg BBEEE certificate'",
                defaultValues: {
                  trading_as: '$sp_profile.details.trading_as',
                  bbeee: '$sp_profile.details.bbeee',
                  co_reg: '$sp_profile.details.co_reg',
                  name: '$sp_profile.details.name',
                  company_type: '$sp_profile.details.company_type',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridAutoFlow: 'row',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'co_reg',
                      label: 'Company registration',
                      state: 'display-only',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'details', edit: 'details' },
                      validation: {
                        required: {
                          value: true,
                          message: 'Registration number is required',
                        },
                        // pattern: {
                        //   value: validationRegex.registration_number.pattern,
                        //   message: validationRegex.registration_number.message,
                        // },
                        conditional: {
                          value: `$store.formDataRaw.company_type === 2 ? 'registration_number' : 'company_registration_number'`,
                          message: 'Registration number is required',
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'name',
                      label: 'Company name',
                      state: 'display-only',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'details', edit: 'details' },
                      validation: {
                        required: {
                          value: true,
                          message: 'Name is required',
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'trading_as',
                      label: 'Trading as',
                      state: 'display-only',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'details', edit: 'details' },
                      validation: {},
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'single-select',
                      name: 'company_type',
                      label: 'Type of company',
                      state: 'display-only',
                      valueProp: 'id',
                      labelProp: 'name',
                      disabledWhen: true,
                      options: {
                        source: 'store',
                        storeDataPath: 'sp_enums.company_types',
                      },
                      fieldAccessPath: { view: 'details', edit: 'details' },
                      validation: {},
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'single-select',
                      dropdownScroll: true,
                      name: 'bbeee',
                      label: 'BBBEEE Level',
                      state: 'display-only',
                      labelProp: 'label',
                      valueProp: 'value',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'details', edit: 'details' },
                      validation: {},
                      options: {
                        data: bbbeee,
                        source: 'literal',
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'bbbeee_certificate',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg BBEEE certificate',
                      purposeAsName: true,
                      isLandscape: true,
                      enableUpload: false,
                      disabledWhen: true,
                      fieldAccessPath: { view: 'details', edit: 'details' },
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      action: '/manage-sps/sps/edit/documentation',
                      list: 'False',
                      css: {
                        wrapper: {
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                // width: 'calc(100% - 226px - 56px)',
              },
            },
          ],
          navs: [
            {
              label: 'Back To Field Ops Tool',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'navigate',
                  payload: ['/manage-sps/sps/list-view'],
                },
              ],
            },
            {
              label: 'Next',
              position: 'right',
              toScreen: '../banking',
            },
          ],
        },
        // #endregion
        // #region SPS/EDIT/CONTACT SCREEN
        contact: {
          layout: {},
          fetchCalls: [],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Details',
                    path: '../details',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documentation',
                    path: '../documentation',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    text: 'Company contact information',
                    options: {
                      format: 'heading',
                      type: 'page-heading',
                    },
                  },
                ],
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                paddingTop: '2rem',
                paddingBottom: '2rem',
              },
            },
            {
              component: 'FormBuilder',
              props: {
                defaultValues: {
                  contact_primary: '$sp_profile.address.contact_primary',
                  contact_secondary: '$sp_profile.address.contact_secondary',
                  contact_person: '$sp_profile.address.contact_person',
                  email_receiving: '$sp_profile.address.email_receiving',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridAutoFlow: 'row',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'contact_person',
                      label: 'Primary Contact Person Name',
                      state: 'display-only',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'address', edit: 'address' },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'contact_primary',
                      label: 'Primary Contact Number',
                      state: 'display-only',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'address', edit: 'address' },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                        pattern: {
                          value: validationRegex.phone_number.pattern,
                          message: validationRegex.phone_number.message,
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'contact_secondary',
                      label: 'Secondary Contact Number',
                      state: 'display-only',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'address', edit: 'address' },
                      validation: {
                        pattern: {
                          value: validationRegex.phone_number.pattern,
                          message: validationRegex.phone_number.message,
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'email_receiving',
                      label: 'Email Address',
                      state: 'display-only',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'address', edit: 'address' },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                        pattern: {
                          value: validationRegex.email.pattern,
                          message: validationRegex.email.message,
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
            {
              component: 'Separator',
              layout: { width: '50%', minWidth: '712px', margin: 'auto' },
              props: { height: 'thin' },
            },
            {
              component: 'FormBuilder',
              props: {
                defaultValues: {
                  physical_addr: '$sp_profile.address.physical_addr',
                  physical_city: '$sp_profile.address.physical_city',
                  physical_code: '$sp_profile.address.physical_code',
                  physical_suburb: '$sp_profile.address.physical_suburb',
                  province: '$sp_profile.address.province',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridAutoFlow: 'row',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'physical_addr',
                      label: 'Company street address',
                      state: 'display-only',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'address', edit: 'address' },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'physical_suburb',
                      label: 'Company suburb',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'address', edit: 'address' },
                      state: 'display-only',
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'physical_city',
                      label: 'Company city',
                      state: 'display-only',
                      disabledWhen: true,
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'physical_code',
                      label: 'Company postal code',
                      state: 'display-only',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'address', edit: 'address' },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'single-select',
                      name: 'province',
                      label: 'Company province',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'address', edit: 'address' },
                      dropdownScroll: true,
                      state: 'display-only',
                      validation: {
                        required: {
                          value: true,
                          message: 'Province is required',
                        },
                      },
                      labelProp: 'name',
                      valueProp: 'name',
                      options: {
                        source: 'store',
                        storeDataPath: 'sp_enums.provinces',
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
            {
              component: 'Separator',
              layout: { width: '50%', minWidth: '712px', margin: 'auto' },
              props: { height: 'thin' },
            },
            {
              component: 'FormBuilder',
              props: {
                defaultValues: {
                  postal_box: '$sp_profile.address.postal_box',
                  postal_city: '$sp_profile.address.postal_city',
                  postal_code: '$sp_profile.address.postal_code',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridAutoFlow: 'row',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'postal_box',
                      label: 'Postal address',
                      state: 'display-only',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'address', edit: 'address' },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'postal_city',
                      label: 'Postal address suburb/town',
                      state: 'display-only',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'address', edit: 'address' },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'postal_code',
                      label: 'Postal address postal code',
                      state: 'display-only',
                      disabledWhen: true,
                      fieldAccessPath: { view: 'address', edit: 'address' },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
          ],
          navs: [
            {
              label: 'Back To Field Ops Tool',
              position: 'left',
              // toScreen: '/',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'navigate',
                  payload: ['/manage-sps/sps/list-view'],
                },
              ],
            },
            {
              label: 'Previous',
              position: 'left',
              toScreen: '../directors',
            },
            { label: 'Next', position: 'right', toScreen: '../work' },
          ],
        },
        // #endregion
        // #region SPS/EDIT/WORK SCREEN
        work: {
          layout: {},
          fetchCalls: [],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Details',
                    path: '../details',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documentation',
                    path: '../documentation',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    text: 'Scope of work you accept',
                    options: {
                      format: 'heading',
                      type: 'page-heading',
                    },
                  },
                ],
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                paddingTop: '2rem',
                paddingBottom: '2rem',
              },
            },
            {
              component: 'FormBuilder',
              props: {
                sp_associated_companies:
                  '$sp_profile.companies?map:item.client_id',
                defaultValues: {
                  skills: '$sp_profile.skills',
                  after_hours: '$sp_profile.after_hours',
                  // mid: '$sp_profile.additional_identities.mid',
                  // accredition: '$sp_profile.additional_identities.accredition',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridAutoFlow: 'row',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'multiselect-checklist',
                      name: 'skills',
                      label: 'Company skills',
                      state: 'display-only',
                      fieldAccessPath: {
                        view: 'skills',
                        edit: 'skills',
                        special: 'skills',
                      },
                      labelProp: 'name',
                      valueProp: 'id',
                      options: {
                        source: 'store',
                        storeDataPath: 'sp_enums.skills',
                      },
                      checkedItems: 'sp_profile.skills',
                      maxColumns: 2,
                      css: {
                        wrapper: {
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'radio-group',
                      name: 'after_hours',
                      fieldAccessPath: {
                        view: 'after_hours',
                        edit: 'after_hours',
                      },
                      disabledWhen: true,
                      label: 'Do you work after hours?',
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      options: {
                        source: 'literal',
                        data: [
                          { label: 'Yes', value: true },
                          { label: 'No', value: false },
                        ],
                      },
                      size: 'small',
                      returnBoolean: true,
                      css: {
                        wrapper: {
                          justifySelf: 'start',
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'multiselect-checklist',
                      name: 'sp_associated_companies',
                      companies: '$sp_profile.companies',
                      disabledWhen: true,
                      fieldAccessPath: {
                        view: 'companies',
                        edit: 'companies',
                      },
                      label: 'Companies you would like to recieve work from',
                      state: 'display-only',
                      labelProp: 'name',
                      valueProp: 'id',
                      options: {
                        source: 'store',
                        storeDataPath: 'sp_enums.companies',
                      },
                      checkedItems: 'sp_profile.companies',
                      checkedItemsTransformPath: 'client_id',
                      css: {
                        wrapper: {
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
          ],
          navs: [
            {
              label: 'Back To Field Ops Tool',
              position: 'left',
              // toScreen: '/',
              onClick: [
              {
                type: 'clientAction',
                action: 'navigate',
                payload: ['/manage-sps/sps/list-view'],
              },
              ],
            },
            {
              label: 'Previous',
              position: 'left',
              toScreen: '../contact',
            },
            { label: 'Next', position: 'right', toScreen: '../areas' },
          ],
        },
        // #endregion
        // #region SPS/EDIT/AREAS SCREEN
        areas: {
          layout: {},
          fetchCalls: [],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Details',
                    path: '../details',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documentation',
                    path: '../documentation',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'FormBuilder',
              layout: {
                paddingTop: '2rem',
              },
              props: {
                operational_area: '$sp_profile.operational_area',
                defaultValues: {
                  radius: '$sp_profile.operational_area.0.operating_range',
                  jobLocation: '$sp_profile.operational_area.0.location',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridTemplateColumns: '1fr',
                    rowGap: '15px',
                    justifyItems: 'center',
                  },
                  controls: [
                    {
                      type: 'radius', // Custom control type for OperationAreas
                      name: 'operational_area',
                      label: 'Operational Area',
                      disabledWhen: true,
                      instructions: "Service provider's work area radius",
                      marks: [
                        { value: 25, label: '25km' },
                        { value: 50, label: '50km' },
                        { value: 75, label: '75km' },
                        { value: 100, label: '100km' },
                        { value: 150, label: '150km' },
                        { value: 200, label: '200km' },
                        { value: 250, label: '250km' },
                      ],
                      css: { wrapper: { gridColumn: '1', gridRow: '1' } },
                    },
                  ],
                },
              },
            },
          ],
          navs: [
            {
              label: 'Back To Field Ops Tool',
              position: 'left',
              // toScreen: '/',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'navigate',
                  payload: ['/manage-sps/sps/list-view'],
                },
              ],
            },
            {
              label: 'Previous',
              position: 'left',
              toScreen: '../work',
            },
            { label: 'Next', position: 'right', toScreen: '../documentation' },
          ],
        },
        // #endregion
        // #region SPS/EDIT/DOCUMENTATION SCREEN
        documentation: {
          layout: {},
          fetchCalls: [],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Details',
                    path: '../details',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documentation',
                    path: '../documentation',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    text: 'Uploaded documents',
                    options: {
                      format: 'heading',
                      type: 'page-heading',
                    },
                  },
                  {
                    text: 'PDF only. Maximum 5Mb per document',
                    options: {
                      format: 'heading',
                      type: 'sub-heading',
                    },
                  },
                ],
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                paddingTop: '2rem',
                paddingBottom: '2rem',
              },
            },
            {
              component: 'FormBuilder',
              props: {
                co_reg_document:
                  "$company_documentation?find:item.purpose === 'Reg Company registration'",
                pub_profile_document:
                  "$company_documentation?find:item.purpose === 'Reg Public profile'",
                pub_liability_document:
                  "$company_documentation?find:item.purpose === 'Reg Public liability insurance'",
                bbeee_cert_document:
                  "$company_documentation?find:item.purpose === 'Reg BBEEE certificate'",
                sars_tax_document:
                  "$company_documentation?find:item.purpose === 'Reg SARS Tax certificate'",
                proof_of_bank_account_document:
                  "$company_documentation?find:item.purpose === 'Reg Proof of bank account'",
                vehicle_document:
                  "$company_documentation?find:item.purpose === 'Reg Vehicle picture'",
                office_document:
                  "$company_documentation?find:item.purpose === 'Reg Office picture'",
                staff_uniform_document:
                  "$company_documentation?find:item.purpose === 'Reg Staff uniform picture'",
                config: {
                  style: {
                    display: 'grid',
                    gridAutoFlow: 'row',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'document-card',
                      name: 'co_reg_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Company registration',
                      purposeAsName: true,
                      isLandscape: true,
                      enableUpload: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      action: '/manage-sps/sps/edit/documentation',
                      list: 'False',
                      css: {
                        wrapper: {
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'pub_profile_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Public profile',
                      purposeAsName: true,
                      isLandscape: true,
                      enableUpload: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      action: '/manage-sps/sps/edit/documentation',
                      list: 'False',
                      css: {
                        wrapper: {
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'pub_liability_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Public liability insurance',
                      purposeAsName: true,
                      isLandscape: true,
                      enableUpload: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      action: '/manage-sps/sps/edit/documentation',
                      list: 'False',
                      css: {
                        wrapper: {
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'bbeee_cert_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg BBEEE certificate',
                      purposeAsName: true,
                      isLandscape: true,
                      enableUpload: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      action: '/manage-sps/sps/edit/documentation',
                      list: 'False',
                      css: {
                        wrapper: {
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'sars_tax_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg SARS Tax certificate',
                      purposeAsName: true,
                      isLandscape: true,
                      enableUpload: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      action: '/manage-sps/sps/edit/documentation',
                      list: 'False',
                      css: {
                        wrapper: {
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'proof_of_bank_account_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Proof of bank account',
                      purposeAsName: true,
                      isLandscape: true,
                      enableUpload: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      action: '/manage-sps/sps/edit/documentation',
                      list: 'False',
                      css: {
                        wrapper: {
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'vehicle_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Vehicle picture',
                      purposeAsName: true,
                      isLandscape: true,
                      enableUpload: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      action: '/manage-sps/sps/edit/documentation',
                      list: 'False',
                      css: {
                        wrapper: {
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'office_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Office picture',
                      purposeAsName: true,
                      isLandscape: true,
                      enableUpload: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      action: '/manage-sps/sps/edit/documentation',
                      list: 'False',
                      css: {
                        wrapper: {
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'staff_uniform_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Staff uniform picture',
                      purposeAsName: true,
                      isLandscape: true,
                      enableUpload: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      action: '/manage-sps/sps/edit/documentation',
                      list: 'False',
                      css: {
                        wrapper: {
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
              },
            },
          ],
          navs: [
            {
              label: 'Back To Field Ops Tool',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'navigate',
                  payload: ['/manage-sps/sps/list-view'],
                },
              ],
            },
            {
              label: 'Previous',
              position: 'left',
              toScreen: '../areas',
            },
          ],
        },
        // #endregion
        // #region SPS/EDIT/DIRECTORS SCREEN
        directors: {
          layout: {},
          fetchCalls: [
            {
              key: 'directors',
              method: 'POST',
              url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_directors',
              body: { sp_id: '$object_id' },
              slicePath: 'payload',
            },
          ],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Details',
                    path: '../details',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documentation',
                    path: '../documentation',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    text: 'Enter all director names',
                    options: {
                      format: 'heading',
                      type: 'page-heading',
                    },
                  },
                  {
                    text: 'Changing director names requires approval',
                    options: {
                      format: 'heading',
                      type: 'sub-heading',
                    },
                  },
                ],
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                paddingTop: '2rem',
                paddingBottom: '2rem',
              },
            },
            {
              component: 'DirectorsListWithFileView',
              props: {
                directors: '$directors',
                disableWhen: true,
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
          ],
          navs: [
            {
              label: 'Back To Field Ops Tool',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'navigate',
                  payload: ['/manage-sps/sps/list-view'],
                },
                {
                  type: 'clientAction',
                  action: 'clearStore',
                  payload: [
                    'sp_profile',
                    'company_documentation',
                    'documentsNotFound',
                    'derivedCompanies',
                    'derivedOperationalArea',
                    'directors',
                    'originalValues',
                  ],
                },
              ],
            },
            {
              label: 'Previous',
              position: 'left',
              toScreen: '../banking',
            },
            { label: 'Next', position: 'right', toScreen: '../contact' },
          ],
        },
        // #endregion
        // #region SPS/EDIT/BANKING SCREEN
        banking: {
          layout: {},
          fetchCalls: [],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Details',
                    path: '../details',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documentation',
                    path: '../documentation',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    text: 'Company banking details',
                    options: {
                      format: 'heading',
                      type: 'page-heading',
                    },
                  },
                  {
                    text: 'Changing bank details requires approval',
                    options: {
                      format: 'heading',
                      type: 'sub-heading',
                    },
                    icon: {
                      type: 'alert-diamond',
                      size: 24,
                      strokeWidth: '1px',
                      color: '#FF9800',
                      style: { margin: '0 0 0 4px' },
                    },
                    iconPosition: 'right',
                  },
                ],
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                paddingTop: '2rem',
                paddingBottom: '2rem',
              },
            },
            {
              component: 'FormBuilder',
              props: {
                proof_of_bank_account_document:
                  "$company_documentation?find:item.purpose === 'Reg Proof of bank account'",
                defaultValues: {
                  b_acc_holder: '$sp_profile.financial.b_acc_holder',
                  b_branch_name: '$sp_profile.financial.b_branch_name',
                  b_acc_type: '$sp_profile.financial.b_acc_type',
                  b_branch_code: '$sp_profile.financial.b_branch_code',
                  b_bank_name: '$sp_profile.financial.b_bank_name',
                  b_acc_no: '$sp_profile.financial.b_acc_no',
                  vat_no: '$sp_profile.financial.vat_no',
                  tax_no: '$sp_profile.financial.tax_no',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridAutoFlow: 'row',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                    marginBottom: '6rem',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'b_acc_holder',
                      label: 'Bank account holder name',
                      icon: 'alarm-clock',
                      position: 'right',
                      state: 'display-only',
                      disabledWhen: true,
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'single-select',
                      name: 'b_bank_name',
                      label: 'Bank name',
                      labelProp: 'name',
                      valueProp: 'id',
                      icon: 'alarm-clock',
                      dropdownScroll: true,
                      state: 'display-only',
                      disabledWhen: true,
                      options: {
                        source: 'store',
                        storeDataPath: 'sp_enums.banks',
                        labelProp: 'name',
                        valueProp: 'id',
                      },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'b_acc_no',
                      label: 'Bank account number',
                      icon: 'alarm-clock',
                      position: 'right',
                      state: 'display-only',
                      disabledWhen: true,
                      validation: {
                        pattern: {
                          value: validationRegex.bank_account_number.pattern,
                          message: validationRegex.bank_account_number.message,
                        },
                        minLength: {
                          value: 9,
                          message:
                            'Account number is invalid, please confirm banking details',
                        },
                        maxLength: {
                          value: 16,
                          message:
                            'Account number is invalid, please confirm banking details',
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'single-select',
                      name: 'b_acc_type',
                      label: 'Account type',
                      labelProp: 'name',
                      valueProp: 'id',
                      icon: 'alarm-clock',
                      state: 'display-only',
                      disabledWhen: true,
                      dropdownScroll: true,
                      validation: {
                        required: {
                          value: true,
                          message: 'Bank account type is required',
                        },
                      },
                      options: {
                        source: 'store',
                        storeDataPath: 'sp_enums.account_types',
                        labelProp: 'name',
                        valueProp: 'id',
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'b_branch_name',
                      label: 'Branch name',
                      icon: 'alarm-clock',
                      position: 'right',
                      state: 'display-only',
                      disabledWhen: true,
                      validation: {
                        required: {
                          value: true,
                          message: 'Branch name is required',
                        },
                        pattern: {
                          value: validationRegex.name.pattern,
                          message: validationRegex.name.message,
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'b_branch_code',
                      label: 'Branch code',
                      icon: 'alarm-clock',
                      state: 'display-only',
                      position: 'right',
                      disabledWhen: true,
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                        pattern: {
                          value: validationRegex.branch_code.pattern,
                          message: validationRegex.branch_code.message,
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'vat_no',
                      label: 'VAT registration number',
                      icon: 'alarm-clock',
                      position: 'right',
                      state: 'display-only',
                      disabledWhen: true,
                      validation: {
                        pattern: {
                          value: validationRegex.vat_number.pattern,
                          message: validationRegex.vat_number.message,
                        },
                      },
                      // TODO: How to close modal and refocus on control?
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'tax_no',
                      label: 'Tax number',
                      state: 'display-only',
                      disabledWhen: true,
                      validation: {
                        pattern: {
                          value: validationRegex.tax_number.pattern,
                          message: validationRegex.tax_number.message,
                        },
                      },
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'proof_of_bank_account_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Proof of bank account',
                      purposeAsName: true,
                      isLandscape: true,
                      enableUpload: false,
                      disabledWhen: true,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      action: '/manage-sps/sps/edit/documentation',
                      list: 'False',
                      css: {
                        wrapper: {
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
          ],
          navs: [
            {
              label: 'Back To Field Ops Tool',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'navigate',
                  payload: ['/manage-sps/sps/list-view'],
                },
                {
                  type: 'clientAction',
                  action: 'clearStore',
                  payload: [
                    'sp_profile',
                    'company_documentation',
                    'documentsNotFound',
                    'derivedCompanies',
                    'derivedOperationalArea',
                    'directors',
                    'originalValues',
                  ],
                },
              ],
            },
            {
              label: 'Previous',
              position: 'left',
              toScreen: '../details',
            },
            { label: 'Next', position: 'right', toScreen: '../directors' },
          ],
        },
        // #endregion
      },
      formOriginalValues: {
        // Orignals for diffing to find changed fields (from clientDataObject to store)
        'sp_profile.details.trading_as': 'trading_as',
        'sp_profile.details.bbeee': 'bbeee',
        'sp_profile.details.name': 'name',
        'sp_profile.details.co_reg': 'co_reg',
        'sp_profile.details.company_type': 'company_type',

        'sp_profile.financial.vat_registered': 'vat_registered',
        'sp_profile.financial.vat_no': 'vat_no',
        'sp_profile.financial.tax_no': 'tax_no',
        'sp_profile.financial.b_acc_holder': 'b_acc_holder',
        'sp_profile.financial.b_acc_type': 'b_acc_type',
        'sp_profile.financial.b_acc_no': 'b_acc_no',
        'sp_profile.financial.b_bank_name': 'b_bank_name',
        'sp_profile.financial.b_branch_name': 'b_branch_name',
        'sp_profile.financial.b_branch_code': 'b_branch_code',

        'sp_profile.address.contact_primary': 'contact_primary',
        'sp_profile.address.contact_secondary': 'contact_secondary',
        'sp_profile.address.contact_person': 'contact_person',
        'sp_profile.address.email_receiving': 'email_receiving',
        'sp_profile.address.physical_addr': 'physical_addr',
        'sp_profile.address.physical_city': 'physical_city',
        'sp_profile.address.physical_suburb': 'physical_suburb',
        'sp_profile.address.physical_code': 'physical_code',
        'sp_profile.address.postal_box': 'postal_box',
        'sp_profile.address.postal_city': 'postal_city',
        'sp_profile.address.postal_code': 'postal_code',
        'sp_profile.address.province': 'province',

        'sp_profile.skills': 'skills',
        // 'sp_profile.companies': 'companies',
        derivedCompanies: 'companies',
        'sp_profile.after_hours': 'after_hours',

        derivedOperationalArea: 'operational_area',

        'sp_profile.additional_identities.accredition': 'accredition',
        'sp_profile.additional_identities.mid': 'mid',
        'sp_profile.onboarding_state': 'onboarding_state',
      },
      // #endregion
      // #region FormTransformMapper configuration
      // formTransformMapper: {
      //   // Actual mapper to get final object shaped for server
      //   trading_as: 'details.trading_as',
      //   bbeee: 'details.bbeee',
      //   name: 'details.name',
      //   co_reg: 'details.co_reg',
      //   company_type: 'details.company_type',

      //   vat_registered: 'financial.vat_registered',
      //   vat_no: 'financial.vat_no',
      //   tax_no: 'financial.tax_no',
      //   b_acc_holder: 'financial.b_acc_holder',
      //   b_acc_type: 'financial.b_acc_type',
      //   b_acc_no: 'financial.b_acc_no',
      //   b_bank_name: 'financial.b_bank_name',
      //   b_branch_name: 'financial.b_branch_name',
      //   b_branch_code: 'financial.b_branch_code',

      //   contact_primary: 'address.contact_primary',
      //   contact_secondary: 'address.contact_secondary',
      //   contact_person: 'address.contact_person',
      //   email_receiving: 'address.email_receiving',
      //   physical_addr: 'address.physical_addr',
      //   physical_city: 'address.physical_city',
      //   physical_suburb: 'address.physical_suburb',
      //   physical_code: 'address.physical_code',
      //   postal_box: 'address.postal_box',
      //   postal_city: 'address.postal_city',
      //   postal_code: 'address.postal_code',
      //   province: 'address.province',

      //   skills: 'skills',
      //   companies: 'companies',
      //   after_hours: 'after_hours',

      //   operational_area: 'operational_area',

      //   mid: 'additional_identities.mid',
      //   accredition: 'additional_identities.accredition',
      //   onboarding_state: 'onboarding_state',
      // },
      // #endregion
      /*
       * SECTION: SPS/EDIT ACTION PANELS
       * Add all action panel item configurations here
       */
      // #region SPS/EDIT ACTION PANELS
      actionPanels: [
        // #region SCRATCH PAD
        {
          icon: 'clipboard',
          title: 'Scratch Pad', //?actionPanel=Messages--bell-02
          // fetchCalls: [],
          layout: {},
          onEnter: [],
          onLeave: [],
          fragments: [
            {
              component: 'ScratchPadView',
              layout: { marginLeft: '10px', marginRight: '10px' },
              props: {
                titlePlaceholder: 'Heading',
                icon: 'trash-01',
                iconHandler: (data: { heading: string; body: string }) =>
                  console.log(
                    'got data: Heading - ' +
                      data.heading +
                      ' Body - ' +
                      data.body
                  ),
                placeHolder: 'Text here...',
              },
            },
          ],
          actionLevel: 'bottomControls',
        },
        // #endregion
      ],
      // #endregion
    },
  },
};
