import {
  AppShell,
  desktopDark,
  useAppStore,
  useClientActionAsync,
  useSpaKeycloak,
} from '@4-sure/ui-platform';
import {
  Outlet,
  useLoaderData,
  useLocation,
  useNavigate,
} from 'react-router-dom';

const moduleTabs = [
  { name: 'Open Tasks', path: '../field-ops/tasks' },
  { name: 'Service Providers', path: './sps' },
];

export function ManageSpModule() {
  const data: any = useLoaderData();
  const auth = useAppStore((state: any) => state.auth);
  const { keycloak } = useSpaKeycloak();
  const navigate = useNavigate();
  const location = useLocation();
  const { callClientAction } = useClientActionAsync({
    keycloak,
    navigate,
    location,
  });

  return (
    <AppShell
      callClientAction={callClientAction}
      activeModuleName="Field Ops tool"
      keycloak={keycloak}
      username={auth?.preferred_username}
      email={auth?.email}
      moduleTabs={moduleTabs}
      theme={desktopDark}
      appConfig={data?.appConfig}
      clientDataObject={{ ...data?.fetchResultsObject }}
      persistKeys={['sp_enums', 'my_profile', 'fieldAccess']}
      enableCleanup={true}
    >
      <Outlet />
    </AppShell>
  );
}
