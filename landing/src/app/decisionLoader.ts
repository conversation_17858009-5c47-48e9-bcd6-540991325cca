import { TextConfig } from '@4-sure/ui-platform';
import Keycloak from 'keycloak-js';
import { redirect } from 'react-router-dom';

const userProfileStates: { [key: number]: string } = {
  1: 'New user incomplete',
  2: 'Evaluating',
  3: 'Approved',
  4: 'Rejected',
  5: 'Re-evaluating',
  6: 'Disabled',
  7: 'Pre-registered',
  8: 'Pre-existing',
};

const companyProfileStates: { [key: number]: string } = {
  1: 'New',
  2: 'Evaluating',
  3: 'Approved',
  4: 'Rejected',
  5: 'Re-evaluating',
  6: 'Disabled',
  7: 'Pre-registered',
  8: 'Pre-existing',
  9: 'Fixes required',
  10: 'Training',
  11: 'Suspended',
};

const availableUrls: { [key: string]: string } = {
  // 'builders': 'https://app.builders.com',
  // 'game': 'https://app.sp-staff.com',
  // 'multichoice': 'https://app.multi-choice.com',
  // 'sil': 'https://app.insurance-staff.com',
  pinggo: import.meta.env.VITE_PINGGOSP_URL,
  bettersure: import.meta.env.VITE_BETSP_URL,
  pinghoc: import.meta.env.VITE_PINGSP_URL,
  // pinghoc: import.meta.env.VITE_PINGHOC_URL,
  // pingsp: import.meta.env.VITE_PINGSP_URL,
  // 'sp-management': import.meta.env.VITE_SP_MANAGEMENT_URL,
  // 'fieldops': 'https://app.fieldops.com',
};

export async function decisionLoaderFactory(keycloak: Keycloak | undefined) {
  return async function decisionLoader({ request }: { request: Request }) {
    if (!keycloak) {
      return;
    }

    if (!keycloak.authenticated) {
      // Redirect to login if not authenticated
      return redirect(keycloak.createLoginUrl());
    }

    try {
      // Retrive user profile
      const userProfilePromise = fetch(
        `${
          import.meta.env.VITE_STAFF_SERVER
        }/api/v1/profile_actions/get_profile`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${keycloak.token}`,
          },
        }
      ).then((res) => res.json());
      // Retrive company profile
      const companyProfilePromise = fetch(
        `${import.meta.env.VITE_SP_SERVER}/api/v1/spaas_actions/get_sp`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${keycloak.token}`,
          },
        }
      ).then((res) => res.json());

      // Retrive all enums
      const allEnumsPromise = fetch(
        `${import.meta.env.VITE_SP_SERVER}/api/v1/spaas_actions/get_enum`,
        {
          method: 'POST',
          body: JSON.stringify({ enum: 'all' }),
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${keycloak.token}`,
          },
        }
      ).then((res) => res.json());

      const dataResponse = await Promise.all([
        userProfilePromise,
        companyProfilePromise,
        allEnumsPromise,
      ]);
      const data = dataResponse.map((r) => r.payload);

      const userProfile = data[0];
      const companyProfile = data[1];
      const enums = data[2];

      const companiesMap = enums.companies?.reduce(
        (acc: { [key: number]: any }, company: any) => {
          return { ...acc, [company.id]: company };
        },
        {}
      );

      const associatedCompanies = companyProfile.companies
        ?.map((c: any) => ({ ...companiesMap[c.client_id], active: c.active }))
        .map((c: any) => ({ ...c, url: availableUrls[c.short_name] }));
      //////////////////////////////// FLOWS RULES ////////////////////////////////

      // If both userProfile and companyProfile are approved, redirect to the platform
      if (
        userProfile.onboarding_state === 3 &&
        companyProfile.onboarding_state === 3
      ) {
        return {
          component: 'LandingPage',
          props: {
            staffType: userProfile.staff_type,
            headerText: 'Welcome to Connexa.',
            line1Text: ``,
            line2Text: '',
            displayPlatforms: true,
            associatedCompanies,
            staffRoles: userProfile.roles,
            description: {
              textItems: [
                {
                  text: 'You can access and edit your SP Company details and your own details using the profile button on the top right and manage your staff using the dropdown menu button on the top left.',
                  options: {
                    format: 'heading',
                    type: 'control-view-heading',
                    style: {
                      fontSize: 'clamp(16px, 80%, 31.2px)',
                    },
                  },
                },
              ] as TextConfig[],
            },
          },
        };
      }

      // If company profile onboarding status is Rejected, Show them message on landing page
      if (companyProfile.onboarding_state === 4) {
        return {
          component: 'LandingPage',
          props: {
            staffType: userProfile.staff_type,
            headerText: 'Your company application has been rejected.',
            line1Text: `Please contact 4-Sure`,
            line2Text: '010 110 9698',
            // line1Text: `Your Profile Status: ${
            //   userProfileStates[userProfile.onboarding_state]
            // }`,
            // line2Text: companyProfile.onboarding_state
            //   ? `Company Status: ${
            //       companyProfileStates[companyProfile.onboarding_state]
            //     }`
            //   : '',
            displayPlatforms: false,
            staffRoles: userProfile.roles,
            description: undefined,
          },
        };
      }

      // If company profile onboarding status is Training, Show them message on landing page
      if (companyProfile.onboarding_state === 10) {
        return {
          component: 'LandingPage',
          props: {
            staffType: userProfile.staff_type,
            headerText: 'Congratulations!\r\nYou are cleared for training.',
            line1Text: ``,
            line2Text: '',
            // line1Text: `Your Profile Status: ${
            //   userProfileStates[userProfile.onboarding_state]
            // }`,
            // line2Text: companyProfile.onboarding_state
            //   ? `Company Status: ${
            //       companyProfileStates[companyProfile.onboarding_state]
            //     }`
            //   : '',
            displayPlatforms: true,
            staffRoles: userProfile.roles,
            description: {
              textItems: [
                {
                  text: 'Kindly await an email from the Procurement team to arrange a training session with you.',
                  options: {
                    format: 'paragraph',
                    type: 'value',
                    style: {
                      fontSize: 'clamp(16px, 80%, 31.2px)',
                    },
                  },
                } as TextConfig,
                {
                  text: 'You can access your SP Company details and your own details using the profile button on the top right and manage your staff using the dropdown menu button on the top left.',
                  options: {
                    format: 'paragraph',
                    type: 'value',
                    style: {
                      fontSize: 'clamp(16px, 80%, 31.2px)',
                    },
                  },
                } as TextConfig,
              ] as TextConfig[],
            },
            hideClients: true,
          },
        };
      }

      // If company profile onboarding status is Disabled, Show them message on landing page
      if (companyProfile.onboarding_state === 6) {
        return {
          component: 'LandingPage',
          props: {
            staffType: userProfile.staff_type,
            headerText:
              'Your company has been deactivated. Please contact 4-SURE',
            line1Text: `Your Profile Status: ${
              userProfileStates[userProfile.onboarding_state]
            }`,
            line2Text: companyProfile.onboarding_state
              ? `Company Status: ${
                  companyProfileStates[companyProfile.onboarding_state]
                }`
              : '',
            displayPlatforms: false,
            staffRoles: userProfile.roles,
            description: undefined,
          },
        };
      }

      // If user profile is Evaluating and company profile is Approved, show them the platform
      if (
        userProfile.onboarding_state === 2 &&
        companyProfile.onboarding_state === 3
      ) {
        return {
          component: 'LandingPage',
          props: {
            staffType: userProfile.staff_type,
            headerText: 'Registration in review',
            line1Text: `Your Profile Status: ${
              userProfileStates[userProfile.onboarding_state]
            }`,
            line2Text: companyProfile.onboarding_state
              ? `Company Status: ${
                  companyProfileStates[companyProfile.onboarding_state]
                }`
              : '',
            displayPlatforms: false,
            staffRoles: userProfile.roles,
            description: undefined,
          },
        };
      }

      // If user profile and company profile are in evaluating state
      if (
        userProfile.onboarding_state === 2 &&
        companyProfile.onboarding_state === 2
      ) {
        return {
          component: 'LandingPage',
          props: {
            staffType: userProfile.staff_type,
            headerText: 'Thanks for registering',
            line1Text: `Your Profile Status: ${
              userProfileStates[userProfile.onboarding_state]
            }`,
            line2Text: companyProfile.onboarding_state
              ? `Company Status: ${
                  companyProfileStates[companyProfile.onboarding_state]
                }`
              : '',
            displayPlatforms: false,
            staffRoles: userProfile.roles,
            description: {
              textItems: [
                {
                  text: 'You will receive an email as soon as your account is active or if there are changes required.',
                  options: {
                    format: 'heading',
                    type: 'control-view-heading',
                    style: {
                      fontSize: 'clamp(16px, 80%, 31.2px)',
                    },
                  },
                } as TextConfig,
              ] as TextConfig[],
            },
          },
        };
      }

      // If user  profile onboarding status is Evaluating, Show them message on landing page
      if (userProfile.onboarding_state === 2) {
        return {
          component: 'LandingPage',
          props: {
            staffType: userProfile.staff_type,
            headerText: 'Registration in review',
            line1Text: `Your Profile Status: ${
              userProfileStates[userProfile.onboarding_state]
            }`,
            line2Text: companyProfile.onboarding_state
              ? `Company Status: ${
                  companyProfileStates[companyProfile.onboarding_state]
                }`
              : '',
            displayPlatforms: false,
            staffRoles: userProfile.roles,
            description: {
              textItems: [
                {
                  text: 'You will receive an email as soon as your account is active or if there are changes required.',
                  options: {
                    format: 'heading',
                    type: 'control-view-heading',
                    style: {
                      fontSize: 'clamp(16px, 80%, 31.2px)',
                    },
                  },
                } as TextConfig,
              ] as TextConfig[],
            },
          },
        };
      }

      // STAFF REJECTED
      if (userProfile.onboarding_state === 4) {
        return {
          component: 'LandingPage',
          props: {
            headerText:
              'Your application has been rejected. Please contact 4-SURE',
            staffType: userProfile.staff_type,
            line1Text: `Your Profile Status: ${
              userProfileStates[userProfile.onboarding_state]
            }`,
            line2Text: companyProfile.onboarding_state
              ? `Company Status: ${
                  companyProfileStates[companyProfile.onboarding_state]
                }`
              : '',
            displayPlatforms: false,
            staffRoles: userProfile.roles,
            description: undefined,
          },
        };
      }

      // STAFF DISABLED
      if (userProfile.onboarding_state === 6) {
        return {
          component: 'LandingPage',
          props: {
            staffType: userProfile.staff_type,
            headerText:
              'Your staff account has been disabled. Please contact 4-SURE',
            line1Text: `Your Profile Status: ${
              userProfileStates[userProfile.onboarding_state]
            }`,
            line2Text: companyProfile.onboarding_state
              ? `Company Status: ${
                  companyProfileStates[companyProfile.onboarding_state]
                }`
              : '',
            displayPlatforms: false,
            staffRoles: userProfile.roles,
            description: undefined,
          },
        };
      }

      // STAFF PRE-REGISTERED
      if (userProfile.onboarding_state === 5) {
        return {
          component: 'LandingPage',
          props: {
            staffType: userProfile.staff_type,
            headerText:
              'Your profile changes are in review. Continue with existing profile.',
            line1Text: `Your Profile Status: ${
              userProfileStates[userProfile.onboarding_state]
            }`,
            line2Text: companyProfile.onboarding_state
              ? `Company Status: ${
                  companyProfileStates[companyProfile.onboarding_state]
                }`
              : '',
            displayPlatforms: true,
            staffRoles: userProfile.roles,
            description: undefined,
          },
        };
      }

      // If userProfile is an approved account with fieldOps role
      if (
        userProfile.onboarding_state === 3 &&
        userProfile.roles.every((role: number) => [44].includes(role))
      ) {
        return {
          component: 'LandingPage',
          props: {
            staffType: userProfile.staff_type,
            headerText: 'Successful',
            line1Text: `Your Profile Status: ${
              userProfileStates[userProfile.onboarding_state]
            }`,
            line2Text: companyProfile.onboarding_state
              ? `Company Status: ${
                  companyProfileStates[companyProfile.onboarding_state]
                }`
              : '',
            displayPlatforms: true,
            associatedCompanies,
            staffRoles: userProfile.roles,
            description: undefined,
          },
        };
      }

      // if userProfile.active.reason === 'New user incomplete'
      if (
        userProfile.onboarding_state === 1 ||
        userProfile.onboarding_state === 7
      ) {
        return redirect(
          `${import.meta.env.VITE_REGISTRATION_APP_URL}/profile/update`
        );
      }
      // Any role that is not an SP role (team lead, sp mgr, scheduler)

      // FieldOps (our tenants, )

      // Staff sp staff, insurance staff
      // if staff_type:1
      // onboarding status..

      //   // If no apps are accessible, return the data to show a warning
      //   return { userProfile: { ...userProfile, roles }, accessibleApps };
      // return { profiles: profiles || [], accessibleApps: [{ url: 'https://app1.example.com', requiredRoles: ['role1', 'role2'] }] };
      return {
        component: 'LandingPage',
        props: {
          staffType: userProfile.staff_type,
          headerText: 'Registration in review',
          line1Text: `Your Profile Status: ${
            userProfileStates[userProfile.onboarding_state]
          }`,
          line2Text: companyProfile.onboarding_state
            ? `Company Status: ${
                companyProfileStates[companyProfile.onboarding_state]
              }`
            : '',
          displayPlatforms: false,
          staffRoles: userProfile.roles,
          description: undefined,
        },
      };
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw new Response('Error fetching user data', { status: 500 });
    }
  };
}
