import { AppConfig } from '@4-sure/ui-platform';
import { settingsConfig } from './pages/settings/SettingsConfig';
import { tasksConfig } from './pages/tasks/TasksConfig';
import { teamsConfig } from './pages/teams/TeamsConfig';
import { workflowConfig } from './pages/workflow/WorkflowConfig';

export async function getAppConfig(isAdmin?: boolean, tasksAlert?: boolean) {
  let mutatedConfig: AppConfig = {
    ...appConfig,
    toolBarConfig: {
      modulesList: [],
      menuItems: [
        {
          label: 'Personal Settings',
          menuItems: [
            {
              label: 'My details',
              icon: 'bell-02',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'navigate',
                  payload: ['/settings/personal/my-profile'],
                },
              ],
            },
            {
              type: 'divider',
            },
            {
              label: 'Logout',
              icon: 'bell-02',
              onClick: [
                { type: 'clientAction', action: 'clearStore', payload: [] },
                {
                  type: 'clientAction',
                  action: 'log',
                  payload: ['Logging out....'],
                },
                { type: 'clientAction', action: 'logout', payload: [] },
              ],
            },
          ],
        },
      ],
      email: '',
      username: '',
    },
    teams: {
      defaultTeamState: '', // Provide a default state object
      teamStates: {}, // Provide a team states object
    },
  };

  if (isAdmin) {
    mutatedConfig = {
      ...appConfig,
      toolBarConfig: {
        ...appConfig.toolBarConfig,
        modulesList: [
          {
            name: 'Team Management',
            path: '/teams',
            icon: 'team-icon',
            alert: tasksAlert,
            active: true,
          },
        ],
        alertIndicator: tasksAlert,
      },
    };
  }
  return mutatedConfig;
}

export const appConfig: AppConfig = {
  toolBarConfig: {
    modulesList: [
      // { name: 'Team Management', path: '/teams' },
      // { name: 'Tasks', path: `/tasks` },
      // { name: 'Workflow', path: `${import.meta.env.VITE_CC_URL}/workflow` },
      // { name: 'Workflow', path: '/workflow' },
      // {name: 'Dashboard', path: '/dashboard' },
      // {name: 'Working Capital', path: '/working-capital' },
    ],
    buttonText: '',
    username: '',
    email: '',
    image: '',
    menuItems: [
      {
        label: 'Personal Settings',
        menuItems: [
          {
            label: 'My details',
            icon: 'bell-02',
            onClick: [
              {
                type: 'clientAction',
                action: 'navigate',
                payload: ['/settings/personal/my-profile'],
              },
            ],
          },
        ],
      },
      {
        label: 'Company Settings',
        menuItems: [
          {
            label: 'Company',
            icon: 'bell-02',
            onClick: [
              {
                type: 'clientAction',
                action: 'navigate',
                payload: ['/settings/company/company'],
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            label: 'Logout',
            icon: 'bell-02',
            onClick: [
              { type: 'clientAction', action: 'clearStore', payload: [] },
              {
                type: 'clientAction',
                action: 'log',
                payload: ['Logging out....'],
              },
              { type: 'clientAction', action: 'logout', payload: [] },
            ],
          },
        ],
      },
    ],
  },
  fetchCalls: [
    {
      key: 'staff_enums',
      method: 'POST',
      url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/get_enum',
      body: { enum: 'all' },
      slicePath: 'payload',
    },
    {
      key: 'staffFieldAccess',
      method: 'POST',
      url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/staff_field_access',
      body: {},
      slicePath: 'payload',
    },
  ],

  onEnter: [],
  onLeave: [],
  workflow: workflowConfig,
  teams: teamsConfig,
  settings: settingsConfig,
  tasks: tasksConfig,
  states: {
    // 98: {
    //     title: {
    //       template: ''
    //         // template: 'Payment preview completed {toUpperCase(todo.completed)} with userId of {foo.userId} count is {toUpperCase(todo.title)} with count {count(todo.title)}',
    //     },
    //     // fetchCalls: [
    //     //         {key: 'todos', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos'},
    //     //         {key: 'todo', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos/1'},
    //     //         {key: 'foo', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos/2'},
    //     // ],
    //     // onEnter: [
    //     //     // { type: 'clientAction', action: 'log', payload: 'testing something'},
    //     //     // { type: 'serverAction', action: 'doServerWork', payload: { foo: 'bar' } },
    //     //     // { type: 'clientAction', action: 'storeInLocalStorage', payload: { key: 'Country', value: '@form:country' } },
    //     //     // { type: 'clientAction', action: 'storeInLocalStorage', payload: { key: 'test', value: 'Kofi' } },
    //     // ],
    //     // onLeave: [],
    //     defaultScreen: 'preview',
    //     screens: {
    //         // screen
    //         preview: {
    //             layout: {
    //                 backgroundColor: 'coral',
    //             },
    //             onEnter: [
    //                 // { type: 'clientAction', action: 'storeInLocalStorage', payload: { key: 'bar', value: '@form:screenInput' } },
    //             ],
    //             onLeave: [],
    //             fetchCalls: [],
    //             fragments: [
    //                 {
    //                     component: 'TestComponentA',
    //                     props: {},
    //                     layout: {
    //                         color: 'green',
    //                         "gridColumn": "1 / 2",
    //                         "gridRow": "1 / 2"
    //                     }
    //                 },
    //                 {
    //                     component: 'TestComponentB',
    //                     props: {},
    //                     layout: {
    //                         "gridColumn": "1 / 2",
    //                         "gridRow": "1 / 2"
    //                     }
    //                 },
    //                 {
    //                     component: 'TestComponentB',
    //                     props: {},
    //                     layout: {
    //                         "gridColumn": "1 / 2",
    //                         "gridRow": "1 / 2"
    //                     }
    //                 },
    //                 {
    //                     component: 'TestComponentB',
    //                     props: {},
    //                     layout: {
    //                         "gridColumn": "1 / 2",
    //                         "gridRow": "1 / 2"
    //                     }
    //                 },
    //                 {
    //                     component: 'FormBuilder',
    //                     props: {
    //                         config: {
    //                             style: {},
    //                             controls: [
    //                                 {
    //                                     name: 'first_name',
    //                                     label: 'First Name'
    //                                 }
    //                             ]
    //                         }
    //                     },
    //                     layout: {},
    //                 },
    //                 {
    //                     component: 'FormBuilder',
    //                     props: {
    //                         config: {
    //                             style: {},
    //                             controls: [
    //                                 {
    //                                     name: 'first_name',
    //                                     label: 'First Name'
    //                                 }
    //                             ]
    //                         }
    //                     },
    //                     layout: {},
    //                 }
    //             ],
    //             navs: [
    //                 { label: 'Go Back', position: 'left' },
    //                 { label: 'Summary', toScreen: 'summary', position: 'left' },
    //             ],
    //         },
    //         // end screen
    //           // screen
    //           summary: {
    //             layout: {
    //                 backgroundColor: 'thistle'
    //             },
    //             onEnter: [
    //                 // { type: 'clientAction', action: 'storeInLocalStorage', payload: { key: 'bar', value: '@form:screenInput' } },
    //             ],
    //             onLeave: [],
    //             fetchCalls: [],
    //             fragments: [
    //                 {
    //                     component: 'TestComponentA',
    //                     props: {},
    //                     layout: {
    //                         "gridColumn": "1 / 2",
    //                         "gridRow": "1 / 2"
    //                     }
    //                 },
    //                 {
    //                     component: 'TestComponentB',
    //                     props: {},
    //                     layout: {
    //                         marginBottom: '50px',
    //                         "gridColumn": "1 / 2",
    //                         "gridRow": "1 / 2"
    //                     }
    //                 },
    //             ],
    //             navs: [
    //                 { label: 'Go Back To..', toScreen: 'preview', position: 'left' },
    //                 // { label: 'Next', onClick: [], position: 'left' },
    //             ],
    //         },
    //         // end screen
    //     },
    //     actionPanels:  [
    //         {
    //           icon: 'bell-02',
    //           title: 'Messages', //?actionPanel=Messages--bell-02
    //           // fetchCalls: [],
    //           layout: {},
    //           onEnter: [],
    //           onLeave: [],
    //           fragments: [
    //             {
    //               component: 'NoteCardList',
    //               layout: {
    //                 marginTop: '20px',
    //                 marginLeft: '10px',
    //                 marginRight: '10px',
    //               },
    //               props: {
    //                 notes: [
    //                   {
    //                     title: 'Find Invoice',
    //                     date: '05/10/23',
    //                     time: '11:42',
    //                     content:
    //                       'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
    //                   },
    //                   {
    //                     title: 'Find Invoice',
    //                     date: '05/10/23',
    //                     time: '11:42',
    //                     content:
    //                       'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
    //                   },
    //                   {
    //                     title: 'Find Invoice',
    //                     date: '05/10/23',
    //                     time: '11:42',
    //                     content:
    //                       'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
    //                   },
    //                 ],
    //               },
    //             },
    //           ],
    //           actionLevel: 'bottomControls',
    //         },
    //         {
    //           icon: 'trash-01',
    //           title: 'Scratch Pad', //?actionPanel=Messages--bell-02
    //           // fetchCalls: [],
    //           layout: {},
    //           onEnter: [],
    //           onLeave: [],
    //           fragments: [
    //             {
    //               component: 'NoteCardList',
    //               layout: {
    //                 marginTop: '20px',
    //                 marginBottom: '20px',
    //                 marginLeft: '10px',
    //                 marginRight: '10px',
    //               },
    //               props: {
    //                 notes: [
    //                   {
    //                     title: 'Find Invoice',
    //                     date: '05/10/23',
    //                     time: '11:42',
    //                     content:
    //                       'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
    //                   },
    //                   {
    //                     title: 'Find Invoice',
    //                     date: '05/10/23',
    //                     time: '11:42',
    //                     content:
    //                       'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
    //                   },
    //                   {
    //                     title: 'Find Invoice',
    //                     date: '05/10/23',
    //                     time: '11:42',
    //                     content:
    //                       'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
    //                   },
    //                 ],
    //               },
    //             },
    //           ],
    //           actionLevel: 'bottomControls',
    //         },
    //       ],
    // }
  },
};
