import {
  AppShell,
  desktopDark,
  useAppStore,
  useClientActionAsync,
  useSpaKeycloak,
} from '@4-sure/ui-platform';
import { useEffect, useMemo, useState } from 'react';
import {
  Outlet,
  useLoaderData,
  useLocation,
  useNavigate,
} from 'react-router-dom';
import { appConfig, getAppConfig } from '../../app-confg';

const rightsideTabs = [
  { name: 'Personal Settings', path: './personal' },
  { name: 'Company Settings', path: './company' },
];

export function SettingsModule() {
  const data: any = useLoaderData();
  const auth = useAppStore((state: any) => state.auth);
  const profile = useAppStore((state: any) => state.my_profile_picture);
  const isAdmin = useAppStore((state: any) => state.isAdmin);
  const pending_admin_tasks = useAppStore(
    (state: any) => state.pending_admin_tasks || 0
  );

  const { keycloak } = useSpaKeycloak();
  const navigate = useNavigate();
  const location = useLocation();
  const { callClientAction } = useClientActionAsync({
    keycloak,
    navigate,
    location,
  });
  const [newAppConfig, setNewAppConfig] = useState<any>(null);

  useEffect(() => {
    const fetchAppConfig = async () => {
      const config = await getAppConfig(isAdmin, !!pending_admin_tasks);
      setNewAppConfig(config);
    };

    fetchAppConfig();
  }, [isAdmin, pending_admin_tasks]);
  const updatedAppConfig = useMemo(
    () => ({
      ...data?.appConfig,
      ...newAppConfig,
    }),
    [data?.appConfig, newAppConfig]
  );

  console.log('settings module!');

  return (
    <AppShell
      callClientAction={callClientAction}
      activeModuleName=""
      keycloak={keycloak}
      username={auth?.preferred_username}
      email={auth?.email}
      moduleTabs={[]}
      rightsideTabs={isAdmin ? rightsideTabs : rightsideTabs.slice(0, 1)}
      theme={desktopDark}
      appConfig={updatedAppConfig}
      clientDataObject={{ ...data?.fetchResultsObject }}
      image={profile?.file}
      alertIndicator={pending_admin_tasks || 0}
      persistKeys={[
        'my_profile',
        'my_profile_picture',
        'sp_profile',
        'isAdmin',
        'fieldAccess',
        'pending_admin_tasks',
        'sp_profile_onboarding_state',
        'staffFieldAccess',
      ]}
    >
      <Outlet />
    </AppShell>
  );
}
