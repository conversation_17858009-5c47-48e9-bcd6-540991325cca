import { StateConfig } from '@4-sure/ui-platform';

export const MANAGE_TEAM_WORKFLOW_STATE = {
  title: { template: 'Helllllo world' },
  fetchCalls: [
    {
      key: 'staff_members',
      method: 'POST',
      body: {},
      url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/get_staff_members',
      slicePath: 'payload',
    },
    {
      key: 'sp_profile_onboarding_state',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp',
      body: {},
      slicePath: 'payload.onboarding_state',
    },
  ],
  defaultScreen: 'list-view',
  screens: {
    // screen
    'list-view': {
      layout: {},
      fragments: [
        {
          component: 'ButtonRow',
          layout: {
            display: 'grid',
            gridAutoFlow: 'column',
            justifyContent: 'right',
            marginBottom: '1rem',
          },
          props: {
            buttons: [
              {
                btnValue: 'ADD MEMBER',
                onClick: [
                  {
                    type: 'clientAction',
                    action: 'navigate',
                    payload: ['/teams/manage-team/add/add-member'],
                  },
                ],
                iconRight: 'plus',
              },
            ],
          },
        },
        {
          component: 'TeamMemberCardList',
          props: {
            data: '$staff_members',
            config: [
              {
                type: 'linkColumn',
                showAvatar: true,
                showTitleText: true,
                showStatusIndicator: false,
                showRating: true,
                titleKey: 'full_name',
              },
              {
                type: 'textColumn',
                titleKey: 'roles',
              },
              {
                type: 'textColumn',
                titleKey: 'contact_number',
              },
              {
                type: 'textColumn',
                titleKey: 'email_address',
              },
            ],
            linkUrl: '/teams/manage-team/profile/member-profile',
            linkUrlParams: [{ key: 'staff_id', value: 'staff_id' }],
          },
          layout: {},
        },
      ],
      navs: [],
    },
    // end screen
  },
  actionPanels: [
    // Scratch Pad
    {
      icon: 'clipboard',
      title: 'Scratch Pad', //?actionPanel=Messages--bell-02
      layout: {},
      onEnter: [],
      onLeave: [],
      fragments: [
        {
          component: 'ScratchPadView',
          layout: { marginLeft: '10px', marginRight: '10px' },
          props: {
            titlePlaceholder: 'Heading',
            icon: 'trash-01',
            iconHandler: (data: { heading: string; body: string }) =>
              console.log(
                'got data: Heading - ' + data.heading + ' Body - ' + data.body
              ),
            placeHolder: 'Text here...',
          },
        },
      ],
      actionLevel: 'bottomControls',
    },
  ],
} satisfies StateConfig;
