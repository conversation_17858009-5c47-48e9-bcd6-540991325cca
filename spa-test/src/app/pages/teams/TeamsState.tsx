import {
  StateShell,
  useAppStore,
  useClientActionAsync,
  useSpaKeycloak,
} from '@4-sure/ui-platform';
import { useEffect } from 'react';
import {
  Outlet,
  useLoaderData,
  useLocation,
  useNavigate,
} from 'react-router-dom';

// State
export function TeamsState() {
  const data: any = useLoaderData();
  const navigate = useNavigate();
  const isAdmin = useAppStore((state: any) => state?.isAdmin);
  const { keycloak } = useSpaKeycloak();
  const location = useLocation();
  const { callClientAction } = useClientActionAsync({
    keycloak,
    navigate,
    location,
  });
  useEffect(() => {
    if (!isAdmin) {
      navigate('/settings');
    }
  }, [isAdmin]);

  return (
    <StateShell
      callClientAction={callClientAction || (() => {})}
      stateConfig={data?.stateConfig || {}}
      clientDataObject={{ ...data?.fetchResultsObject }}
      persistKeys={[
        'my_profile',
        'my_profile_picture',
        'sp_profile',
        'isAdmin',
        'fieldAccess',
        'pending_admin_tasks',
        'sp_profile_onboarding_state',
        'staffFieldAccess',
      ]}
    >
      <Outlet />
    </StateShell>
  );
}
