import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { SplashScreenLoader } from '../Components';
import { useAppStore } from '../Engine';
import { makeFetchCall } from '../Utilities';
import { useSpaKeycloak } from './SpaKeycloakAuthProvider';

interface Props {
  userProfileUrl: string;
  children: any;
  userProfilePicUrl?: string;
}

interface IContext {
  currentUserProfile?: { [key: string]: any };
  currentUserProfilePic?: { [key: string]: any };
  isAdmin?: boolean;
  validUser?: boolean;
  isLoading?: boolean;
}

const AuthorisationContext = createContext<IContext>(null!);

export function AuthorisationProvider({
  userProfileUrl,
  userProfilePicUrl,
  children,
}: Props): ReactNode {
  const [currentUserProfile, setCurrentUserProfile] = useState<{
    [key: string]: any;
  }>();
  const [currentUserProfilePic, setCurrentUserProfilePic] =
    useState<Record<string, any>>();
  const [currentUserIsAdmin, setCurrentUserIsAdmin] = useState<boolean>();
  const [spOnboardingState, setSpOnboardingState] = useState<number>();
  const [isLoading, setIsLoading] = useState(false);

  const { keycloak } = useSpaKeycloak();
  const setState = useAppStore((state: any) => state.setState);

  const hasFetchedData = useRef(false);

  const fetchUserData = useCallback(
    async (token: string) => {
      if (!token || hasFetchedData.current) return;

      setIsLoading(true);
      hasFetchedData.current = true;

      try {
        const [profileResponse, companyResponse] = await Promise.all([
          makeFetchCall(userProfileUrl, { token }),
          makeFetchCall(
            `${
              (import.meta as any).env.VITE_SP_SERVER
            }/api/v1/spaas_actions/get_sp`,
            { token }
          ),
        ]);

        const profile = (profileResponse as any).payload;
        const company = (companyResponse as any).payload;

        const ADMIN_ROLE = 43;
        const isAdmin = profile?.roles?.some(
          (role: number) => role === ADMIN_ROLE
        );

        setCurrentUserProfile(profile);
        setCurrentUserIsAdmin(isAdmin);
        setSpOnboardingState(company?.onboarding_state);

        let profilePic = null;
        if (userProfilePicUrl && profile?.profile_pic) {
          try {
            const proPicResponse = await makeFetchCall(
              userProfilePicUrl,
              { token },
              { file_id: profile.profile_pic }
            );
            profilePic = (proPicResponse as any).payload;
            setCurrentUserProfilePic(profilePic);
          } catch (error) {
            console.warn('Failed to fetch profile picture:', error);
          }
        }

        setState({
          my_profile: profile,
          my_profile_picture: profilePic,
          sp_profile: company,
          isAdmin,
        });
      } catch (error) {
        console.error('Failed to fetch user data:', error);
        hasFetchedData.current = false;
      } finally {
        setIsLoading(false);
      }
    },
    [userProfileUrl, userProfilePicUrl, setState]
  );

  useEffect(() => {
    hasFetchedData.current = false;

    if (keycloak?.token) {
      fetchUserData(keycloak.token);
    } else if (!keycloak) {
      console.warn('Not logged in');
    }
  }, [keycloak, fetchUserData]);

  const validUser = useMemo(() => {
    const STAFF_ROLES = [43, 9, 10];
    const validRole = (currentUserProfile as any)?.roles?.every(
      (role: number) => STAFF_ROLES.includes(role)
    );
    const ALLOWED_SP_ONBOARDING_STATES = [3, 10];
    const validCompanyOnboardingState = spOnboardingState
      ? ALLOWED_SP_ONBOARDING_STATES.includes(spOnboardingState)
      : false;
    if (currentUserProfile && spOnboardingState) {
      return validRole && validCompanyOnboardingState;
    }
  }, [currentUserProfile, spOnboardingState]);

  const contextValue = useMemo(
    () => ({
      currentUserProfile,
      currentUserProfilePic,
      isAdmin: currentUserIsAdmin,
      validUser,
      isLoading,
    }),
    [
      currentUserProfile,
      currentUserProfilePic,
      currentUserIsAdmin,
      validUser,
      isLoading,
    ]
  );

  return (
    <AuthorisationContext.Provider value={contextValue}>
      {keycloak ? children : <SplashScreenLoader transparent={true} />}
    </AuthorisationContext.Provider>
  );
}

export function useAuthorisation() {
  const context = useContext<IContext>(AuthorisationContext);
  if (!context) {
    throw new Error(
      'useAuthorisation must be used within an AuthorisationProvider'
    );
  }
  return context;
}
