/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from "react";
import { ActionPanelAvailableJobsContainer } from "./ActionPanelAvailableJobsContainer";
import { APAvailableJobCard } from "../../Cards/APAvailableJobCard/APAvailableJobCard";
import styled from "styled-components";
import { useAvailableJobsStore } from "../../../Engine/hooks/useAvailableJobsStore";
import { PaginationBar } from '../../Controllers/Pagination';
import {
  PaginationPageCount,
  useListPagination2,
} from '../../Controllers/Pagination/PaginationPageCount/PaginationPageCount';
import { useCheckJobAvailabilityAndGetJobs } from "../../../Hooks";
import { JobResponse } from "../../../db";
import { db } from "../../../services/IndexedDbService";
import { ElementaryThemedMap } from '../../Maps/ElementaryThemedMap/ElementaryThemedMap';
import { Icon } from "../../Icons/Icon";
import { useActionPanelStore } from "../hooks/useActionPanel";

import { useAppStore } from "../../../Engine/useAppStore";

interface IActionPanelAvailableJobsProps {
    // children: any;
    width?: string;
    background?: string;
    availableJobs?: any[];
    filteredJobs?: any[];
    token?: string | null;
    showAvailableList?: boolean;
    skills?: Array<{id: number | string, name: string, active: boolean, mid: number | string}>;
    shouldShowAvailableList?: boolean;
  }

const PaginationWrapper = styled.div`
  width: 100%;
  margin-top: 10px;
`;


  const ActionPanelAvailableJobsList = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  height: 100%;
  display: grid;
  gap: 10px;
  grid-template-rows: auto 1fr auto;
  box-sizing: border-box;
  // text-align: center;
  height: auto;
  
`;
  const ActionPanelAvailableJobsListItems = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-rows: repeat(5, minmax(auto, auto));
  grid-auto-rows: 0;
  box-sizing: border-box;
  // text-align: center;
  height: auto;
  
`;
  const ActionPanelAvailableJobsListButton = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-rows: repeat(5, minmax(auto, auto));
  grid-auto-rows: 0;
  box-sizing: border-box;
  align-self: end;
  height: auto;
  
`;
  const ActionPanelAvailableJobDetail = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-rows: auto 1fr auto;
  box-sizing: border-box;
  height: 100%;
  background: black;
  gap: 16px;
  padding: 16px;
  
`;

const Button = styled.button`
    width: 124px;
    padding: 8px;
    background: ${(props) => props?.theme.ColorsButtonColorModuleNavigationDefault};
    color: ${(props) => props?.theme.ColorsTypographyPrimary};
    box-shadow: 0px 0px 3px ${(props) => props.theme.ColorsInputsInverse};
    border-radius: 104px;
    cursor: pointer;
`;

const ButtonContainer = styled.div`
  display: grid;
  grid-auto-flow: column;
  gap: ${(props) => props.theme.GapMd};
  justify-content: center;
  align-items: center;
`;

const StyledHeading = styled.h4`
  color: ${(props) => props.theme.ColorsTypographySecondary};
`;

const IconWrapper = styled.div<{ iconType: 'check-circle' | 'x-xircle' | 'minus-xircle' | 'map-01' }>`
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    transition: stroke 0.2s ease-in-out;
  }

  &:hover svg {
    stroke: ${props => {
      switch (props.iconType) {
        case 'check-circle':
          return props.theme.ColorsCardColorCalendarSuccess || 'green';
        case 'x-xircle':
          return props.theme.ColorsCardColorCalendarError || 'red';
        default:
          return 'inherit';
      }
    }};
  }
`;

// Helper function to parse location string
const parseLocation = (locationString: string) => {
  try {
    const [lat, lng] = locationString.split(',').map(coord => parseFloat(coord.trim()));
    return { lat, lng };
  } catch (error) {
    return { lat: 0, lng: 0 }; // Default coordinates if parsing fails
  }
};

const formatDateTime = (dateString: string, appointmentName: string) => {
    try {
        const date = new Date(dateString);

        // check if date is valid
        if(isNaN(date.getTime())) {
            return 'Invalid date';

            }

        const month = date.toLocaleString('en-ZA', { month: 'long' });
        const day = date.getDate();
        const time = date.toLocaleTimeString('en-ZA', { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: false 
        })
        return `${month} ${day} ${appointmentName.toLowerCase()} ${time}`;
    } catch (error) {
        return 'Date formatting error';
    }
    
}
export const ActionPanelAvailableJobs: React.FC<IActionPanelAvailableJobsProps> = ({
    availableJobs,
    skills,
    ...props
}) => {
    const auth = useAppStore((state: any) => state.auth);
    const env = useAppStore((state: any) => state.env);
    const staffMember = auth.staffMember;
    
    const [listView, setListView] = useState(true);
    const [selectedJobId, setSelectedJobId] = useState<number | null>(null);
    // const [animatingJobId, setAnimatingJobId] = useState<number | string | null>(null);
    const { 
      filteredJobs, 
      // allInfo, 
      // jobsAwarded, 
      // jobsLost, 
      // isOpen,
      setFilteredJobs,
      setJobResponses 
  } = useAvailableJobsStore();
    // const [jobs, setJobs]= useState<IAvailableJob[]>(availableJobs || [])

    const { pages, currentPage, pageItems, setCurrentPage, ...rest } = useListPagination2({
        items: filteredJobs || [],
        itemsPerPage: 4,  // Show 4 items per page
      });

      // Only show jobs for the current page
    const visibleJobs = pageItems;
   
    const { registerJobInterest } = useCheckJobAvailabilityAndGetJobs({baseUrl: env.VITE_API_BASE_URL, staffMember: staffMember}  );

    const handleIgnoreJob = (jobId: number | string) => {
      registerInterestAndRemoveJob(jobId, 0);
      setListView(true); // Return to list view after handling the job
    };
    
    const handleDeclineJob = (jobId: number | string) => {
      registerInterestAndRemoveJob(jobId, -1);
      setListView(true); // Return to list view after handling the job
    };
    
    const handleAcceptJob = (jobId: number | string) => {
      registerInterestAndRemoveJob(jobId, 1);
      setListView(true); // Return to list view after handling the job
    };
    const registerInterestAndRemoveJob = async (job_id: number | string, interest: number, staffId?: string | number) => {
      try {
          await registerJobInterest(job_id, interest, 170);
          
          // Update filtered jobs in Zustand store
          setFilteredJobs(filteredJobs.filter(job => job.id !== job_id));
          
          // Update pagination if needed
          const newTotalPages = Math.ceil((filteredJobs.length - 1) / 4);
          if (currentPage > newTotalPages && setCurrentPage) {
              setCurrentPage(Math.max(1, newTotalPages));
          }
          
          setListView(true);
      } catch (error) {
          console.error('Error registering job interest:', error);
      }
  };

    // const handleIgnoreAll = async () => {
    //   if(!availableJobs) return;
    //   const timeOfResponse = new Date().toISOString();

    //   try {
    //     // Create job responses for all available jobs
    //     const jobResponses: JobResponse[] = availableJobs.map(job => ({
    //       job_id: Number(job.id),
    //       interested: 0,
    //       spm: 170,
    //       time_of_response: timeOfResponse,
    //     }))

    //     // store job responses in the IndexedDB database
    //     await Promise.all(jobResponses.map(response => db.job_responses.add(response)));

    //     // Register job interest with the API for each job
    //     await Promise.all(jobResponses.map((response: JobResponse) => registerJobInterest(response.job_id, Number(response.interested), response.spm)));

    //     // Update Zustand store with the new job responses
    //     const allJobResponses = await db.job_responses.toArray();
    //     useAvailableJobsStore.setState(state => ({
    //       ...state,
    //       filteredJobs: [],
    //       jobResponses: allJobResponses,
    //     }));

    //      // Clear the local jobs state
    //      handleBackToList();
    //     setJobs([]);

    //     // If using pagination, reset to first page
    //     if (setCurrentPage) {
    //       setCurrentPage(1);
    //     }
    //     // Close the action panel
    //     useActionPanelStore.setState({ isOverlayVisible: false});

    //     // Close the pane or update view
    //     setListView(true);
    //   } catch (error) {
    //     console.error('Error storing job responses', error);
    //   }
    // }
    const handleIgnoreAll = async () => {
      if(!filteredJobs.length) return;
      const timeOfResponse = new Date().toISOString();

      try {
          const jobResponses: JobResponse[] = filteredJobs.map(job => ({
              job_id: Number(job.id),
              interested: 0,
              spm: 170,
              time_of_response: timeOfResponse,
          }));

          await Promise.all(jobResponses.map(response => db.job_responses.add(response)));
          await Promise.all(jobResponses.map((response: JobResponse) => 
              registerJobInterest(response.job_id, Number(response.interested), response.spm)
          ));

          const allJobResponses = await db.job_responses.toArray();
          
          // Update Zustand store
          setJobResponses(allJobResponses);
          setFilteredJobs([]);

          handleBackToList();
          
          if (setCurrentPage) {
              setCurrentPage(1);
          }
          
          useActionPanelStore.setState({ isOverlayVisible: false });
          setListView(true);
      } catch (error) {
          console.error('Error storing job responses', error);
      }
  };

    const handleJobClick = (jobId: number) => {
        setSelectedJobId(jobId);
        setListView(false);
    };

    const handleBackToList = () => {
        setListView(true);
        setSelectedJobId(null);
    };

    const getSkillNameById = (skillId: string | number | undefined, skills: Array<{id: number, name: string}> = []) => {
        if (!skills || !skillId) {
            console.warn('getSkillNameById (parent): Missing skills array or skillId', { skills, skillId });
            return 'Unknown Skill';
        }
        
        // Convert skillId to number for comparison
        const numericSkillId = Number(skillId);
        
        // Check if conversion was successful
        if (isNaN(numericSkillId)) {
            console.warn('getSkillNameById (parent): Invalid skillId conversion', { skillId, numericSkillId });
            return 'Unknown Skill';
        }
        
        // Try exact match first
        let skill = skills.find(s => s.id === numericSkillId);
        
        // If not found, try string comparison as fallback
        if (!skill) {
            skill = skills.find(s => String(s.id) === String(skillId));
        }
        
        if (!skill) {
            console.warn('getSkillNameById (parent): Skill not found', { 
                skillId, 
                numericSkillId, 
                availableSkillIds: skills.map(s => s.id),
                availableSkillIdTypes: skills.map(s => typeof s.id),
                skillsLength: skills.length 
            });
            return 'Unknown Skill';
        }
        
        return skill.name;
    };

    return (
       
        <ActionPanelAvailableJobsContainer>
            {
                listView ? (
                  <>
                  <ActionPanelAvailableJobsList>  
                <div>Available Jobs</div>   
                <ActionPanelAvailableJobsListItems>
                {visibleJobs?.map((job:any) => {
                        // Debug skills transformation
                        const transformedSkills = skills?.map(skill => ({
                          id: Number(skill.id),
                          name: skill.name
                      })) || [];
                        
                        console.log('APAvailableJobCard skills debug:', {
                            originalSkills: skills,
                            transformedSkills,
                            jobSkill: job.skill,
                            jobSkillType: typeof job.skill
                        });
                        
                        return <APAvailableJobCard 
                                    job={job} 
                                    key={job.id}
                                    onClick={() => handleJobClick(job.id)}
                                    skills={transformedSkills}
                                    >
                                    
                                </APAvailableJobCard>;
                    })}
                    {/* {availableJobs?.map((job:any) => (
                        <APAvailableJobCard job={job}></APAvailableJobCard>
                    ))} */}
                    
                </ActionPanelAvailableJobsListItems>

            </ActionPanelAvailableJobsList>
            <PaginationWrapper>
        <PaginationBar
          paginationItems={
            <PaginationPageCount
              pages={pages}
              currentPage={currentPage}
              {...rest}
            />
          }
        />
      </PaginationWrapper>
            <ActionPanelAvailableJobsListButton>
                <Button onClick={handleIgnoreAll}>IGNORE ALL</Button>
            </ActionPanelAvailableJobsListButton>
                  </>

                ) : (
                    <ActionPanelAvailableJobDetail>
                        <h3>Job Details</h3>
                        <div>
                            <div style={{marginBottom:'10px'}}>{formatDateTime(availableJobs?.find(job => job.id === selectedJobId)?.appointment?.range_start, availableJobs?.find(job => job.id === selectedJobId)?.appointment?.appointment_name)}</div>
                            <div style={{marginBottom:'10px'}}>{availableJobs?.find(job => job.id === selectedJobId)?.suburb}</div>
                            <div style={{marginBottom:'10px'}}>
                                {(() => {
                                    const selectedJob = availableJobs?.find(job => job.id === selectedJobId);
                                    const transformedSkills = skills?.map(skill => ({ id: Number(skill.id), name: skill.name }));
                                    
                                    console.log('Job detail skills debug:', {
                                        selectedJob,
                                        selectedJobSkill: selectedJob?.skill,
                                        selectedJobSkillType: typeof selectedJob?.skill,
                                        transformedSkills
                                    });
                                    
                                    return getSkillNameById(selectedJob?.skill, transformedSkills);
                                })()}
                            </div>
                            <StyledHeading>What Matters</StyledHeading>
                            <StyledHeading>Job location by suburb</StyledHeading>
                            <div style={{marginBottom:'10px', height: '200px'}}>
                              <ElementaryThemedMap
                                joblocation={parseLocation(availableJobs?.find(job => job.id === selectedJobId)?.location || '0,0')}
                                theme="dark"
                                size="full"
                              />
                            </div>
                          <ButtonContainer>
                            <IconWrapper iconType="minus-xircle">
                              <Icon
                                  type="minus-xircle"
                                  size={30}
                                  onClick={() =>selectedJobId && handleIgnoreJob(selectedJobId)}
                                  style={{ cursor: 'pointer' }}
                                />
                                <div onClick={() => selectedJobId && handleIgnoreJob(selectedJobId)}>Ignore</div>
                            </IconWrapper>
                            <IconWrapper iconType="x-xircle">
                              <Icon
                                type="x-xircle"
                                size={30}
                                onClick={() => selectedJobId && handleDeclineJob(selectedJobId)}
                                style={{ cursor: 'pointer' }}
                              />
                              <div onClick={() => selectedJobId && handleDeclineJob(selectedJobId)}>Decline</div>
                            </IconWrapper>
                            <IconWrapper iconType="check-circle">
                              <Icon
                                  type="check-circle"
                                  size={30}
                                  onClick={() => selectedJobId && handleAcceptJob(selectedJobId)}
                                  style={{ cursor: 'pointer' }}
                                />
                                <div onClick={() => selectedJobId && handleAcceptJob(selectedJobId)}>Accept</div>
                            </IconWrapper>
                          </ButtonContainer>

                        </div>
                        <div>
                            <Button onClick={handleBackToList}>Back to List</Button>
                        </div>
                    </ActionPanelAvailableJobDetail>
                )
            }
        </ActionPanelAvailableJobsContainer>
    )
}