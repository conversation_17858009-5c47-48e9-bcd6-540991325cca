import Keycloak from 'keycloak-js';
import { ComponentPropsWithoutRef, useEffect, useMemo } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { ActionConfig } from '../../../Engine';
import { useAsyncLoaderStore } from '../../../Engine/hooks/useAsyncLoaderStore';
import { ActivationButton, ActivationStates } from '../../Inputs';
import { ScratchPadWrapper } from '../ScratchPadWrapper/ScratchPadWrapper';

/**
 * Type definition for a Tenant object that extends ActivationButton props
 * @typedef {Object} Tenant
 * @extends {Omit<ComponentPropsWithoutRef<typeof ActivationButton>, 'currentState' | 'bottomDivider' | 'onChange'>}
 * @property {string} tenant_id - Unique identifier for the tenant
 * @property {string} [active] - Optional activation status of the tenant
 * @property {string} [client_id] - Optional client identifier
 * @property {string} [sp_id] - Optional service provider identifier
 */
type Tenant = Omit<
  ComponentPropsWithoutRef<typeof ActivationButton>,
  'currentState' | 'bottomDivider' | 'onChange'
> & { tenant_id: string; active?: string; client_id?: string; sp_id?: string };

/**
 * Props interface for the FOTenantApproval component
 * @template T
 * @typedef {Object} Props
 * @property {string} title - Title displayed at the top of the component
 * @property {T[]} tenants - Array of tenant objects to be displayed
 * @property {boolean} [divider=true] - Whether to show dividers between tenants
 * @property {(state?: { [key: string]: ActivationStates }) => void} [onChange] - Callback when tenant states change
 * @property {boolean} [submitOnChange=false] - Whether to auto-submit on state changes
 * @property {boolean} [isStory=false] - Whether component is being used in Storybook
 * @property {Object} [store] - Store object containing company and tenant data
 * @property {string} store.id - Company identifier
 * @property {Object} store.additional - Additional company information
 * @property {Object.<string, ActivationStates>} store.additional.tenant_approval - Tenant approval states
 */
type Props<T extends Tenant> = {
  title: string;
  tenants: T[];
  keycloak?: Keycloak;
  divider?: boolean;
  onChange?: (state?: {
    [key: string]: ActivationStates;
  }) => void | ActionConfig[];
  submitOnChange?: boolean;
  isStory?: boolean;
  store?: any;
  _callClientAction?: (config: ActionConfig[]) => void;
};

/**
 * FOTenantApproval is a component for managing tenant activation states within a company.
 * It provides a checklist interface where users can activate or deactivate tenants and
 * optionally submit these changes to a server.
 *
 * Features:
 * - Displays a list of tenants with activation toggle buttons
 * - Integrates with React Hook Form for form state management
 * - Optional automatic submission of changes
 * - Supports asynchronous loading states
 * - Keycloak integration for authentication (disabled in Storybook mode)
 *
 * @template T
 * @component
 * @param {Props<T>} props - Component props
 *
 * @example
 * ```tsx
 * // Basic usage
 * <FOTenantApproval
 *   title="Manage Tenants"
 *   tenants={[
 *     { tenant_id: "1", name: "Tenant 1" },
 *     { tenant_id: "2", name: "Tenant 2", active: "true" }
 *   ]}
 *   onChange={(states) => console.log('Tenant states:', states)}
 * />
 * ```
 *
 * @example
 * ```tsx
 * // With auto-submit and store integration
 * <FOTenantApproval
 *   title="Tenant Approval"
 *   tenants={tenants}
 *   submitOnChange={true}
 *   store={{
 *     id: "company-1",
 *     additional: {
 *       tenant_approval: {
 *         "tenant-1": "ACTIVE",
 *         "tenant-2": "INACTIVE"
 *       }
 *     }
 *   }}
 * />
 * ```
 *
 * @returns {JSX.Element} The rendered FOTenantApproval component
 */
export function FOTenantApproval<T extends Tenant>({
  title,
  tenants,
  divider = true,
  onChange: onChangeEventHandler,
  submitOnChange,
  isStory = false,
  store,
  _callClientAction,
  keycloak: keycloakProp,
}: Props<T>) {
  const { getValues, setValue } = useFormContext();
  // const { keycloak } = isStory ? { keycloak: null } : useSpaKeycloak();
  const keycloak = keycloakProp ? keycloakProp : null;
  const [asyncLoading, setAsyncLoading] = useAsyncLoaderStore((state: any) => [
    state.asyncLoading,
    state.setAsyncLoading,
  ]);

  /**
   * Computes the available tenants and their activation states.
   *
   * This function iterates over the tenants array and builds an object where
   * each key is a tenant's name and its value is the activation state of the tenant.
   * The activation state is determined by checking the form context values for the
   * tenant's name, or if not available, finding the corresponding tenant in the
   * store's profile and retrieving its active state.
   *
   * @returns {Record<string, string | undefined>} An object mapping tenant names to their
   * activation states.
   */
  const availableTenants = useMemo(
    () =>
      tenants.reduce((acc, cur) => {
        return {
          ...acc,
          [cur.name]:
            (getValues() as Record<string, string>)[cur.name] ??
            store?.sp_profile?.companies?.find(
              (sub: any) => sub?.client_id === cur.client_id
            )?.active,
        };
      }, {}),
    [tenants, getValues, store?.sp_profile?.companies]
  );
  // console.log({
  //   availableTenants: availableTenants(),
  //   tenant_subscriptions_evaluated: !Object.values(availableTenants()).includes(
  //     'EVALUATING' || 3
  //   ),
  // });

  useEffect(() => {
    setValue(
      'tenant_subscriptions_evaluated',
      !Object.values(availableTenants).includes('EVALUATING')
    );
  }, []);

  const clientSubActiveStates = store?.sp_enums?.client_sub_active_states;
  const clientSubscriptions = store?.sp_profile?.companies;

  // async function submitChange(data: { key: string; value: ActivationStates }) {
  //   if (!isStory) {
  //     try {
  //       setIsLoading(true);
  //       const response = await fetch(
  //         `${import.meta.env.VITE_SP_SERVER}/api/v1/spaas_actions/update_sp`,
  //         {
  //           method: 'POST',
  //           body: JSON.stringify({
  //             sp_id: store?.id,
  //             details: {
  //               additional: {
  //                 ...companyDetailsAdditionalStore.additional,
  //                 tenant_approval: {
  //                   ...persistData,
  //                   [data.key]: data.value,
  //                 },
  //               },
  //             },
  //           }),
  //           headers: {
  //             'Content-Type': 'application/json',
  //             Authorization: `Bearer ${keycloak?.token}`,
  //           },
  //         }
  //       );

  //       if (!response.ok) {
  //         throw new Error('Failed to submit change');
  //       }

  //       const res = await response.json();
  //     } catch (error) {
  //       console.error('Error submitting change: ', error);
  //     }
  //   } else {
  //     console.log('submit: ', { [data.key]: data.value });
  //   }
  //   setIsLoading(false);
  // }

  async function submit(data: { id: string; value: number; name: string }) {
    console.log('submit: ', data);
    if (!isStory) {
      try {
        const update_sp_subscription = `${
          (import.meta as any).env.VITE_SP_SERVER
        }/api/v1/spaas_actions/update_client_subscription`;
        setAsyncLoading(true);

        const response = await fetch(update_sp_subscription, {
          method: 'POST',
          body: JSON.stringify({
            sp_id: store?.sp_profile?.details?.id,
            client_id: data.id,
            active: data.value,
          }),
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${keycloak?.token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to submit change');
        }

        const res = await response.json();
        const newActiveState = res?.payload?.[0].active;
        setValue(data.name, newActiveState);
        setValue(
          'tenant_subscriptions_evaluated',
          (() => {
            const tenantSubs = {
              ...availableTenants,
              [data.name]: newActiveState,
            };
            return !Object.values(tenantSubs).includes('EVALUATING');
          })()
        );
      } catch (error) {
        console.error('Error submitting change: ', error);
      }
    } else {
      console.log('submit: ', { [data.id]: data.value });
      console.log('submit payload: ', {
        sp_id: store?.sp_profile?.details?.id,
        client_id: data.id,
        active: data.value,
      });
    }
    setAsyncLoading(false);
  }
  const handleChange = async (
    tenant: Tenant,
    state: ActivationStates,
    onSelectState: (state?: { [key: string]: ActivationStates }) => void,
    value: { [key: string]: ActivationStates }
  ) => {
    console.log({ tenant, state, value });
    const newClientSubState = clientSubActiveStates?.find(
      (clientSubActiveState: { id: number; name: string }) =>
        clientSubActiveState.name === state
    ).id;
    // console.log('new selected state: ', newClientSubState);
    const tenantName = tenant.name;
    const tenantStatuses = value;

    const updatedStatuses = {
      ...tenantStatuses,
      [tenantName]: state,
    };

    if (onSelectState) {
      if (typeof onSelectState === 'function') {
        onSelectState(updatedStatuses);
      }
      if (typeof onChangeEventHandler === 'function') {
        onChangeEventHandler(updatedStatuses);
      }
      if (
        _callClientAction &&
        typeof onChangeEventHandler !== 'function' &&
        Array.isArray(onChangeEventHandler)
      ) {
        const setState = store?.setState ? store.setState : undefined;
        if (!setState) return;
        setState((state: any) => {
          type ClientSubscriptionUpdatePayload = {
            sp_id: string;
            client_id: string;
            active: number;
          };
          const clientSubscriptionUpdates: ClientSubscriptionUpdatePayload[] = [
            ...(state?.['client_subscription_updates'] || []),
            {
              sp_id: store?.sp_profile?.details?.id,
              client_id: tenant.tenant_id,
              active: newClientSubState,
            },
          ];
          return { client_subscription_updates: clientSubscriptionUpdates };
        });
        await _callClientAction(onChangeEventHandler as ActionConfig[]);
      }
    }

    // submitOnChange && submitChange({ key: tenantName, value: state });
    submitOnChange &&
      submit({
        id: tenant.tenant_id,
        value: newClientSubState,
        name: tenantName,
      });
  };
  return (
    <ScratchPadWrapper title={title}>
      <>
        {tenants.map((tenant: Tenant, idx: number) => {
          const current = clientSubscriptions?.find(
            (sub: any) => sub?.client_id === tenant.tenant_id
          )?.active;
          // console.log(tenant.name, current);
          const currentValue = clientSubActiveStates?.find(
            (state: any) => state.id === current
          )?.name;
          return (
            <Controller
              key={idx}
              control={tenant.control}
              name={tenant.name}
              rules={tenant.rules}
              defaultValue={currentValue}
              render={({ field: { value, onChange } }) => (
                <ActivationButton
                  {...tenant}
                  key={idx}
                  bottomDivider={divider}
                  onChange={(state) => {
                    handleChange(tenant, state, onChange, value);
                  }}
                  currentState={value}
                />
              )}
            />
          );
        })}
      </>
    </ScratchPadWrapper>
  );
}
