import { ReactNode } from 'react';
import styled from 'styled-components';
import { additionalFontStyling } from '../../../Utilities';
import { Divider } from '../../Dividers';
import { Heading } from '../../Heading/Heading';

/**
 * Props interface for the ScratchPadWrapper component
 * @typedef {Object} Props
 * @property {ReactNode} children - Content to be displayed within the wrapper
 * @property {string} title - Title to display at the top of the wrapper
 * @property {boolean} [useFontTransformer] - Whether to use font transformation for the title
 */
type Props = {
  children: ReactNode;
  title: string;
  useFontTransformer?: boolean;
  transparentBackground?: boolean;
};

/**
 * Container component using Grid layout for the ScratchPad content
 *
 * @styled-component
 * @component
 *
 * Features:
 * - Grid-based layout with auto-sized header and flexible content area
 * - Theme-aware colors and spacing
 * - Centered content alignment
 * - Consistent gap spacing between elements
 * - Rounded corners for modern appearance
 */
const Container = styled.div<{ transparentBackground?: boolean }>`
  color: ${(props) => props.theme.ColorsStrokesGrey};
  width: 100%;
  display: grid;
  grid-template-rows: auto 1fr;
  justify-items: center;
  gap: ${(props) => props?.theme.GapSm};
  background-color: ${(props) =>
    props?.transparentBackground
      ? 'transparent'
      : props?.theme.ColorsBackgroundModule};
  border-radius: ${(props) => props?.theme?.RadiusXs};
`;

/**
 * Wrapper component that provides padding and layout for the ScratchPad content
 *
 * @styled-component
 * @component
 *
 * Features:
 * - Responsive width calculation with theme-based spacing
 * - Consistent padding on all sides
 * - Grid-based content layout
 * - Support for absolute positioning of child elements
 * - Transparent background for layering flexibility
 */
const Wrapper = styled.div`
  padding: ${(props) => `${props.theme.SpacingLg} ${props.theme.SpacingMd}`};
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  gap: ${(props) => props?.theme.GapSm};
  position: relative;
  background-color: transparent;
  width: ${(props) => `calc(100% - ${props.theme.SpacingXl})`};
`;

/**
 * Styled divider component for visual separation between header and content
 *
 * @styled-component
 * @component
 *
 * Features:
 * - Full-width horizontal rule
 * - Consistent 2px height
 * - Absolute positioning for precise placement
 * - Theme-aware styling
 */
const StyledDivider = styled(Divider)`
  height: 2px;
  width: 100%;
  position: absolute;
  top: 0;
`;

/**
 * Styled title component with customizable font properties
 *
 * @styled-component
 * @component
 *
 * Features:
 * - Custom Inter font family with fallback
 * - Theme-aware font sizing and colors
 * - Precise line height control
 * - Optional font transformation support
 * - Consistent top padding
 */
const Title = styled(Heading)<{ useFontTransformer?: boolean }>`
  font-family: ${(props) => props.theme.FontFamiliesInter}, sans-serif;
  font-size: ${(props) => props.theme.FontSize3}px;
  line-height: 16.94px;
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  padding-top: ${(props) => props.theme.SpacingXs};
  ${(props) =>
    additionalFontStyling(
      props.theme.FontWeightsInter6,
      props.useFontTransformer
    )}
`;

/**
 * ScratchPadWrapper is a component that provides a consistent layout and styling
 * wrapper for ScratchPad content. It implements a modern, clean design with a
 * title, divider, and flexible content area.
 *
 * Features:
 * - Consistent layout with title and content sections
 * - Theme-aware styling for colors, spacing, and typography
 * - Flexible content area that adapts to children
 * - Visual separation between title and content
 * - Optional font transformation for the title
 *
 * @component
 * @param {Props} props - Component props
 *
 * @example
 * ```tsx
 * // Basic usage
 * <ScratchPadWrapper title="Notes">
 *   <p>Your content here</p>
 * </ScratchPadWrapper>
 * ```
 *
 * @example
 * ```tsx
 * // With font transformation
 * <ScratchPadWrapper
 *   title="Important Notes"
 *   useFontTransformer={true}
 * >
 *   <div>
 *     <h2>Section 1</h2>
 *     <p>Content for section 1</p>
 *   </div>
 * </ScratchPadWrapper>
 * ```
 *
 * @returns {JSX.Element} The rendered ScratchPadWrapper component
 */
export function ScratchPadWrapper(props: Props) {
  return (
    <Container transparentBackground={props.transparentBackground}>
      <Title>{props.title}</Title>
      <Wrapper>
        <StyledDivider size="fullwidth" background="primary" />
        {props.children}
      </Wrapper>
    </Container>
  );
}
