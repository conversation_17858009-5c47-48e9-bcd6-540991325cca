import React, { FunctionComponent } from 'react';
import styled from 'styled-components';
import { SpacingMd } from '../../../themes/tokens/outputs/desktop-light';

interface ButtonLinkProps {
  label: string;
  altText: string;
  iconSrc?: React.ReactNode;
  onClick?: () => void;
  href?: string;
  disabled?: boolean;
  download?: false | string;
  className?: string;
}

const ButtonLinkContainer = styled.div<{ disabled?: boolean }>`
  width: 202px;
  height: 115px;
  position: relative;
  border-radius: 4px;
  background-color: #3f4142;
  border: ${({ disabled }) => (disabled ? 'none' : '2px solid #f8f8f8')};
  box-sizing: border-box;
  overflow: hidden;
  display: grid;
  grid-template-rows: auto;
  justify-content: center;
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
  opacity: ${({ disabled }) => (disabled ? 0.5 : 1)};
  pointer-events: ${({ disabled }) => (disabled ? 'none' : 'auto')};
  padding: ${(props) => `${props.theme.SpacingXl} 0`};

  div {
    color: #f8f8f8;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    text-align: center;
    margin-bottom: 1.5rem;
  }

  &:hover {
    background-color: ${({ disabled }) => (disabled ? '#3f4142' : '#555')};
  }
`;

const DownloadLinkContainer = styled.a<{ disabled?: boolean }>`
  width: 202px;
  height: 115px;
  position: relative;
  border-radius: 4px;
  background-color: #3f4142;
  border: ${({ disabled }) => (disabled ? 'none' : '2px solid #f8f8f8')};
  box-sizing: border-box;
  overflow: hidden;
  display: grid;
  grid-template-rows: auto;
  justify-content: center;
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
  opacity: ${({ disabled }) => (disabled ? 0.5 : 1)};
  pointer-events: ${({ disabled }) => (disabled ? 'none' : 'auto')};
  padding: ${(props) => `${props.theme.SpacingMd} 0`};
  place-items: center;

  div {
    color: #f8f8f8;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    text-align: center;
  }

  &:link {
    text-decoration: none;
  }

  &:visited {
    text-decoration: none;
  }

  &:active {
    text-decoration: none;
  }

  &:hover {
    text-decoration: none;
    background-color: ${({ disabled }) => (disabled ? '#3f4142' : '#555')};
  }
`;

const IconImage = styled.img`
  object-fit: cover;
`;

/**
 * A button that can be used to navigate to a given URL or call a provided
 * onClick function. If the button is disabled, it will not respond to clicks.
 *
 * @param {string} label - The label to display on the button.
 * @param {React.ReactNode|string} iconSrc - The source of the icon to display on the button.
 *  If a string is provided, it is assumed to be the source of the image. If a ReactNode
 *  is provided, it is rendered directly.
 * @param {string} altText - The alt text to use for the icon.
 * @param {Function} [onClick] - If provided, this function is called when the button is
 *  clicked.
 * @param {string} [href] - If provided, the button will navigate to this URL when clicked.
 * @param {boolean} [disabled=false] - If true, the button will not respond to clicks.
 */

export const ButtonLink: FunctionComponent<ButtonLinkProps> = ({
  label,
  iconSrc,
  altText,
  onClick,
  href,
  disabled = false,
  download = false,
  className,
}) => {
  const handleClick = () => {
    if (!disabled) {
      if (onClick) {
        onClick();
      } else if (href) {
        window.open(href, '_blank');
      }
    }
  };

  return (
    <div>
      {download ? (
        <DownloadLinkContainer
          className={className}
          href={`${href}`}
          disabled={disabled}
          download={download}
        >
          {typeof iconSrc === 'string' ? (
            <IconImage src={iconSrc} alt={altText} />
          ) : (
            iconSrc
          )}
          <div>{label}</div>
        </DownloadLinkContainer>
      ) : (
        <ButtonLinkContainer
          onClick={handleClick}
          disabled={disabled}
          className={className}
        >
          {typeof iconSrc === 'string' ? (
            <IconImage src={iconSrc} alt={altText} />
          ) : (
            iconSrc
          )}
          {label && <div>{label}</div>}
        </ButtonLinkContainer>
      )}
    </div>
  );
};
