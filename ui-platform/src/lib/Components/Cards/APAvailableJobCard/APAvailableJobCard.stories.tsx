import type { Meta, StoryObj } from '@storybook/react';
import { APAvailableJobCard } from './APAvailableJobCard';

const meta: Meta<typeof APAvailableJobCard> = {
  component: APAvailableJobCard,
  title: 'Components/Cards/APAvailableJobCard',
};

export default meta;
type Story = StoryObj<typeof APAvailableJobCard>;

const mockJob = {
  id: 1201,
  appointments: [],
  appointment: {
    id: 1201,
    after_hours: false,
    appointment_name: 'Before',
    appointment_type_id: '5',
    range_start: '2025-02-25T16:00:00',
    range_end: '2025-02-25T16:00:00',
  },
  location: '',
  mid: '',
  address: '12 Windmill Road, Albertaville, 12345',
  skill: 1,
  customer: '<PERSON>',
  cellnumber: '0801234567',
  area: 'Gauteng',
  claim_type: 'Plumbing',
  suburb: 'Roodepoort',
};

const mockSkills = [
  { id: 1, name: 'Plumbing' },
  { id: 2, name: 'Electrical' },
  { id: 3, name: 'Carpentry' },
  { id: 4, name: 'Painting' },
  { id: 5, name: 'Cleaning' },
];

export const Default: Story = {
  args: {
    job: mockJob,
    skills: mockSkills,
    onClick: (job) => console.log('Card clicked:', job),
  },
};

export const WithStringSkillId: Story = {
  args: {
    job: {
      ...mockJob,
      skill: '2',
    },
    skills: mockSkills,
    onClick: (job) => console.log('Card clicked:', job),
  },
};

export const UnknownSkill: Story = {
  args: {
    job: {
      ...mockJob,
      skill: 999,
    },
    skills: mockSkills,
    onClick: (job) => console.log('Card clicked:', job),
  },
};

export const NoSkills: Story = {
  args: {
    job: mockJob,
    skills: [],
    onClick: (job) => console.log('Card clicked:', job),
  },
};