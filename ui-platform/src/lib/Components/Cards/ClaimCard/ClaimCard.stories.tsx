import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { ClaimCard } from './ClaimCard';
import { MappedClaim } from '../../../Engine/models/mapped-claim';

const meta: Meta<typeof ClaimCard> = {
  component: ClaimCard,
  title: 'Components/Cards/ClaimCard',
  tags: ['autodocs'],
};
export default meta;
type Story = StoryObj<typeof ClaimCard>;

const menuItems = [
    {
        icon: 'user-01',
        label: 'Assign to Team',
        path: '/',
      },
      {
        icon: 'eye-off',
        label: 'Assign to Team',
        path: '/',
      },
      {
        icon: 'x-xircle',
        label: 'Assign to Team',
        path: '/',
      },
  ];  

const claimWithMultipleJobs: MappedClaim = {
  type: 'Geyser Only',
  customer: 'King <PERSON>',
  spAllocated: '2: SP Allocated',
  timeRemaining: '1d 03h 25m',
  "id": 9898,
  "applicant": {
      "first_name": "<PERSON><PERSON>",
      "surname": "<PERSON><PERSON>",
      "id_number": "158786932",
      "claimantpoliceynum": "None",
      "bondnumber": "--",
      "generated": "from maven",
      "local_file": null,
      "created_by": "auto"
  },
  "application_creator": null,
  "application_date": "2025-04-29T12:50:46.027033",
  "state": 16,
  "state_change_date": "2025-04-29T12:50:46.027034",
  "location": 11883,
  "modified_date": "2025-04-29T13:06:01.496459",
  "mid": "KK25/tr432",
  "sub_section": 1,
  "jobs": [
      {
          "id": 8765,
          "appointment": null,
          "note_count": 0,
          "unread_note_count": null,
          "claim_type_id": 37,
          "assessor_name": "",
          "lat": "-26.100407000",
          "long": "28.001390000",
          "claim": {
              "id": 9898,
              "mid": "KK25/tr432",
              "is_cat": false,
              "cat_code": null,
              "applicant": {
                  "first_name": "Macc",
                  "surname": "Macc"
              },
              "property_city": "RANDBURG",
              "property_complex": "",
              "property_complex_block": "",
              "property_complex_unit_number": "",
              "property_street_name": "",
              "property_street_number": "",
              "property_suburb": "FERNDALE,"
          },
          "source": "kingprice",
          "source_id": 1,
          "source_key": "Cla",
          "suburb": "FERNDALE, ",
          "address": ", 251 251 OAK AVENUE, RANDBURG",
          "postal_code": "2194",
          "claim_value": null,
          "mid": null,
          "ping_count": 1,
          "token": "awFL5WuddftL9gLvL2YMA4QEJy6ywKKqWCxaK73yTJGAfTStkCkr7h",
          "valid_job": null,
          "updated": "2025-04-28T11:00:00.000000",
          "on_site": null,
          "distance": null,
          "job_creator": null,
          "skill_name": "Incident Management",
          "skill": 51,
          "sp": {
            "name": "4-Sure PTY LTD"
          },
          "team_leader": null,
          "area": 1,
          "state": 45,
          "state_display_text": "Job Cancelled",
          "final_status": "All Done",
          "supplier_type": 1,
          "forced_location": null,
          "assessor": null,
          "authorizer": null,
          "location": null
      },
      {
          "id": 8766,
          "appointment": {
              "id": 11017,
              "job": 8766,
              "state": 1,
              "range_start": "2025-05-01T08:00:00",
              "range_end": null,
              "appointment_type": 4,
              "reason": null
          },
          "note_count": 0,
          "unread_note_count": null,
          "claim_type_id": 38,
          "assessor_name": "",
          "lat": "-26.100407000",
          "long": "28.001390000",
          "claim": {
              "id": 9898,
              "mid": "KK25/tr432",
              "is_cat": false,
              "cat_code": null,
              "applicant": {
                  "first_name": "Macc",
                  "surname": "Macc"
              },
              "property_city": "RANDBURG",
              "property_complex": "",
              "property_complex_block": "",
              "property_complex_unit_number": "",
              "property_street_name": "",
              "property_street_number": "",
              "property_suburb": "FERNDALE,"
          },
          "source": "multichoice",
          "source_id": 1,
          "source_key": "Cla",
          "suburb": "FERNDALE, ",
          "address": ", 251 251 OAK AVENUE, RANDBURG",
          "postal_code": "2194",
          "claim_value": null,
          "mid": null,
          "ping_count": 1,
          "token": "awFL5WuddftL9gLvL2YMA4QEJy6ywKKqWCxaK73yTJGAfTStkCkr7h",
          "valid_job": null,
          "updated": "2025-04-29T13:07:00.000000",
          "on_site": null,
          "distance": null,
          "job_creator": null,
          "skill_name": "Electric Geyser",
          "skill": 52,
          "sp": {
            "name": "Baatseba's SP"
          },
          "team_leader": null,
          "area": 1,
          "state": 288,
          "state_display_text": "Review SP Repudiation",
          "final_status": "Review SP Repudiation",
          "supplier_type": 1,
          "forced_location": null,
          "assessor": null,
          "authorizer": null,
          "location": null
      }
  ],
  "note_count": 0,
  "repudiation": null,
  "deceased": [],
  "form_start": "2025-04-29T12:50:46.027023",
  "assessor": null,
  "cat_code": null,
  "claim_type_id": 37,
  "private_banking": 0,
  "unread_note_count": null,
  "source": "multichoice",
  "source_id": 1,
  "province": 1,
  "source_key": "Cla"
};



export const ClaimCardWithMultipleJobs: Story = {
  args: {
   claim: claimWithMultipleJobs,
   ClaimLinkRouter: ({ children }: {children: any}) => children,
   JobLinkRouter: ({ children }: {children: any}) => children,
   displayLogo: true,
   menuItems: [
     { icon: 'pencil-01', label: 'Edit', path: '/edit' },
     { icon: 'chevron-right', label: 'Next', path: '/next' },
     { icon: 'user-circle', label: 'Profile', path: '/profile' },
   ],
  }
 };


