// external module imports
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
// internal module imports
import { TemplateLiteralLogger } from '../../../Utilities';
import { IconButton } from '../../Buttons/IconButton/IconButton';
import { TextButton } from '../../Buttons/TextButton/TextButton';
import { Heading } from '../../Heading/Heading';
import { Icon, IconTypes } from '../../Icons';
import DocumentPreview from '../../Inputs/DocumentPreview/DocumentPreview';

export interface DocumentViewerProps {
  src: string;
  purpose: string;
  fileName: string;
  mediaType: 'image' | 'pdf' | 'other';
  onClose: () => void;
  created: string;
  icon?: IconTypes;
  onDownload?: () => void;
  onOpenInTab?: () => void;
  downloadable?: boolean;
  meta?: Record<string, any>;
}

const AlertMask = styled.div`
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  /* padding: 10px; */
  gap: ${(props) => props.theme.GapXl};
  background-color: ${(props) => props.theme.ColorsOverlayDynamicPanel};
  display: grid;
  place-items: center;
  z-index: 10;
  overflow: auto;
`;

const Container = styled.div`
  /* width: auto;
  height: auto; */
  /* width: 790px;
  height: 883px; */
  padding: ${(props) => props.theme.SpacingXl};
  gap: ${(props) => props.theme.GapXl};
  display: grid;
  grid-template-rows: auto 1fr auto;
  height: 95vh;
`;

const ImagePanel = styled.div`
  max-height: 72vh;
  display: grid;
  grid-template-columns: 1fr;
  align-items: center;
  justify-content: center;
  justify-items: center;
  /* max-width: 726px;
  max-height: 720px; */
  gap: 0;
  position: relative;
  width: auto;
`;

const ImageDocHolder = styled.div`
  /* width: 646px; */
  height: 100%;
  gap: 0;
  border-radius: 16px;
  display: grid;
  place-items: center;
  justify-content: center;
`;

const NavigationButton = styled(IconButton)`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: 1px solid white;
  border-radius: 140px;
  height: 40px;
  width: 40px;
`;

const Footer = styled.div`
  width: 100%;
  padding: 4px 2px;
  /* margin-top: 24px; */
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  z-index: 4;
`;

const FooterContent = styled.div`
  display: grid;
  grid-template-rows: auto auto auto;
  align-items: center;
  justify-items: center;
  text-align: center;
  margin: 0 20px;
  color: white;
`;

const FooterHeading = styled(Heading)`
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter0};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize6}px;
`;

const NameSubHeading = styled(Heading)`
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  line-height: 16.94px;
  color: white;
  text-align: center;
`;

const DateAndTimeFooter = styled.div`
  display: grid;
  grid-template-columns: auto;
  width: 100%;
  padding-top: 4px;
`;

const DateText = styled.div`
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  text-align: center;
  padding-right: 8px;
`;

const TimeText = styled.div`
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  text-align: left;
  padding-left: 8px;
`;

const StyledIconButton = styled(IconButton)`
  background-color: transparent;
  border: 1px solid white;
  border-radius: 140px;
  height: 40px;
  width: 40px;
`;

const Header = styled.div`
  display: grid;
  grid-auto-columns: 1fr;
  justify-items: center;
`;

const NewTabButton = styled(TextButton)`
  background-color: transparent;
  border: 1px solid white;
  border-radius: 140px;
  /* height: 40px; */
  /* width: 40px; */
  /* grid-column: 3 / span 1;
  grid-row: 2; */
  display: grid;
  align-content: center;
  justify-content: center;
`;

const NewTabButtonContent = styled.div`
  display: grid;
  align-items: center;
  grid-auto-flow: column;
  padding: 0.25rem;
`;

// const extensionToMimeType: { [key: string]: string } = {
//   '.pdf': 'application/pdf',
//   '.png': 'image/png',
//   '.jpg': 'image/jpeg',
//   '.jpeg': 'image/jpeg',
//   '.gif': 'image/gif',
//   '.bmp': 'image/bmp',
//   '.webp': 'image/webp',
//   '.txt': 'text/plain',
//   '.doc': 'application/msword',
//   '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
//   '.xls': 'application/vnd.ms-excel',
//   '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
//   '.ppt': 'application/vnd.ms-powerpoint',
//   '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
//   // Add more mappings as needed
// };

// /**
//  * Derives the MIME type from a filename based on its extension.
//  * @param filename - The name of the file (e.g., "document.pdf")
//  * @returns The corresponding MIME type or 'application/octet-stream' if unknown
//  */
// const getMimeTypeFromFilename = (filename: string): string => {
//   if (!filename || typeof filename !== 'string') {
//     return 'application/octet-stream'; // Default fallback for invalid input
//   }

//   // Extract the extension from the filename
//   const extension = filename.split('.').pop()?.toLowerCase();
//   if (!extension) {
//     return 'application/octet-stream'; // No extension found
//   }

//   // Return the MIME type if found, otherwise fallback to generic type
//   return extensionToMimeType[`.${extension}`] || 'application/octet-stream';
// };

const extensionToMediaType: { [key: string]: 'pdf' | 'image' | 'other' } = {
  '.pdf': 'pdf',
  '.png': 'image',
  '.jpg': 'image',
  '.jpeg': 'image',
  '.gif': 'image',
  '.bmp': 'image',
  '.webp': 'image',
  '.txt': 'other',
  '.doc': 'other',
  '.docx': 'other',
  '.xls': 'other',
  '.xlsx': 'other',
  '.ppt': 'other',
  '.pptx': 'other',
  // Add more mappings as needed
};

/**
 * Derives the media type from a filename based on its extension.
 * @param filename - The name of the file (e.g., "document.pdf")
 * @returns The corresponding media type ('pdf', 'image', or 'other')
 */
const getMediaTypeFromFilename = (
  filename: string
): 'pdf' | 'image' | 'other' => {
  if (!filename || typeof filename !== 'string') {
    return 'other'; // Default fallback for invalid input
  }

  // Extract the extension from the filename
  const extension = filename.split('.').pop()?.toLowerCase();
  if (!extension) {
    return 'other'; // No extension found
  }

  // Return the media type if found, otherwise fallback to 'other'
  return extensionToMediaType[`.${extension}`] || 'other';
};

const PDFPreview = styled(DocumentPreview)`
  width: 80vw;
  height: 72vh;
  min-height: 70vh;
  border-radius: 16px;
`;

// instantiate logger for debugging generate control component
const log = TemplateLiteralLogger.createLog(
  {
    prefix: '🪵[Document Viewer log]:',
    enabled: false,
    options: { style: { backgroundColor: '#efefef', color: '#017812' } },
  },
  'log'
);

const inspect = TemplateLiteralLogger.createLog(
  {
    prefix: '🧐[Document Viewer inspect]:',
    enabled: false,
    options: { style: { color: '#003d8c' } },
  },
  'i'
);

const warn = TemplateLiteralLogger.createLog(
  {
    prefix: '[Document Viewer warning]:',
    enabled: false,
    options: { style: { color: '#a03f0b' } },
  },
  'warn'
);

/**
 * JobImageViewer component renders an image or icon along with metadata and navigation controls.
 *
 * @param {JobImageViewerProps} props - The properties for the JobImageViewer component.
 * @returns {JSX.Element} The rendered JobImageViewer component.
 */
export const DocumentViewer: React.FC<DocumentViewerProps> = ({
  src,
  purpose,
  fileName,
  mediaType,
  onClose,
  icon = 'file-07',
  created,
  onDownload,
  onOpenInTab,
  downloadable,
  meta,
}) => {
  log`${'(Line 310)'} Props ${{
    src,
    purpose,
    mediaType,
    created,
    onClose,
    icon,
    onDownload,
    onOpenInTab,
    downloadable,
  }}`;
  const [pdfUrl, setPdfUrl] = useState<string | null>();
  useEffect(() => {
    if (mediaType === 'pdf' && src) {
      setPdfUrl(src);
    }
    // Cleanup
    return () => {
      if (pdfUrl) URL.revokeObjectURL(pdfUrl);
    };
  }, [src, mediaType]);
  return (
    <AlertMask>
      <Container>
        <Header>
          {onOpenInTab && (
            <NewTabButton
              btnValue={
                <NewTabButtonContent>
                  <Icon type="plus" />
                  <span>Open in new tab</span>
                </NewTabButtonContent>
              }
              onClick={onOpenInTab}
            />
          )}
        </Header>
        <ImagePanel>
          {/* <NavigationButton icon="chevron-left" onClick={onPrevious} /> */}
          <ImageDocHolder>
            {mediaType === 'image' ? (
              <img
                src={src}
                alt={purpose}
                style={{
                  height: '72vh',
                  borderRadius: '16px',
                }}
              />
            ) : mediaType === 'pdf' ? (
              <PDFPreview documentUrl={src} isBase64={true} />
            ) : (
              <ImageDocHolder>
                <Icon
                  type={icon}
                  size={192}
                  width="100%"
                  height="100%"
                  color="white"
                  strokeWidth="1"
                />
                <NameSubHeading>
                  Unable to preview document.
                  <br />
                  Please try opening document in a new tab, or downloading the
                  document.
                </NameSubHeading>
              </ImageDocHolder>
            )}
          </ImageDocHolder>
          {/* <NavigationButton icon="chevron-right" onClick={onNext} /> */}
        </ImagePanel>
        <Footer>
          <div>
            <StyledIconButton icon="x" color="white" onClick={onClose} />
          </div>
          <FooterContent>
            <FooterHeading>{purpose}</FooterHeading>
            <NameSubHeading>{fileName}</NameSubHeading>
            {meta && (
              <div>
                {Object.keys(meta).map((key) => (
                  <DateText key={key}>{`${key}: ${meta[key]}`}</DateText>
                ))}
              </div>
            )}
            <DateAndTimeFooter>
              <DateText>{created}</DateText>
            </DateAndTimeFooter>
          </FooterContent>
          <div>
            {downloadable && onDownload && (
              <StyledIconButton
                title="Download file"
                icon="download-01"
                onClick={onDownload}
              />
            )}
          </div>
        </Footer>
      </Container>
    </AlertMask>
  );
};
