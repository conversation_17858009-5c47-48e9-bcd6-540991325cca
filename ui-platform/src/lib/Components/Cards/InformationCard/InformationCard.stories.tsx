import type { Meta, StoryObj } from '@storybook/react';
import { InformationCard } from './InformationCard';

const meta: Meta<typeof InformationCard> = {
  component: InformationCard,
  title: 'Components/Cards/InformationCard',
};
export default meta;
type Story = StoryObj<typeof InformationCard>;

export const DefaultInformationCardStory: Story = {
  argTypes: {
    type: {
      control: 'select',
      options: ['default', 'info', 'warning', 'error'],
    },
  },
  args: {
    type: 'info',
    children: (
      <div>
        <h3>Information Card</h3>
        <p>This is an Information Card.</p>
      </div>
    )
  },
};
