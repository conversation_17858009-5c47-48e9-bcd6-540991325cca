import React, { ReactNode } from 'react';
import styled from 'styled-components';

type InformationCardType = { type?: 'error' | 'warning' | 'info' | 'default' };

type InformationCardProps = {
  children: ReactNode;
} & InformationCardType & React.HTMLAttributes<HTMLDivElement>;

const InformationCardContainer = styled.div<InformationCardType>`
  border-radius: 12px;
  overflow: hidden;
  padding: 2rem;
  color: ${(props) => props.theme.ColorsTypographyPrimary};

  border: ${(props) => props.type === 'default' && `1px solid ${props.theme.ColorsBackgroundInverse}`};

  background-color: ${(props) => {
    switch (props.type) {
      case 'info':
        return props.theme.ColorsCardColorDashboardPositive;
      case 'warning':
        return props.theme.ColorsCardColorJobCardIndicatorOrange;
      case 'error':
        return props.theme.ColorsStrokesError;
      default:
        return 'inherit';
    }
  }};
`;

/**
 * A card component to show information within a styled component. The content can be specified as the children.
 * Accepts "type" prop to specify the background color of the card.
 * Valid values for the "type" prop are "error", "warning", "default" and "info"
 *
 * @param {Object} props - The props object for this component.
 * @param {ReactNode} props.children - The children of this component.
 * @param {string} props.type - The type of the information card. It can be error, warning, info or default.
 * @returns {ReactElement} - A React element for the InformationCard component.
 *
 * @example
 * <InformationCard type="error">
 *   {children}
 * </InformationCard>
 */
export const InformationCard: React.FC<InformationCardProps> = ({
  children,
  type = 'default',
  ...rest
}) => {
  return (
    <InformationCardContainer type={type} {...rest}>
      {children}
    </InformationCardContainer >
  );
};
