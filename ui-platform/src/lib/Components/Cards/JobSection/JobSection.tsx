import React from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { PermissionsIndicator } from '../PermissionsIndicator/PermissionsIndicator';
import { ButtonContextMenu, useContextMenuOptions } from '../../ContextMenu';
import { Icon } from '../../Icons/Icon';
import { MenuItemConfig } from '../../../Engine/models/menu-list-item.config';
import { withItems } from '../../List/List';
import { MenuItem } from '../../Menu';
import useViewportDevice from '../../../Hooks/useViewportDevice';
import { MappedClaim } from '../../../Engine/models/mapped-claim';

interface ClaimCardProps {
  claim?: MappedClaim;
  JobLinkRouter: any;
  menuItems: { icon: string; label: string; path: string }[];
}

const JobCardContainer = styled.div`
  display: grid;
  grid-template-rows: auto;
  border-left: solid 1px ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  > :first-child {
    border-top-right-radius: 4px;
  }
  > div:last-child {
    border-bottom: none;
    border-bottom-right-radius: 4px;
    align-self: center;
  }
  @media (max-width: 414px) {
    display: grid;
    width: 100%;
    grid-template-rows: auto;
    > :first-child {
      border-top-right-radius: 0px;
    }
    > :last-child {
      border-bottom-right-radius: 0px;
      border: none;
      align-self: center;
    }
  }
`;

const JobCard = styled.div`
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  display: grid;
  box-sizing: border-box;
  grid-template-columns: 0.1fr 1fr 0.1fr 1fr 1fr 0.5fr;
  padding: 0 0 0 0;
  min-height: 36px;
  align-items: center;
  border-bottom: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  cursor: pointer;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  > :last-child {
    border-bottom: none;
    align-self: center;
  }
  @media (min-width: 1200px) {
    > :nth-child(6) {
      align-self: center;
      justify-self: center;
    }
  }
  @media (max-width: 768px) {
    display: grid;
    width: 100%;
    max-width: 768px;
    grid-template-columns: 1fr; // Reset to single column on mobile
    > :nth-child(n) {
      padding: 3px 0 3px 8px;
    }
  }
`;

const MobileJobCard = styled.div`
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  display: grid;
  box-sizing: border-box;
  grid-template-columns: 0.08fr 1fr 0.8fr 0.8fr;
  padding: 4px;
  min-height: 32px;
  align-items: center;
  border-bottom: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  cursor: pointer;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const JobCardIndicator = styled.div`
  align-content: center;
  justify-self: flex-start;
  width: 6px;
  height: 100%;
`;

const MobileJobCardIndicator = styled.div`
  align-content: center;
  justify-self: flex-start;
  width: 6px;
  height: 100%;
`;

const JobCardSection = styled.div`
  padding: 6px 0 6px 8px;
  line-height: 1.2;
  @media (max-width: 1560px) {
    max-width: 130px;
  }
  @media (max-width: 768px) {
    max-width: 90px;
  }
`;

const MobileJobcardSection = styled.div`
  display: grid;
  grid-template-columns: 0.1fr 0.2fr 1fr;
  border-bottom: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  padding: 10px 0 10px 0;
  align-self: center;
  > :last-child {
    justify-self: start;
    padding-left: 10px;
  }

  @media (min-width: 768px) and (max-width: 1200px) {
    > :last-child {
      padding-right: 17px;
    }
  }
  @media (max-width: 768px) {
    > :last-child {
      padding-left: 22px;
    }
  }
`;

const JobCardNotesAndCMenuSection = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;

  > :first-child {
    justify-self: end;
    padding-right: 10px;
  }

  > :last-child {
    justify-self: start;
    padding-left: 10px;
  }

  @media (min-width: 768px) and (max-width: 1200px) {
    > :last-child {
      padding-right: 17px;
    }
  }
  @media (max-width: 768px) {
    > :last-child {
      padding-left: 22px;
    }
  }
`;

const JobCardText = styled.div`
  overflow: hidden;
  font-weight: 200;
  font-size: 0.72rem;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
  max-width: 120px;
  @media (max-width: 414px) {
    font-size: 0.65rem;
  }
`;

const WithMultipleItems = ({ menuItems, navigate, setShowMenu }: any) => {
  const items: MenuItemConfig[] = menuItems?.map((item: any) => ({
    icon: item.icon,
    label: item.label,
    onClick: (event: React.MouseEvent) => {
      event.stopPropagation();
      if (item.path) {
        navigate(item.path);
      }
      if (setShowMenu) {
        setShowMenu(false);
      }
    },
  }));
  return withItems(MenuItem)(items);
};

export const JobSection: React.FC<ClaimCardProps> = ({
  claim,
  JobLinkRouter,
  menuItems,
}) => {
  const navigate = useNavigate();
  const { showMenu, setShowMenu } = useContextMenuOptions();
  const { isMobile } = useViewportDevice();

  if (isMobile) {
    return (
      <JobCardContainer>
        {claim?.jobs?.map((job, index: number) => (
          <JobLinkRouter job={job} key={job.id}>
            <MobileJobCard key={index}>
              <MobileJobCardIndicator>
                {job?.permissionGranted && (
                  <PermissionsIndicator color={'green'} size="" position="" />
                )}
              </MobileJobCardIndicator>
              <JobCardSection>
                <JobCardText>{job.skillName}</JobCardText>
                <JobCardText>{job.teamleaderName || job.spName}</JobCardText>
              </JobCardSection>
              <JobCardSection>
                <JobCardText>
                  <div>{job.formattedDate}</div>
                </JobCardText>
                <JobCardText>
                  <div>{job.appointmentTime}</div>
                </JobCardText>
              </JobCardSection>
              <JobCardNotesAndCMenuSection>
                <ButtonContextMenu
                  children={<WithMultipleItems menuItems={menuItems} navigate={navigate} setShowMenu={setShowMenu} />}
                  orientation="right"
                  additionalStyling="&:hover {background-color: rgba(40, 48, 51, 0.84); color: unset;}"
                  dropdownClassName="job-card-dropdown"
                />
              </JobCardNotesAndCMenuSection>
            </MobileJobCard>
          </JobLinkRouter>
        ))}
      </JobCardContainer>
    );
  }

  return (
    <JobCardContainer>
      {claim?.jobs?.map((job, index: number) => (
        <JobLinkRouter job={job} key={job.id}>
          <JobCard key={index}>
            <JobCardIndicator>
              {job?.permissionGranted && (
                <PermissionsIndicator color={'green'} size="" position="" />
              )}
            </JobCardIndicator>
            <JobCardSection>
              <JobCardText>{job.skillName}</JobCardText>
              <JobCardText>{job.teamleaderName || job.spName}</JobCardText>
            </JobCardSection>
            <JobCardSection>
              <JobCardText>
                <div>{job.formattedDate}</div>
              </JobCardText>
              <JobCardText>
                <div>{job.appointmentTime}</div>
              </JobCardText>
            </JobCardSection>
            <JobCardSection>
              {/* Appointment display logic */}
              <JobCardText>
                {(!job.appointment || job.appointment.reason === 'No Appointment') ? (
                  <div style={{ color: '#888', fontWeight: 400 }}>No Appointment</div>
                ) : null}
                {job.stateTextDisplay}
              </JobCardText>
              <JobCardText>Placeholder</JobCardText>
            </JobCardSection>
            <JobCardSection>
              <JobCardText>Placeholder</JobCardText>
            </JobCardSection>
            <JobCardNotesAndCMenuSection>
              <div
                onClick={(e) => {
                  e.stopPropagation();
                  if (setShowMenu) {
                    setShowMenu(!showMenu);
                  }
                }}
              >
                <Icon type="notes" size={20} />
              </div>
              <ButtonContextMenu
                children={<WithMultipleItems menuItems={menuItems} navigate={navigate} setShowMenu={setShowMenu} />}
                orientation="right"
                additionalStyling="&:hover {background-color: rgba(40, 48, 51, 0.84); color: unset;}"
                dropdownClassName="job-card-dropdown"
              />
            </JobCardNotesAndCMenuSection>
          </JobCard>
        </JobLinkRouter>
      ))}
    </JobCardContainer>
  );
};