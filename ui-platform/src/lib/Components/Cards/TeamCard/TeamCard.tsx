import React, { ReactNode, useCallback, useMemo } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { normalizeUrl } from '../../../Utilities';
import { Avatar } from '../../Avatars/Avatar/Avatar';
import { Icon } from '../../Icons';
import { StatusIndicator } from '../../StatusIndicator/StatusIndicator';
import { TeamCardIcon } from '../../TeamCardIcon/TeamCardIcon';

interface GenericData {
  [key: string]: any;
}

interface SectionConfig {
  type: 'linkColumn' | 'textColumn'; // 'type' is required and should be one of the allowed types
  titleKey?: string; // Optional for sections that don't need it
  subTitleKey?: string;
  statusKey?: string;
  avatarKey?: string;
  showAvatar?: boolean;
  showStatusIndicator?: boolean;
  showTitleText?: boolean;
  showRating?: boolean;
}

interface TeamMemberCardProps<T extends GenericData = GenericData> {
  data: T[];
  linkUrl: string;
  linkUrlParams?: { key: string; value: string }[];
  reduceOpacity?: boolean;
  sectionCount?: number;
  showAvatar?: boolean;
  showStatusIndicator?: boolean;
  showRating?: boolean;
  config: SectionConfig[];
  isBorder?: boolean;
  _navigation?: any;
  _navigate?: any;
  _actionData?: any;
}

// Apply the background color to all sections
const StyledSection = styled.div`
  background-color: ${(props) => props.theme.ColorsCardColorJobCardPrimary};
  border-radius: ${(props) => props.theme.RadiusSm};
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
`;

const StyledAvatarTeamName = styled.h1`
  // overflow: hidden;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  // text-overflow: ellipsis;
  white-space: nowrap;
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-style: normal;
  font-weight: ${(props) => props.theme.FontWeightsInter6};
  line-height: normal;
  width: 10rem;
`;

const RatingContainer = styled.div`
  display: grid;
  justify-content: center;
  align-items: center;
  grid-template-columns: auto auto;
  gap: ${(props) => props.theme.SpacingXs};
`;

const StyledRating = styled.div`
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
`;

const StyledIconContainer = styled.div<{ isFirst?: boolean; isLast?: boolean }>`
  display: grid;
  grid-template-columns: auto auto auto;
  height: 56px;
  align-items: center;
  gap: 8px;
  padding: ${(props) => `${props.theme.SpacingSm} ${props.theme.SpacingLg}`};
  background-color: ${(props) => props.theme.ColorsCardColorJobCardPrimary};
  width: auto;

  border-radius: ${(props) =>
    props.isFirst
      ? `${props.theme.RadiusXs} 0 0 ${props.theme.RadiusXs}`
      : '0'};
  border-radius: ${(props) =>
    props.isLast ? `0 ${props.theme.RadiusXs} ${props.theme.RadiusXs} 0` : '0'};
`;

const ProfileAndStatus = styled.div`
  position: relative;
  max-width: 50px;
`;

const StyledIndicator = styled(StatusIndicator)`
  position: absolute;
  top: 0;
  right: -5px;
`;

const StyledTeamCardContainer = styled(
  ({
    sectionCount,
    reduceOpacity,
    showFirstSection,
    isBorder,
    ...rest
  }: {
    sectionCount: number;
    reduceOpacity: boolean;
    showFirstSection: boolean;
    isBorder: boolean;
    children: ReactNode;
    to: string;
  }) => <Link {...rest} />
)<{
  sectionCount: number;
  reduceOpacity: boolean;
  showFirstSection: boolean;
  isBorder: boolean;
}>`
  all: unset;
  text-decoration: none;
  color: inherit;
  display: grid;
  grid-template-columns: ${(props) => {
    let columns = '';

    if (props.showFirstSection) columns += '1fr '; // First section as fixed size
    columns += `repeat(${
      props.sectionCount - (props.showFirstSection ? 1 : 0)
    }, 1fr)`; // Dynamic columns for other sections

    return columns;
  }};
  width: 100%;
  align-items: stretch;
  box-sizing: border-box;
  border-radius: ${(props) => props.theme.RadiusXs};
  border: ${(props) =>
    props.isBorder
      ? '0.827px solid var(--colors-strokes-focus, #118AB2)'
      : 'none'};
  gap: ${(props) => props.theme.SpacingXxs};
  opacity: ${(props) => (props.reduceOpacity ? 0.5 : 1)};
  transition: opacity 0.3s ease;
  cursor: pointer;

  margin-bottom: ${(props) => props.theme.SpacingSm};

  &:last-child {
    margin-bottom: 0; // Ensure no margin is added after the last card
  }
`;

const StyledSubGridItem = styled.div<{
  isFirst?: boolean;
  isLast?: boolean;
}>`
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: ${(props) => props.theme.SpacingSm};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  background-color: ${(props) => props.theme.ColorsCardColorJobCardPrimary};
  gap: ${(props) => props.theme.SpacingXxs};
  overflow: hidden;
  text-overflow: ellipsis;
  height: 56px;

  border-radius: ${(props) =>
    props.isFirst
      ? `${props.theme.RadiusXs} 0 0 ${props.theme.RadiusXs}`
      : '0'};
  border-radius: ${(props) =>
    props.isLast ? `0 ${props.theme.RadiusXs} ${props.theme.RadiusXs} 0` : '0'};
`;

const StyledSubGridText = styled.div`
  overflow: hidden;
  color: var(--colors-typography-secondary, #c4c4c4);
  text-align: center;
  text-overflow: ellipsis;
  text-wrap: nowrap;
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-style: normal;
  font-weight: ${(props) => props.theme.FontWeightsInter5};
  line-height: normal;
`;

/**
 * A component that renders a team card with the given data and configuration.
 *
 * @param {GenericData[]} data - The data to be rendered in the team card.
 * @param {string} linkUrl - The base URL for the link.
 * @param {{ key: string; value: string }[]} linkUrlParams - The parameters to add to the link URL.
 * @param {SectionConfig[]} config - The configuration for each section of the team card.
 * @param {boolean} reduceOpacity - Whether to reduce the opacity of the team card.
 * @param {boolean} isBorder - Whether to add a border to the team card.
 *
 * @returns {ReactNode} The rendered team card.
 */

export const TeamCard = <T extends GenericData>({
  data,
  linkUrl,
  linkUrlParams,
  config,
  reduceOpacity = false,
  isBorder = false,
}: TeamMemberCardProps<T>) => {
  const teamCardData = useMemo(() => data, [data]);
  const setCardLinkUrl = useCallback(
    (item: T) => {
      if (item.navigate_to_url) {
        return normalizeUrl(
          '',
          item.navigate_to_url,
          `${
            linkUrlParams
              ? `${linkUrlParams.reduce(
                  (
                    prev: string,
                    curr: { key: string; value: string },
                    idx: number,
                    arr: { key: string; value: string }[]
                  ) =>
                    idx === arr.length - 1
                      ? `${prev}${curr.key}=${item[curr.value]}`
                      : `${prev}${curr.key}=${item[curr.value]}&`,
                  '?'
                )}`
              : ''
          }`
        );
      } else {
        return normalizeUrl(
          '',
          linkUrl,
          `${
            linkUrlParams
              ? `${linkUrlParams.reduce(
                  (
                    prev: string,
                    curr: { key: string; value: string },
                    idx: number,
                    arr: { key: string; value: string }[]
                  ) =>
                    idx === arr.length - 1
                      ? `${prev}${curr.key}=${item[curr.value]}`
                      : `${prev}${curr.key}=${item[curr.value]}&`,
                  '?'
                )}`
              : ''
          }`
        );
      }
    },
    [linkUrl, linkUrlParams]
  );
  const renderSectionsForItem = (item: T) => {
    return config.map((section: any, sectionIndex: number) => {
      const isFirst = sectionIndex === 0; // First section
      const isLast = sectionIndex === config.length - 1; // Last section
      const cardLinkUrl = setCardLinkUrl(item);
      switch (section.type) {
        case 'linkColumn':
          return (
            <StyledIconContainer
              key={`linkColumn-${sectionIndex}`}
              isFirst={isFirst}
              isLast={isLast}
            >
              {section.showAvatar && (
                <ProfileAndStatus>
                  <TeamCardIcon
                    icon="user-circle"
                    color="#fff"
                    background={true}
                    status={item[section.statusKey] || 'progress'}
                    showStatusIndicator={section.showStatusIndicator}
                  />
                </ProfileAndStatus>
              )}
              {section.showTitleText && (
                <StyledAvatarTeamName>
                  {item[section.titleKey]}
                </StyledAvatarTeamName>
              )}
              {section.showRating && (
                <RatingContainer>
                  <StyledRating>4.5</StyledRating>
                  <Icon type="star-rating" size={16} color="#FFC107" />
                </RatingContainer>
              )}
            </StyledIconContainer>
          );
        case 'textColumn':
          return (
            <StyledSubGridItem
              key={`textColumn-${sectionIndex}`}
              isFirst={isFirst} // Apply border-radius to first section
              isLast={isLast}
            >
              <StyledSubGridText>
                {typeof item[section.titleKey] === 'string'
                  ? item[section.titleKey]
                  : Array.isArray(item[section.titleKey]) &&
                    item[section.titleKey].every(
                      (element: unknown) => typeof element === 'string'
                    )
                  ? item[section.titleKey].join(', ')
                  : 'N/A'}
              </StyledSubGridText>
            </StyledSubGridItem>
          );
        default:
          return null;
      }
    });
  };

  return teamCardData
    ? teamCardData?.map((item, index) => {
        const cardLinkUrl = setCardLinkUrl(item);
        return (
          <StyledTeamCardContainer
            to={cardLinkUrl}
            key={index}
            sectionCount={config.length}
            showFirstSection={true}
            reduceOpacity={reduceOpacity}
            isBorder={isBorder}
          >
            {renderSectionsForItem(item)}
          </StyledTeamCardContainer>
        );
      })
    : 'No data found';
};
