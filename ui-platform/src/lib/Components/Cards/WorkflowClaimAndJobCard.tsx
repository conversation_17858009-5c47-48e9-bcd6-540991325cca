import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { Icon } from '../Icons';
import { PermissionsIndicator } from './PermissionsIndicator/PermissionsIndicator';
import B64StringLogo from '../ClientLogos/DynamicB64StringLogo';
import { ButtonContextMenu, useContextMenuOptions } from '../ContextMenu';
import { FilterMenu, FilterMenuProps } from '../Filter/FIlterMenu/FilterMenu';
import { useNavigate } from 'react-router-dom';
import { MenuItemConfig } from '../../Engine/models/menu-list-item.config';
import { withItems } from '../List/List';
import { MenuItem } from '../Menu';
import { formatDate } from 'date-fns';
// import { FilterMenuProps } from "./FilterMenu";

export const filterMenuSampleData: FilterMenuProps['items'] = [
  'Text here',
  { text: 'Dropdown 1', icon: 'chevron-right' },
  { text: 'Dropdown 2', icon: 'chevron-right' },
  { text: 'Dropdown 3', icon: 'chevron-right' },
  { text: 'Dropdown 4', icon: 'chevron-right' },
  { text: 'Dropdown 5', icon: 'chevron-right' },
  { text: 'Dropdown 6', icon: 'chevron-right' },
  { text: 'Dropdown 7', icon: 'chevron-right' },
];

interface WorkflowClaimAndJobCardProps {
  claim?: any;
  jobCardNumberPrefix?: string;
  ClaimLinkRouter: any;
  JobLinkRouter: any;
  displayLogo?: boolean;
  menuItems: { icon: string; label: string; path: string }[];

}
interface WorkflowClaimCardProps {
  children?: any;
}
interface WorkflowJobCardProps {
  children?: any;
}
interface WorkflowJobCardIndicatorProps {
  children?: any;
}

const WorkflowClaimAndJobCardContainer = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  grid-template-columns: 2fr 5fr;
  border-radius: 4px;
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  height: auto;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  box-sizing: border-box;

  @media (max-width: 768px) {
    grid-template-columns: 1.5fr 3.5fr;
  }
  @media (max-width: 414px) {
    grid-template-columns: 4fr 4fr 1fr;
    max-width: 414px;
  }
`;
const WorkflowClaimCardContainer = styled(({ ...rest }: WorkflowClaimCardProps) => (
  <div {...rest}></div>
))`
  display: grid;
  grid-template-columns: 1.6fr 2.2fr;
  width: 100%;
  > :first-child {
   
  }
  @media (min-width: 768px) and (max-width: 1200px) {
    display:grid;
    grid-template-columns: 1fr;
    grid-template-rows: 0.3fr 1fr;
    > :first-child {
     width: 100%;
      // max-height: 100px; 
    }
  }
    @media (max-width: 768px) {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 0.3fr 1fr;
    

    > :first-child {
     display: grid;
    //  width: 100%;
      max-height: 100px; 
      text-align: center;
    }
    }
  
  
  //   ${(props) => props?.theme.ColorsCardColorDashboardError}; 
  line-height: 1.5;
 
  
`;
const WorkflowClaimCard = styled(({ ...rest }: WorkflowClaimCardProps) => (
  <div {...rest}></div>
))`
  display: grid;
  grid-template-columns: 0.1fr 1fr 4fr;
  padding: 25px 17px 25px 0;
  line-heigth: 1.5;
  > :nth-child(2) {
    align-self: center;
    }
  > :nth-child() {
    min-width: 175px;
    }
    > :first-child {
    align-self: center;
  }
  @media (max-width: 1200px) and (min-width: 768px) {
    border-top: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  }
  @media (min-width: 1200px) {
      border-left: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};

  }
  @media (max-width: 768px) {
    padding: 25px 17px 25px 0;
    border-top: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
    grid-template-columns: 0.1fr 1.5fr 3.5fr;
    }
    @media (max-width: 414px) {
      padding: 25px 12px 25px 0;
    grid-template-columns: 0.1fr 4fr 1fr;
    // max-width: 414px;
  }
`;
const WorkflowClaimCardActions = styled(({ ...rest }) => <div {...rest}></div>)`
  // padding-top: 25px;
  align-self: center;
`;

const WorkflowClaimCardDetails = styled(({ ...rest }) => <div {...rest}></div>)`
  display: grid;
  grid-auto-rows: min-content;
  align-self: top;
  line-height: 1.5;
  grid-gap: 2px;
  font-size: smaller;
  text-align: right;
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
`;
const WorkflowJobCardContainer = styled(({ ...rest }) => <div {...rest}></div>)`
  display: grid;
  grid-template-rows: auto;
  > :first-child {
    border-top-right-radius: 4px;

  }
  > div:last-child {
    border-bottom: none;
    border-bottom-right-radius: 4px;
    align-self: center;
    
  }
  @media (max-width: 414px) {
    display: grid;
    width: 100%;
    // max-width: 768px;
    grid-template-rows: auto;
    > :first-child {
      border-top-right-radius: 0px;
    }
    > :last-child {
      border-bottom-right-radius: 0px;
      border: none;
      align-self: center;
    }
  }
`;

const WorkflowJobCard = styled(({ ...rest }: WorkflowJobCardProps) => (
  <div {...rest}></div>
))`
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  display: grid;
  box-sizing: border-box;
  grid-template-columns: 0.1fr 1.3fr 1fr 1.4fr 1fr 0.3fr;
  padding: 0 0 0 0;
  height: auto;
  align-items: center;
  border-left: solid 1px ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  border-bottom: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  > :last-child {
    border-bottom: none;
    align-self: center;
  }

  @media(min-width: 1200px) {
    display: grid;
    grid-template-columns: 0.1fr 1.3fr 1fr 1.4fr 1fr 0.5fr 0.3fr;  
    > :nth-child(6) {
      align-self: center;
      justify-self: center;
    }
  }
  @media (max-width: 768px) {
    display: grid;
    width: 100%;
    max-width: 768px;
    grid-template-columns: 0.1fr 1fr 1fr 0.3fr;
 
  }
  @media (max-width: 414px) {
    display: grid;
    width: 100%;
    // max-width: 768px;
    grid-template-rows: auto;
    padding: 10px 0 10px 0;
  }
`;

const WorkflowJobCardIndicator = styled(
  ({ children, ...rest }: WorkflowJobCardIndicatorProps) => (
    <div {...rest}>{children}</div>
  )
)`
  align-content: center;
  justify-self: flex-start;
  // border-left: 1px solid black;
  width: 6px;
  height: 100%;
  children: any;
`;

const WorkflowJobCardSection = styled(({ ...rest }) => <div {...rest}></div>)`
  // display: flex;
  // flex-direction: column;
  // background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  padding: 15px 0 15px 15px;
  line-height: 1.5;

  @media (max-width: 1560px) {
    // width: 100%;
    max-width: 200px;
  }
  @media (max-width: 768px) {
    // width: 100%;
    max-width: 120px;
  }
`;

const WorkflowMobileJobcardSection = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  display: grid;
  grid-template-columns: 0.1fr 0.2fr 1fr;
  // max-height: 20px;
  // background:blue;
  border-bottom: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};

  padding: 10px 0 10px 0;
  align-self: center;
`;

const WorkflowJobCardNotesAndCMenuSection = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  display: grid;
  grid-template-rows: 0.5fr 0.7fr;
> :first-child {
  // background-color: red;
  align-self: center;
  justify-self: center;
  padding: 20px 10px 0 10px;
}
@media (min-width: 768px) and (max-width: 1200px) {
  > :last-child {
    padding-right: 17px
  }
}

@media (max-width: 768px) {
  > :last-child {
    padding-left: 22px
  }
}
`

// FUNCTIONS =================
 
/**
 * A hook that returns the current window size and updates when the window is resized.
 *
 * @returns {{width: number, height: number}} The current window size.
 */
const useScreenSize = () => {
  const [screenSize, setScreenSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });
  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  return screenSize;
};

const Expander = styled.div`
  width: 100%;
  height: 100%;
  background: ${(props) => props?.theme.ColorsCardColorCalendarSuccess};
  align-content: center;
  text-align: center;
  border-radius: 0 4px 4px 0;
`;

const WorkflowJobCardText = styled.div`
  overflow: hidden;
  font-weight: 200;
  font-size: 0.8rem;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
`;

/**
 * @function WorkflowClaimAndJobCard
 * @description This component renders a card containing a claim and its associated jobs.
 * @param {object} claim - The claim object.
 * @returns {ReactElement} The rendered card.
 * @example
 * <WorkflowClaimAndJobCard claim={claim} />
 */

export const WorkflowClaimAndJobCard: React.FC<
  WorkflowClaimAndJobCardProps
> = ({ 
  claim,
  jobCardNumberPrefix,
  ClaimLinkRouter,
  JobLinkRouter,
  menuItems, // icon, label, path
  displayLogo,
 }: any) => {
  const screenSize = useScreenSize();
  const isMobile = screenSize.width < 414;
  const isScreenSize768 = screenSize.width <= 769 && screenSize.width >= 414;
  const isScreenSize769Plus = screenSize.width >= 769 && screenSize.width <= 1200; 
  const isScreenSize769PlusExtra = screenSize.width > 1200; 
  const navigate = useNavigate();

   const WithMultipleItems = () => {
      const { showMenu, setShowMenu } = useContextMenuOptions();
      const items: MenuItemConfig[] = menuItems?.map((item: any) => ({
        icon: item.icon,
        label: item.label,
        onClick: (event: React.MouseEvent) => {
          event.stopPropagation();
          if (item.path) {
            navigate(item.path);
          }
          if (setShowMenu) {
            setShowMenu(false);
          }
        },
      }));
      return withItems(MenuItem)(items);
    }
  
  if (isScreenSize768) {
    return (
      <WorkflowClaimAndJobCardContainer>
        <WorkflowClaimCardContainer>
          <ClaimLinkRouter claim={claim}>
            {displayLogo && <B64StringLogo id_string='builders' maximumWidth={100} minimumWidth={135}></B64StringLogo>}
            <WorkflowClaimCard>
            {claim?.permissionGranted && (
              <PermissionsIndicator
                color={'green'}
                size=""
                position=""
              ></PermissionsIndicator>
            )}
              <WorkflowClaimCardActions>
                {/* <Icon type="dots-vertical" /> */}
                <ButtonContextMenu
                  orientation="left"
                  dropdownGap="4px"
                  Button={Icon as any}
                  btnProps={{ icon: 'dots-vertical', iconColor: '#bbb'}}
                >
                <FilterMenu items={filterMenuSampleData} />

                </ButtonContextMenu>
              </WorkflowClaimCardActions>
              <WorkflowClaimCardDetails>
                <div style={{ fontWeight: 'bold' }}>{claim?.mid}</div>
                <div>{claim?.formattedDate}</div>
                <WorkflowJobCardText>
                  {claim?.customer}
                </WorkflowJobCardText>
                <div>{claim?.stateTextDislplay}</div>
                <div>State</div>
                <div style={{ color: 'red' }}>1d 01h 37m</div>
              </WorkflowClaimCardDetails>
            </WorkflowClaimCard>
          </ClaimLinkRouter>
        </WorkflowClaimCardContainer>

        <WorkflowJobCardContainer>
          {claim?.jobs?.map((job: any) => {
            return (
              <JobLinkRouter job={job} key={job.id}>
                <WorkflowJobCard>
                  <WorkflowJobCardIndicator>
                    {job?.permissionGranted && (
                      <PermissionsIndicator
                        color={'green'}
                        size=""
                        position=""
                      ></PermissionsIndicator>
                    )}
                  </WorkflowJobCardIndicator>
                  <WorkflowJobCardSection>
                  <WorkflowJobCardText>{job.skillName}</WorkflowJobCardText>
                <WorkflowJobCardText>{job.teamleadName}</WorkflowJobCardText>
                <WorkflowJobCardText>
                  {job.stateTextDislplay}
                </WorkflowJobCardText>
                <WorkflowJobCardText>{job.description}</WorkflowJobCardText>
                  </WorkflowJobCardSection>
                  <WorkflowJobCardSection>
                  <WorkflowJobCardText>
                    <div>{job.formattedDate}</div>
                  </WorkflowJobCardText>
                  <WorkflowJobCardText>
                    <div>{job.time}</div>
                  </WorkflowJobCardText>
                  <WorkflowJobCardText>
                    {job.stateTextDislplay}
                  </WorkflowJobCardText>
                  <WorkflowJobCardText>{job.description}</WorkflowJobCardText>
                </WorkflowJobCardSection>

                  <WorkflowJobCardNotesAndCMenuSection>
                  <div><Icon type="notes" size={20} /></div>
                  <WorkflowJobCardSection>
                    {/* <Icon type="dots-vertical" /> */}
                    <ButtonContextMenu
                      children={<WithMultipleItems />}
                      orientation="right"
                      additionalStyling="&:hover {background-color: rgba(40, 48, 51, 0.84); color: unset;}"
                      dropdownClassName="job-card-dropdown"
                    />
                  </WorkflowJobCardSection>
                  </WorkflowJobCardNotesAndCMenuSection>
                </WorkflowJobCard>
              </JobLinkRouter>
            );
          })}
        </WorkflowJobCardContainer>
      </WorkflowClaimAndJobCardContainer>
    );
  } else if (isMobile === true && !isScreenSize768) {
    return (
      <WorkflowClaimAndJobCardContainer>
        <ClaimLinkRouter claim={claim}>
          <WorkflowClaimCard>
          {claim?.permissionGranted && (
            <PermissionsIndicator
              color={'green'}
              size=""
              position=""
            ></PermissionsIndicator>
          )}
            <WorkflowClaimCardActions>
              <Icon type="dots-vertical" />
            </WorkflowClaimCardActions>
            <WorkflowClaimCardDetails>
              <div style={{ fontWeight: 'bold' }}>{claim?.mid}</div>
              <div>{claim?.formattedDate}</div>
              <WorkflowJobCardText>
                {claim?.customer}
              </WorkflowJobCardText>
              <div>{claim?.stateTextDislplay}</div>
              <div>State</div>
              <div style={{ color: 'red' }}>1d 01h 37m</div>
            </WorkflowClaimCardDetails>
          </WorkflowClaimCard>
        </ClaimLinkRouter>
        <WorkflowJobCardContainer>
          {claim?.jobs?.map((job: any, index: number) => {
            return (
              <JobLinkRouter job={job} key={job.id}>
              <WorkflowMobileJobcardSection key={index}>
                <WorkflowJobCardIndicator>
                  {job?.permissionGranted && (
                    <PermissionsIndicator
                      color={'green'}
                      size=""
                      position=""
                    ></PermissionsIndicator>
                  )}
                </WorkflowJobCardIndicator>
                <WorkflowClaimCardActions>
                  {/* <Icon type="dots-vertical" /> */}
                   <ButtonContextMenu
                      children={<WithMultipleItems />}
                      orientation="right"
                      additionalStyling="&:hover {background-color: rgba(40, 48, 51, 0.84); color: unset;}"
                      dropdownClassName="job-card-dropdown"
                    />
                </WorkflowClaimCardActions>
                <div style={{display: 'grid', gridTemplateRows: 'auto'}}>
                <WorkflowJobCardText>
                  <div style={{ fontWeight: 'bold' }}> {job.skillName}</div>
                </WorkflowJobCardText>
                <WorkflowJobCardText>{job.teamleadName}</WorkflowJobCardText>
                <WorkflowJobCardText>{job.description}</WorkflowJobCardText>
                <WorkflowJobCardText>
                  <div style={{ fontWeight: 'bold' }}> {job.formattedDate}</div>
                </WorkflowJobCardText>
                <WorkflowJobCardText>
                  <div> {job?.sla}</div>
                </WorkflowJobCardText>
                </div>
                
              </WorkflowMobileJobcardSection>
              </JobLinkRouter>
            );
          })}
        </WorkflowJobCardContainer>
        <Expander>
          <Icon type="chevron-down" />
        </Expander>
      </WorkflowClaimAndJobCardContainer>
    );
  } else if (isScreenSize769Plus) {
    return (
      <WorkflowClaimAndJobCardContainer>
        <WorkflowClaimCardContainer>
        <ClaimLinkRouter claim={claim}>
        {displayLogo && <B64StringLogo id_string='builders' maximumWidth={100} minimumWidth={135}></B64StringLogo>}
        <WorkflowClaimCard>
          {claim?.permissionGranted && (
            <PermissionsIndicator
              color={'green'}
              size=""
              position=""
            ></PermissionsIndicator>
          )}
          <WorkflowClaimCardActions>
            <Icon type="dots-vertical" />
          </WorkflowClaimCardActions>
          <WorkflowClaimCardDetails>
            <div style={{ fontWeight: 'bold' }}>{claim?.mid}</div>
            <div>{claim?.formattedDate}</div>
            <WorkflowJobCardText>
              {claim?.customer}
            </WorkflowJobCardText>
            <div>{claim?.stateTextDislplay}</div>
            <div>State</div>
            <div style={{ color: 'red' }}>1d 01h 37m</div>
          </WorkflowClaimCardDetails>
        </WorkflowClaimCard>
        </ClaimLinkRouter>
        </WorkflowClaimCardContainer>

        <WorkflowJobCardContainer>
          {claim?.jobs?.map((job: any, index: number) => {
            return (
              <JobLinkRouter job={job} key={job.id}>
                <WorkflowJobCard key={index}>
                  <WorkflowJobCardIndicator>
                    {job?.permissionGranted && (
                      <PermissionsIndicator
                        color={'green'}
                        size=""
                        position=""
                      ></PermissionsIndicator>
                    )}
                  </WorkflowJobCardIndicator>
                  <WorkflowJobCardSection>
                    <WorkflowJobCardText>{job.skillName}</WorkflowJobCardText>
                    <WorkflowJobCardText>{job.sp}</WorkflowJobCardText>
                  </WorkflowJobCardSection>
                  <WorkflowJobCardSection>
                    <WorkflowJobCardText>
                      <div>{job.formattedDate}</div>
                    </WorkflowJobCardText>
                    <WorkflowJobCardText>
                      <div>{job.time}</div>
                    </WorkflowJobCardText>
                  </WorkflowJobCardSection>
                  <WorkflowJobCardSection>
                    <WorkflowJobCardText>{job.state}</WorkflowJobCardText>
                    <WorkflowJobCardText>{job.description}</WorkflowJobCardText>
                  </WorkflowJobCardSection>
                  <WorkflowJobCardSection>
                    <WorkflowJobCardText>
                      <div>{job.customer}</div>
                      <div style={{ color: 'red', fontWeight: 'bold' }}>
                        {job.sla}
                      </div>
                    </WorkflowJobCardText>
                  </WorkflowJobCardSection>
                  <WorkflowJobCardNotesAndCMenuSection>
                  <div><Icon type="notes" size={20} /></div>
                  <WorkflowJobCardSection>
                    <Icon type="dots-vertical" />
                  </WorkflowJobCardSection>
                  </WorkflowJobCardNotesAndCMenuSection>
                  
                </WorkflowJobCard>
              </JobLinkRouter>
            );
          })}
        </WorkflowJobCardContainer>
      </WorkflowClaimAndJobCardContainer>
    );
  } else if (isScreenSize769PlusExtra) {
    return (
      <WorkflowClaimAndJobCardContainer>
        
        <WorkflowClaimCardContainer>
        <ClaimLinkRouter claim={claim}>
        {displayLogo && <B64StringLogo id_string='builders' maximumWidth={100} minimumWidth={135}></B64StringLogo>}
        <WorkflowClaimCard>
          {claim?.permissionGranted && (
            <PermissionsIndicator
              color={'green'}
              size=""
              position=""
            ></PermissionsIndicator>
          )}
          <WorkflowClaimCardActions>
            <Icon type="dots-vertical" />
          </WorkflowClaimCardActions>
          <WorkflowClaimCardDetails>
            <div style={{ fontWeight: 'bold' }}>{claim?.mid}</div>
            <div>{claim?.formattedDate}</div>
            <WorkflowJobCardText>
              {claim?.customer}
            </WorkflowJobCardText>
            <div>{claim?.stateTextDislplay}</div>
            <div>State</div>
            <div style={{ color: 'red' }}>1d 01h 37m</div>
          </WorkflowClaimCardDetails>
        </WorkflowClaimCard>
        </ClaimLinkRouter>
        </WorkflowClaimCardContainer>
        

        <WorkflowJobCardContainer>
          {claim?.jobs?.map((job: any, index: number) => {
            return (
              <JobLinkRouter job={job} key={job.id}>
                <WorkflowJobCard key={index}>
                  <WorkflowJobCardIndicator>
                    {job?.permissionGranted && (
                      <PermissionsIndicator
                        color={'green'}
                        size=""
                        position=""
                      ></PermissionsIndicator>
                    )}
                  </WorkflowJobCardIndicator>
                  <WorkflowJobCardSection>
                    <WorkflowJobCardText>{job.skill}</WorkflowJobCardText>
                    <WorkflowJobCardText>{job.sp}</WorkflowJobCardText>
                  </WorkflowJobCardSection>
                  <WorkflowJobCardSection>
                    <WorkflowJobCardText>
                      <div>{job.date}</div>
                    </WorkflowJobCardText>
                    <WorkflowJobCardText>
                      <div>{job.time}</div>
                    </WorkflowJobCardText>
                  </WorkflowJobCardSection>
                  <WorkflowJobCardSection>
                    <WorkflowJobCardText>{job.state}</WorkflowJobCardText>
                    <WorkflowJobCardText>{job.description}</WorkflowJobCardText>
                  </WorkflowJobCardSection>
                  <WorkflowJobCardSection>
                    <WorkflowJobCardText>
                      <div>{job.customer}</div>
                      <div style={{ color: 'red', fontWeight: 'bold' }}>
                        {job.sla}
                      </div>
                    </WorkflowJobCardText>
                  </WorkflowJobCardSection>
                  <WorkflowJobCardSection>
                    <Icon type="notes" size={20} />
                  </WorkflowJobCardSection>
                  <WorkflowJobCardSection>
                    <Icon type="dots-vertical" />
                  </WorkflowJobCardSection>
                </WorkflowJobCard>
              </JobLinkRouter>
            );
          })}
        </WorkflowJobCardContainer>
      </WorkflowClaimAndJobCardContainer>
    );
  }
};
