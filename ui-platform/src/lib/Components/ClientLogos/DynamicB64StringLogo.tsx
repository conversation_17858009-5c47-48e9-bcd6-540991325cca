//import { useState, useEffect } from 'react';
import {imageData} from '../../Assets/imageData'

/**
 * Interface for B64StringLogo component props.
 * 
 * @interface B64StringLogoProps
 * @property {string} id_string - The ID string for the logo.
 * @property {number} maximumWidth - The maximum width of the logo.
 * @property {number} minimumWidth - The minimum width of the logo.
 */

export interface B64StringLogoProps { 
    // [key: string]: string
    id_string: string;
    maximumWidth: number;
    minimumWidth: number;

 }
/*
randomizeIdString() is for Storybook purposes only. Remove the 
hook for actual functionality

*/
/**
 * DynamicB64StringLogo component.
 * 
 * This component renders a logo based on the provided ID string and width constraints.
 * 
 * @param {B64StringLogoProps} props - The component props.
 * @returns {JSX.Element} The rendered logo.
 */
export const B64StringLogo: React.FC<B64StringLogoProps> = ({ id_string, maximumWidth, minimumWidth }) => {
    const lowerCaseId = id_string.toLowerCase();
    const imageSource = `data:image/svg+xml;base64,${imageData[lowerCaseId]}`;
    
    if (!imageData[lowerCaseId]) {
      // Fallback to a default image or return null if the logo is not found
      const fallbackImageSource = `data:image/svg+xml;base64,${imageData['builders']}`;
      return (
         <div style={{placeSelf: 'center', width:'max-content', padding: '20px', margin: 'auto'}}>
           <img src={fallbackImageSource} alt={`Logo for builders`}  style={{maxWidth: `${maximumWidth}px`, minWidth: `${minimumWidth}px`}}/>
         </div>
      );
    }
 
    return (
     <div style={{placeSelf: 'center', width:'max-content', padding: '20px', margin: 'auto'}}>
       <img src={imageSource} alt={`Logo for ${id_string}` }  style={{maxWidth: `${maximumWidth}px`, minWidth: `${minimumWidth}px`}}/>
     </div>
    );
     
 } 
 
export default B64StringLogo;