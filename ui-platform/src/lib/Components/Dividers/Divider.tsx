import React from 'react';
import styled from 'styled-components';

export interface DividerProps {
  background: string;
  size: string;
  type?: string;
  state?: string;
  height?: string;
  className?: string;
}
interface DividerColourMap {
  [key: string]: string;
}
interface DividerSizeMap {
  [key: string]: string;
}
interface DividerTypeMap {
  [key: string]: string;
}
interface DividerStateMap {
  [key: string]: string;
}

interface DividerHeightMap {
  [key: string]: string;
}
const DividerDiv = styled(({ ...rest }) => <div {...rest}></div>)`
  && {
    ${(props) => {
      const backgroundColourMap: DividerColourMap = {
        primary: props?.theme.ColorsTypographyPrimary,
        secondary: props?.theme.ColorsTypographySecondary,
        error: props?.theme.ColorsInterfaceVariant45,
        success: props?.theme.ColorsInterfaceVariant65,
        grey: props?.theme.ColorsStrokesDefault,
        focus: props?.theme.ColorsUtilityColorFocus,
        default: props?.theme.ColorsStrokesDefault,
      };
      return {
        background: backgroundColourMap[props?.background],
      };
    }}
    ${(props) => {
      const sizeMap: DividerSizeMap = {
        tab: '58px',
        small: '144px',
        menu: '222px',
        medium: '288px',
        large: '360px',
        fullWidth: '100%',
      };
      return {
        width: sizeMap[props?.size],
      };
    }}

${(props) => {
      const stateMap: DividerStateMap = {
        default:
          'linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(17, 138, 178, 0.49) 50%, rgba(255, 255, 255, 0) 100%)',
        grey: 'linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.49) 50%, rgba(255, 255, 255, 0) 100%)',
      };
      const typeMap: DividerTypeMap = {
        default: props?.theme.ColorsTypographyPrimary,
        tabSmll:
          'linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, white 50%, rgba(255, 255, 255, 0) 100%)',
        tabActive: ['default', 'grey'].includes(props?.state)
          ? stateMap[props?.state]
          : stateMap['default'],
        dotted: 'dotted',
      };
      if (props?.type === 'dotted') {
        const color = props?.theme.ColorsTypographyPrimary || '#000';
        return {
          background: `repeating-linear-gradient(to right, ${color}, ${color} 8px, transparent 8px, transparent 16px)`,
          borderBottom: 'none',
        };
      }
      return {
        background: typeMap[props?.type],
        borderBottom: 'none',
      };
    }}

${(props) => {
      const heightMap: DividerHeightMap = {
        default: '2px',
        thin: '1px',
        mid: '1.5px',
      };
      return {
        height: heightMap[props?.height],
      };
    }}
    left: 0;
    top: 0;
    position: 'absolute';
  }
`;

/**
 * Divider component renders a horizontal line with customizable options.
 *
 * @param {DividerProps} props - The properties for the Divider component.
 * @param {string} [props.background] - The background color of the divider.
 * @param {string} [props.size] - The size of the divider.
 * @param {string} [props.type] - The type of the divider.
 * @param {string} [props.state] - The state of the divider.
 * @param {string} [props.height='default'] - The height of the divider.
 * @param {string} [props.className] - The class name of the divider.
 * @returns {JSX.Element} The rendered Divider component.
 */
export const Divider: React.FC<DividerProps> = ({
  background,
  size,
  type,
  state,
  height = 'default',
  className,
}) => {
  return (
    <DividerDiv
      className={className}
      background={background}
      size={size}
      type={type}
      state={state}
      height={height}
    ></DividerDiv>
  );
};
