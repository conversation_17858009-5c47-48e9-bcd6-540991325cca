import React, { FunctionComponent, useState } from 'react';
import { Control, Controller, RegisterOptions } from 'react-hook-form';
import styled from 'styled-components';
import { Divider } from '../../Dividers';

export type ActivationStates =
  | 'DISABLED'
  | 'ENABLED'
  | 'PENDING'
  | 'EVALUATING';

export interface ActivationButtonProps {
  currentState?: ActivationStates;
  onChange?: (value: ActivationStates) => void;
  name: string;
  rules?: RegisterOptions;
  control?: Control;
  label?: string;
  bottomDivider?: boolean;
}

const ActivationButtonContainer = styled.div`
  display: grid;
  grid-auto-flow: row;
  gap: ${(props) => props.theme.GapSm};
  justify-items: center;
  margin-bottom: 1rem;
`;

const ActivationButtonLabel = styled.div`
  display: grid;
  text-align: center;
`;

const Container = styled.div<{ noOfStates: number }>`
  width: 90%;
  height: 30px;
  background: ${(props) => props.theme.ColorsTypographyPrimary};
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid ${(props) => props.theme.ColorsUtilityColorFocus};
  cursor: pointer;
  display: grid;
  grid-template-columns: ${(props) => `repeat(${props.noOfStates}, auto)`};
  align-items: center;
  position: relative;
  column-gap: ${(props) => props.theme.SpacingXs};
`;

const ToggleBar = styled.div<{ activeState: number; noOfStates: number }>`
  --barWidth: calc(100% / ${(props) => props.noOfStates});
  width: var(--barWidth);
  height: 30px;
  position: absolute;
  background: ${(props) =>
    props.activeState === -1 ? 'none' : props.theme.ColorsUtilityColorFocus};
  transform: translateX(
    calc(${(props) => props.activeState * props.noOfStates} * var(--barWidth))
  );
  transition: transform 0.1s ease-in-out;
`;

const Label = styled.div<{ active: boolean }>`
  color: ${(props) =>
    props.active
      ? props.theme.ColorsIconColorPrimary
      : props.theme.ColorsTypographyInverse};
  background-color: transparent;
  font-size: ${(props) => props.theme.FontSize3}px;
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  display: grid;
  place-items: center;
  height: 100%;
  z-index: 1;
  /* width: calc(100%); */
`;

const DividerDiv = styled.div`
  width: 80%;
`;

/**
 * A button component that allows toggling between three states: 'activate', 'provisional', and 'inactive'.
 * The button shows the current state and changes when clicked. If provided, the onChange callback is triggered.
 *
 * @param {ActivationButtonProps} props - The props containing currentState and optional onChange callback.
 * @returns {JSX.Element} The rendered ActivationButton component.
 */
export const ActivationButton: FunctionComponent<ActivationButtonProps> = ({
  currentState,
  onChange,
  rules,
  name,
  control,
  label,
  bottomDivider,
}) => {
  // Internal state to manage the active state of the button
  const [activeState, setActiveState] = useState(currentState);

  const ActivationStatus = [
    'DISABLED',
    'ENABLED',
    'PENDING',
    // 'EVALUATING',
  ] as const;
  /**
   * Returns the index for the given state.
   * @param {string} state - The state to get the index for: 'activate', 'provisional', or 'inactive'.
   * @returns {number} - The index representing the state.
   */
  const getStateIndex = (state?: ActivationStates): number => {
    switch (state) {
      case 'DISABLED':
        return 0;
      case 'ENABLED':
        return 1;
      case 'PENDING':
        return 2;
      // case 'EVALUATING':
      //   return 3;
      default:
        return -1;
    }
  };

  const ToggleBandLabel = ({
    active,
    onClick,
    idx,
  }: {
    active: boolean;
    onClick: () => void;
    idx: number;
  }) => (
    <Label active={active} onClick={onClick}>
      {ActivationStatus[idx]}
    </Label>
  );

  /**
   * Handles clicking on a label and changes the active state.
   * If onChange is provided, the new state is passed to the callback.
   *
   * @param {string} state - The new state of the button ('activate', 'provisional', or 'inactive').
   */
  const handleClick = (
    state: ActivationStates,
    setFormState?: (value: ActivationStates) => void
  ) => {
    setActiveState(state); // Update internal state
    if (onChange) {
      onChange(state); // Trigger the callback if available
    }
    if (setFormState) {
      setFormState(state);
    }
  };

  const activeIndex = getStateIndex(activeState);

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      defaultValue={currentState}
      render={({ field: { onChange, value } }) => (
        <ActivationButtonContainer>
          {label && <ActivationButtonLabel>{label}</ActivationButtonLabel>}
          <Container noOfStates={ActivationStatus.length}>
            <ToggleBar
              activeState={activeIndex}
              noOfStates={ActivationStatus.length}
            />
            {ActivationStatus.map((state, idx) => (
              <ToggleBandLabel
                key={state}
                active={activeIndex === idx}
                onClick={() => handleClick(state, onChange)}
                idx={idx}
              />
            ))}
          </Container>
          {bottomDivider && (
            <DividerDiv>
              <Divider background="primary" size="fullwidth" height="thin" />
            </DividerDiv>
          )}
        </ActivationButtonContainer>
      )}
    />
  );
};
