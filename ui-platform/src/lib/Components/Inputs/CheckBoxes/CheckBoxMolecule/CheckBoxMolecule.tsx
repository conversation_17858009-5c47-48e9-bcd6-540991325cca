import { FunctionComponent, useEffect, useMemo, useState } from 'react';
import styled, { useTheme } from 'styled-components';
import { LineItem } from '../../../BOQ/LineItemTable/LineItemTable';
import { svgs } from '../../../Icons/svgs';

interface CheckboxProps {
  item?: LineItem;
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  name: string;
  disabled?: boolean;
  label?: string;
  error?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
}

const CheckboxContainer = styled.div<{ checked: boolean }>`
  width: 16px;
  position: relative;
  border-radius: 2px;
  border: 1px solid ${(props) => props?.theme.ColorsStrokesDefault};
  background: ${(props) =>
    props.checked ? props?.theme?.ColorsUtilityColorSuccess : 'transparent'};
  box-sizing: border-box;
  height: 16px;
  display: grid;
  place-items: center;
  z-index: 1;
`;

const CheckIcon = styled.svg<{ checked: boolean }>`
  width: 16px;
  height: 16px;
  /* visibility: ${(props) => (props.checked ? 'visible' : 'hidden')}; */
  position: absolute;
  top: 0;
  left: 0;
`;

const Text = styled.div`
  grid-column: 2;
  text-transform: uppercase;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 0;
  text-wrap: auto;
`;

const Selectionbox = styled(({ ...rest }) => <div {...rest}></div>)`
  /* width: 280px; */
  display: grid;
  grid-template-columns: 16px auto;
  align-items: center;
  padding: 4px 8px;
  box-sizing: border-box;
  gap: 12px;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  cursor: pointer;
`;

/**
 * A checkbox component that can be used to select options.
 *
 * @param {boolean} [checked=false] - The initial value of the checkbox.
 * @param {function} [onChange] - The callback to be called when the checkbox
 *     is clicked. If the checkbox has an onChange handler, it will be
 *     called with the new value of the checkbox.
 * @param {string} name - The name of the checkbox.
 *
 * @example
 * const MyComponent = () => {
 *   const [isChecked, setIsChecked] = useState(false);
 *   const handleChange = (newValue) => {
 *     setIsChecked(newValue);
 *   };
 *   return (
 *     <SelectionBox
 *       checked={isChecked}
 *       onChange={handleChange}
 *       name="My Checkbox"
 *     />
 *   );
 * };
 */
const SelectionBox: FunctionComponent<CheckboxProps> = ({
  checked = false,
  onChange,
  name,
  disabled = false,
  label,
  error,
  'aria-label': ariaLabel,
  'aria-describedby': ariaDescribedBy,
}) => {
  const theme = useTheme();
  const [isChecked, setIsChecked] = useState(checked);
  useEffect(() => {
    setIsChecked(checked);
    return () => setIsChecked(false);
  }, [checked]);

  const colors = {
    notChecked: theme.ColorsControllersDefault,
    checked: theme.ColorsStrokesDefault,
  };

  /**
   * Handles the click event of the SelectionBox. If the SelectionBox has an
   * onChange handler, it will be called with the new value of the checkbox.
   * If the SelectionBox does not have an onChange handler, it will simply
   * toggle the value of the checkbox.
   */
  const handleClick = () => {
    if (disabled) return;

    const newValue = !isChecked;
    setIsChecked(newValue);
    if (onChange) {
      onChange(newValue);
    }
  };

  const displayText = label || name;

  return (
    <div>
      <Selectionbox
        onClick={handleClick}
        style={{
          opacity: disabled ? 0.6 : 1,
          cursor: disabled ? 'not-allowed' : 'pointer',
        }}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedBy}
        role="checkbox"
        aria-checked={isChecked}
        tabIndex={disabled ? -1 : 0}
        onKeyDown={(e: any) => {
          if ((e.key === ' ' || e.key === 'Enter') && !disabled) {
            e.preventDefault();
            handleClick();
          }
        }}
      >
        <CheckboxContainer checked={isChecked}>
          <CheckIcon viewBox={svgs['check'].viewBox} checked={isChecked}>
            <path
              d={svgs['check'].paths[0].d}
              stroke={isChecked ? colors.checked : colors.notChecked}
              strokeWidth={2}
              strokeLinecap={svgs['check'].paths[0].strokeLinecap}
              strokeLinejoin={svgs['check'].paths[0].strokeLinejoin}
            />
          </CheckIcon>
        </CheckboxContainer>
        <Text>{displayText}</Text>
      </Selectionbox>
      {error && (
        <div
          style={{
            color: '#dc2626',
            fontSize: '12px',
            marginTop: '4px',
          }}
        >
          {error}
        </div>
      )}
    </div>
  );
};

export default SelectionBox;
