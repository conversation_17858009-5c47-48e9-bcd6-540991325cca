import { FunctionComponent, useMemo } from 'react';
import {
  Control,
  Controller,
  FieldError,
  RegisterOptions,
} from 'react-hook-form';
import styled from 'styled-components';
import Checkbox from './CheckBoxMolecule/CheckBoxMolecule';

const Multiselectchecklist = styled.div`
  width: 100%;
  position: relative;
  border-radius: 5px;
  box-sizing: border-box;
  height: auto;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  font-family: ${(props) => props.theme.FontFamiliesInter};
  overflow: hidden;
`;

const Property1internal = styled.div`
  display: grid;
  grid-template-rows: auto 1fr auto;
  gap: 10px;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize4}px;
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;

const HeadingHere = styled.div`
  font-weight: ${(props) => props.theme.FontWeightsInter1};
  white-space: nowrap;
`;

const RolesBox = styled.div<{ hideBorder?: boolean }>`
  border-radius: 4px;
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  display: grid;
  padding: 8px;
  overflow: visible;
  ${(props) =>
    props.hideBorder &&
    `
    border: none;
  `}
`;

const Selectionbox = styled.div`
  display: grid;
  /* grid-template-columns: 16px auto; */
  align-items: center;
  border-radius: 6px;
  padding: 4px 8px;
  gap: 12px;
`;

const Checkboxlist = styled.div<{ maxColumns?: number }>`
  --minWidth: ${(props) => (props.maxColumns ? props.maxColumns * 180 : 540)}px;
  display: grid;
  gap: 12px;
  /* grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); */
  /* grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); */
  grid-template-columns: repeat(${(props) => props.maxColumns}, 1fr);

  @media (min-width: var(--minWidth)) {
    grid-template-columns: repeat(${(props) => props.maxColumns}, 1fr);
  }
`;

const SubtextHere = styled.div`
  color: ${(props) => props.theme.ColorsTypographySecondary};
  white-space: nowrap;
`;

const Instruction = styled.div<{ error?: boolean }>`
  margin-top: ${(props) => props.theme.SpacingXs};
  color: ${({ error, theme }) =>
    error ? theme.ColorsUtilityColorError : theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize2}px;
  text-align: start;
`;

const StyledInstuctionContainer = styled.div`
  margin: unset;
`;

interface MultiSelectCheckListProps {
  items: any[]; // Updated to accept any data structure from store
  heading: string;
  subtext: string;
  checkedItems?: any[]; // Will store the actual `valueProp` values
  onChange: (updatedCheckedItems: any[]) => void;
  control?: Control;
  name: string;
  valueProp: string; // Property to be used as the value in the checklist
  labelProp: string; // Property to be used as the label in the checklist
  rules?: RegisterOptions;
  maxColumns?: number;
  error?: FieldError | null;
  hideBorder?: boolean;
}

/**
 * Renders a multi-select checklist component with support for dynamic value and label props.
 *
 * @param {MultiSelectCheckListProps} props - The component props.
 * @param {any[]} props.items - The list of items to display in the checklist.
 * @param {string} props.heading - The heading of the checklist.
 * @param {string} props.subtext - The subtext of the checklist.
 * @param {any[]} props.checkedItems - The currently checked items (controlled state, using `valueProp` values).
 * @param {(checkedItems: any[]) => void} props.onChange - The handler to call when the checked items change.
 * @param {string} props.valueProp - The property to use as the value in the checklist.
 * @param {string} props.labelProp - The property to use as the label in the checklist.
 * @return {JSX.Element} The rendered multi-select checklist component.
 */
export const MultiSelectCheckList: FunctionComponent<
  MultiSelectCheckListProps
> = ({
  items,
  name,
  heading,
  subtext,
  checkedItems,
  onChange,
  control,
  rules,
  maxColumns,
  valueProp,
  labelProp,
  error,
  hideBorder,
}) => {
  const handleCheckboxChange = (
    item: any,
    fieldOnChange: (value: any) => void,
    initialVal: any[]
  ) => {
    const itemValue = item[valueProp];
    let updatedCheckedItems: any[] = initialVal;

    if (updatedCheckedItems.includes(itemValue)) {
      updatedCheckedItems = updatedCheckedItems.filter(
        (checkedItem) => checkedItem !== itemValue
      );
    } else {
      updatedCheckedItems = [...updatedCheckedItems, itemValue];
    }

    fieldOnChange(updatedCheckedItems);
    onChange(updatedCheckedItems);
  };

  const checked = useMemo(() => checkedItems, [checkedItems]);
  console.log({ checkedItems, checked });

  return (
    <Multiselectchecklist>
      <Property1internal>
        <HeadingHere>{heading}</HeadingHere>
        <RolesBox hideBorder={hideBorder}>
          <Checkboxlist maxColumns={maxColumns}>
            {items.map((item) => (
              <Selectionbox key={item[valueProp]}>
                <Controller
                  control={control}
                  name={name}
                  rules={rules}
                  defaultValue={checked}
                  render={({
                    field: { onChange, value },
                    fieldState: { error: fieldError = error },
                  }) => (
                    <Checkbox
                      name={item[labelProp]}
                      onChange={() => {
                        handleCheckboxChange(item, onChange, value);
                      }}
                      checked={
                        Array.isArray(checked)
                          ? checked?.includes(item[valueProp]) ||
                            value.includes(item[valueProp])
                          : false
                      }
                    />
                  )}
                />
                {/* Display the label from labelProp */}
              </Selectionbox>
            ))}
          </Checkboxlist>
        </RolesBox>
        {subtext && (
          <StyledInstuctionContainer>
            {<Instruction>{subtext}</Instruction>}
          </StyledInstuctionContainer>
        )}
      </Property1internal>
    </Multiselectchecklist>
  );
};
