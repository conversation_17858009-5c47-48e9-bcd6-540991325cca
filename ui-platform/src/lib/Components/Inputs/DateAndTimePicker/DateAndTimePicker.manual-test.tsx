import React from 'react';
import { useForm } from 'react-hook-form';
import { ThemeProvider } from 'styled-components';
import { DateAndTimePicker } from './DateAndTimePicker';

// Simple theme for testing
const testTheme = {
  SpacingXs: '4px',
  SpacingSm: '8px',
  SpacingLg: '16px',
  RadiusXs: '4px',
  FontFamiliesInter: 'Inter, sans-serif',
  FontSize2: 12,
  FontSize3: 14,
  ColorsTypographyPrimary: '#000000',
  ColorsTypographySecondary: '#666666',
  ColorsInputsPrimary: '#ffffff',
  ColorsInputsNonEditable: '#f5f5f5',
  ColorsStrokesFocus: '#0066cc',
  ColorsStrokesGrey: '#cccccc',
  ColorsUtilityColorError: '#ff0000',
  ColorsControllersDefault: '#999999',
  ColorsOverlaySurfaceOverlay: '#ffffff',
  ColorsIconColorTertiary: '#999999',
  ColorsButtonColorModuleActionsPrimary: '#ffffff',
  ColorsUtilityColorFocus: '#0066cc',
};

// Manual test component to verify functionality
export const DateAndTimePickerManualTest = () => {
  const { control, watch, setValue } = useForm({
    defaultValues: {
      datetime: '',
      datetimeWithDefault: '2024-12-25T14:30:00.000Z',
    },
  });

  const watchedDateTime = watch('datetime');
  const watchedDateTimeWithDefault = watch('datetimeWithDefault');

  return (
    <ThemeProvider theme={testTheme}>
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h2>DateAndTimePicker Manual Test</h2>
        
        <div style={{ marginBottom: '30px' }}>
          <h3>Basic DateAndTimePicker</h3>
          <DateAndTimePicker
            name="datetime"
            control={control}
            label="Select Date and Time"
            instructions="Choose both date and time"
          />
          <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#f0f0f0' }}>
            <strong>Form Value:</strong> {watchedDateTime || 'No value selected'}
          </div>
        </div>

        <div style={{ marginBottom: '30px' }}>
          <h3>DateAndTimePicker with Default Value</h3>
          <DateAndTimePicker
            name="datetimeWithDefault"
            control={control}
            label="Appointment Date & Time"
            value="2024-12-25T14:30:00.000Z"
            validMinutes={[0, 15, 30, 45]}
          />
          <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#f0f0f0' }}>
            <strong>Form Value:</strong> {watchedDateTimeWithDefault || 'No value selected'}
          </div>
        </div>

        <div style={{ marginBottom: '30px' }}>
          <h3>DateAndTimePicker with Icon</h3>
          <DateAndTimePicker
            name="datetime"
            control={control}
            label="Event Date & Time"
            icon="calendar"
            iconPosition="left"
          />
        </div>

        <div style={{ marginBottom: '30px' }}>
          <h3>Test Controls</h3>
          <button 
            onClick={() => setValue('datetime', '2024-06-15T09:00:00.000Z')}
            style={{ marginRight: '10px', padding: '5px 10px' }}
          >
            Set DateTime to June 15, 2024 09:00
          </button>
          <button 
            onClick={() => setValue('datetime', '')}
            style={{ padding: '5px 10px' }}
          >
            Clear DateTime
          </button>
        </div>

        <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#e8f4f8', borderRadius: '4px' }}>
          <h4>✅ Expected Behavior:</h4>
          <ul>
            <li>Two inputs side by side (date on left, time on right)</li>
            <li>Date input shows formatted date when selected</li>
            <li>Time input shows formatted time when selected</li>
            <li>Clicking date input opens date picker dropdown</li>
            <li>Clicking time input opens time picker dropdown</li>
            <li>Form value updates as single ISO date string</li>
            <li>Default values are parsed and displayed correctly</li>
            <li>Icons appear only on date input</li>
          </ul>
        </div>
      </div>
    </ThemeProvider>
  );
};

export default DateAndTimePickerManualTest;
