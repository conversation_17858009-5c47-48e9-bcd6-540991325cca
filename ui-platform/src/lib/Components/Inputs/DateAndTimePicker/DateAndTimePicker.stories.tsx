import type { Meta, StoryObj } from '@storybook/react';
import { useForm } from 'react-hook-form';
import { DateAndTimePicker } from './DateAndTimePicker';

const meta: Meta<typeof DateAndTimePicker> = {
  component: DateAndTimePicker,
  title: 'Components/Inputs/DateAndTimePicker',
  parameters: {
    docs: {
      description: {
        component:
          'A combined date and time picker component with side-by-side inputs that manages a single ISO date string value.',
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof DateAndTimePicker>;

// Wrapper component to demonstrate form integration
const FormWrapper = ({
  children,
  defaultValue,
}: {
  children: React.ReactNode;
  defaultValue?: string;
}) => {
  const { control, watch } = useForm({
    defaultValues: {
      datetime: defaultValue || '',
    },
  });

  const watchedValue = watch('datetime');

  return (
    <div>
      {children}
      <div
        style={{
          marginTop: '20px',
          padding: '10px',
          backgroundColor: '#f5f5f5',
          borderRadius: '4px',
        }}
      >
        <strong>Form Value:</strong> {watchedValue || 'No value selected'}
      </div>
    </div>
  );
};

export const Default: Story = {
  render: (args) => (
    <FormWrapper>
      <DateAndTimePicker {...args} />
    </FormWrapper>
  ),
  args: {
    name: 'datetime',
    label: 'Select Date and Time',
    placeholder: 'Choose date and time',
    instructions: 'Select both date and time from the dropdown',
  },
};

export const WithDefaultValue: Story = {
  render: (args) => (
    <FormWrapper defaultValue="2024-12-25T14:30:00.000Z">
      <DateAndTimePicker {...args} />
    </FormWrapper>
  ),
  args: {
    name: 'datetime',
    label: 'Appointment Date & Time',
    placeholder: 'Choose appointment date and time',
    value: '2024-12-25T14:30:00.000Z',
  },
};

export const WithIcon: Story = {
  render: (args) => (
    <FormWrapper>
      <DateAndTimePicker {...args} />
    </FormWrapper>
  ),
  args: {
    name: 'datetime',
    label: 'Event Date & Time',
    placeholder: 'Select event date and time',
    icon: 'alarm-clock',
    iconPosition: 'left',
  },
};

export const WithValidation: Story = {
  render: (args) => (
    <FormWrapper>
      <DateAndTimePicker {...args} />
    </FormWrapper>
  ),
  args: {
    name: 'datetime',
    label: 'Required Date & Time',
    placeholder: 'This field is required',
    rules: { required: 'Date and time are required' },
    fieldError: {
      type: 'required',
      message: 'Date and time are required',
    },
  },
};

export const CustomTimeIntervals: Story = {
  render: (args) => (
    <FormWrapper>
      <DateAndTimePicker {...args} />
    </FormWrapper>
  ),
  args: {
    name: 'datetime',
    label: 'Meeting Time (30-min intervals)',
    placeholder: 'Select meeting time',
    validMinutes: [0, 30],
    instructions: 'Time can only be selected in 30-minute intervals',
  },
};

export const FutureOnly: Story = {
  render: (args) => (
    <FormWrapper>
      <DateAndTimePicker {...args} />
    </FormWrapper>
  ),
  args: {
    name: 'datetime',
    label: 'Future Date & Time Only',
    placeholder: 'Select future date and time',
    disablePast: true,
    disableFuture: false,
    instructions: 'Only future dates can be selected',
  },
};

export const WithDisabledDates: Story = {
  render: (args) => (
    <FormWrapper>
      <DateAndTimePicker {...args} />
    </FormWrapper>
  ),
  args: {
    name: 'datetime',
    label: 'Booking Date & Time',
    placeholder: 'Select booking date and time',
    disabledDates: ['2024-12-25', '2024-01-01'],
    instructions: 'Holidays are disabled',
  },
};

export const DisplayOnly: Story = {
  render: (args) => (
    <FormWrapper defaultValue="2024-06-15T09:00:00.000Z">
      <DateAndTimePicker {...args} />
    </FormWrapper>
  ),
  args: {
    name: 'datetime',
    label: 'Read-only Date & Time',
    value: '2024-06-15T09:00:00.000Z',
    state: 'display-only',
    instructions: 'This field is read-only',
  },
};
