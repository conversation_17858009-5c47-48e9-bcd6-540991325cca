import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { useForm } from 'react-hook-form';
import { ThemeProvider } from 'styled-components';
import { describe, expect, it, vi } from 'vitest';
import { DateAndTimePicker } from './DateAndTimePicker';

// Import the actual theme for testing
import * as desktopDark from '../../../themes/tokens/outputs/desktop-dark';
const mockTheme = desktopDark;

// Test wrapper component
const TestWrapper = ({
  children,
  defaultValue,
}: {
  children:
    | React.ReactNode
    | (({ control }: { control: any }) => React.ReactNode);
  defaultValue?: any;
}) => {
  const { control } = useForm({
    defaultValues: {
      datetime: defaultValue || '',
    },
  });

  return (
    <ThemeProvider theme={mockTheme}>
      {typeof children === 'function' ? children({ control }) : children}
    </ThemeProvider>
  );
};

describe('DateAndTimePicker', () => {
  it('renders with basic props', () => {
    render(
      <TestWrapper>
        {({ control }: { control: any }) =>
          (
            <DateAndTimePicker
              name="datetime"
              control={control}
              label="Test Date Time"
              placeholder="Select date and time"
            />
          ) as React.ReactNode
        }
      </TestWrapper>
    );

    expect(screen.getByText('Test Date Time')).toBeInTheDocument();
    expect(screen.getByTestId('date-time-picker')).toBeInTheDocument();
  });

  it('displays placeholder when no value is set', () => {
    render(
      <TestWrapper>
        {({ control }: any) => (
          <DateAndTimePicker
            name="datetime"
            control={control}
            placeholder="Select date and time"
          />
        )}
      </TestWrapper>
    );

    const dateInput = screen.getByPlaceholderText('Select date');
    const timeInput = screen.getByPlaceholderText('Select time');
    expect(dateInput).toBeInTheDocument();
    expect(timeInput).toBeInTheDocument();
  });

  it('opens date dropdown when date input clicked', async () => {
    render(
      <TestWrapper>
        {({ control }: any) => (
          <DateAndTimePicker
            name="datetime"
            control={control}
            placeholder="Select date and time"
          />
        )}
      </TestWrapper>
    );

    const dateInputContainer = screen.getByTestId('date-picker-container');
    fireEvent.click(dateInputContainer);

    await waitFor(() => {
      expect(screen.getByTestId('date-picker-dropdown')).toBeInTheDocument();
      // Check for calendar content instead of specific test ID
      expect(screen.getByText('June 2025')).toBeInTheDocument();
    });
  });

  it('opens time dropdown when time input clicked', async () => {
    render(
      <TestWrapper>
        {({ control }: any) => (
          <DateAndTimePicker
            name="datetime"
            control={control}
            placeholder="Select date and time"
          />
        )}
      </TestWrapper>
    );

    const timeInputContainer = screen.getByTestId('time-picker-container');
    fireEvent.click(timeInputContainer);

    await waitFor(() => {
      expect(screen.getByTestId('time-picker-dropdown')).toBeInTheDocument();
      // The TimePickerV2 component doesn't have a test ID, so we check for its content
      expect(screen.getAllByText('00').length).toBeGreaterThan(0); // Hour and minute options
    });
  });

  it('displays error message when provided', () => {
    const errorMessage = 'This field is required';

    render(
      <TestWrapper>
        {({ control }: any) => (
          <DateAndTimePicker
            name="datetime"
            control={control}
            fieldError={{
              type: 'required',
              message: errorMessage,
            }}
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('displays instructions when provided', () => {
    const instructions = 'Please select a date and time';

    render(
      <TestWrapper>
        {({ control }: any) => (
          <DateAndTimePicker
            name="datetime"
            control={control}
            instructions={instructions}
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByText(instructions)).toBeInTheDocument();
  });

  it('renders with icon when provided', () => {
    render(
      <TestWrapper>
        {({ control }: any) => (
          <DateAndTimePicker
            name="datetime"
            control={control}
            icon="alarm-clock"
            iconPosition="left"
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByTestId('date-picker-left-icon')).toBeInTheDocument();
    expect(screen.getByTestId('alarm-clock')).toBeInTheDocument();
  });

  it('calls onChange when provided', () => {
    const mockOnChange = vi.fn();

    render(
      <TestWrapper>
        {({ control }: any) => (
          <DateAndTimePicker
            name="datetime"
            control={control}
            onChange={mockOnChange}
          />
        )}
      </TestWrapper>
    );

    // This test would need more complex interaction simulation
    // to actually trigger the onChange callback
    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it('handles default value correctly', () => {
    const defaultValue = '2024-06-15T14:30:00.000Z';

    render(
      <TestWrapper defaultValue={defaultValue}>
        {({ control }: any) => (
          <DateAndTimePicker
            name="datetime"
            control={control}
            value={defaultValue}
          />
        )}
      </TestWrapper>
    );

    // The component should parse and display the default value
    expect(screen.getByTestId('date-time-picker')).toBeInTheDocument();
  });

  it('applies display-only state correctly', () => {
    render(
      <TestWrapper>
        {({ control }: any) => (
          <DateAndTimePicker
            name="datetime"
            control={control}
            state="display-only"
          />
        )}
      </TestWrapper>
    );

    const dateContainer = screen.getByTestId('date-picker-container');
    const timeContainer = screen.getByTestId('time-picker-container');
    expect(dateContainer).toBeInTheDocument();
    expect(timeContainer).toBeInTheDocument();
  });
});
