import React, { useEffect, useState } from 'react';
import styled from 'styled-components';

export type DocumentPreviewProps = {
  documentUrl?: string;
  isBase64?: boolean;
  className?: string;
};

const Container = styled.div`
  display: grid;
  place-items: center;
  padding: 10px;
  max-width: 100%;
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
`;

const PDFViewer = styled.iframe`
  width: 100%;
  height: 100%;
  border: none;
`;

const DocumentPreview: React.FC<DocumentPreviewProps> = ({
  documentUrl,
  isBase64 = false,
  className,
}) => {
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    setError(false); // Reset error state when document URL changes
  }, [documentUrl]);

  const src = isBase64
    ? `data:application/pdf;base64,${documentUrl}`
    : `${documentUrl}#toolbar=1`;

  return (
    <Container className={className}>
      {error ? (
        <p>Error loading the document. Please try again later.</p>
      ) : (
        <PDFViewer className={className} src={src} title="PDF Document" />
      )}
    </Container>
  );
};

export default DocumentPreview;
