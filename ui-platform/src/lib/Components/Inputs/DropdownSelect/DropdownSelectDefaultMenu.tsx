import React, { forwardRef, useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { IconTypes, svgs } from '../../Icons';
import SelectionBox from '../CheckBoxes/CheckBoxMolecule/CheckBoxMolecule';

export interface Item {
  label: string;
  value: any;
  // id: number;
  // description: string;
  // quantity?: number;
  // compulsoryItem?: boolean;
  // unitPrice?: number;
  iconName?: IconTypes;
  dropdownOptions?: Item[];
}

const DropdownSelectDefaultMenu = styled.div<{
  width?: number;
  isChild: boolean;
}>`
  position: absolute;
  color: ${(props) => props.theme.ColorsIconColorTertiary};
  /* top: 45px; */
  right: 0px;
  left: 0px;
  border-radius: ${(props) => props.theme.RadiusXs};
  background-color: ${(props) => props.theme.ColorsOverlaySurfaceOverlay};
  border: 1px solid ${(props) => props.theme.ColorsStrokesFocus};
  box-sizing: border-box;
  z-index: 10;
  //   width: 60%;
  width: 100%;
  // padding-bottom: 5px;
  //   overflow: visible;

  div {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
    // display: grid;
    align-items: center;
    grid-template-columns: auto 1fr;
    // align-items: self-start;
    // padding: 0.8rem 0.7rem;
    // margin: 10px 18px;
    padding: ${(props) => props.theme.SpacingXs};
    outline: none;
    cursor: pointer;
    white-space: nowrap;
    // overflow: visible;
    text-overflow: ellipsis;
  }

  ${(props) =>
    props.isChild &&
    `
    // position: relative;
    top: 0;
    position: absolute;
    z-index: 11;
    // overflow: visible;
    right: 0;
    left: unset;
    translate: 100%;
`}
`;

const FilterContainer = styled.div`
  display: grid;
  align-items: center;
  /* width: 100%; */
  position: relative;
  text-align: start;
`;

const FilterInput = styled.input<{ width: number }>`
  border: none;
  background-color: unset;
  transition: color 1s ease, background-color 1s ease;
  cursor: text;
  // width: 100%;
  padding: ${(props) => props.theme.SpacingSm};
  outline: none;
  color: #a5a5a5;
`;

const SearchIcon = styled.svg`
  width: 24px;
  height: 24px;
  position: absolute;
  right: 8px;
  z-index: 9;
  cursor: pointer;
`;

const Content = styled.div`
  max-height: 150px;
  overflow-y: auto;
`;

// const SelectionBox = styled.input.attrs({ type: 'checkbox' })`
//   margin-right: 8px;
// `;

const SecondDropdownMenu = styled(DropdownSelectDefaultMenu)`
  position: absolute;
  top: 0px;
  left: 100%;
  //   margin-left: 8px;
  //   width: 150px;
  z-index: 11;
  //   overflow: visible;
  padding: 8px;
  gap: 4px;
  cursor: pointer;
`;

const DropdownIcon = styled(SearchIcon)<{ isSelected: boolean }>`
  transform: ${({ isSelected }) => (isSelected ? 'rotate(180deg)' : 'none')};
`;
interface Props {
  items: Item[];
  selectedItems: Item[];
  multiSelect?: boolean;
  isSelected?: boolean;
  isChild?: boolean;
  handleItemClick: (item: Item) => void;
  filterPlaceholder?: string;
  searchIcon: any;
  filter: string;
  inputWidth: number;
  handleFilterChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  labelProp: string;
  valueProp: string;
  notSearchable?: boolean;
}

export const DropdownDefaultMenuComponent = forwardRef(
  (props: Props, ref: React.ForwardedRef<HTMLDivElement>) => {
    const {
      items,
      multiSelect = false,
      handleItemClick,
      handleFilterChange,
      filter,
      selectedItems,
      isSelected = false,
      filterPlaceholder = '|Search',
      searchIcon,
      isChild = false,
      inputWidth,
      className,
      labelProp,
      valueProp,
      notSearchable,
    } = props;

    const [itemId, setItemId] = useState(-1);

    /**
     * Handles the selection of a menu item. If the item has a dropdown
     * option, it will toggle the id of the item in the state.
     * @param {object} item - The item to be selected.
     */
    const onItemSelect = (item: any) => {
      if (item.dropdownOptions) {
        console.log('item inside:', item);

        setItemId((prev) => {
          return prev === item[valueProp] ? -1 : item[valueProp];
        });
      }
      handleItemClick(item);
    };

    const filterInputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
      if (filterInputRef && filterInputRef?.current?.focus) {
        filterInputRef.current.focus();
      }
    }, []);

    return (
      <DropdownSelectDefaultMenu
        width={300}
        className="dropdown__menu"
        ref={ref}
        isChild={isChild}
      >
        {!notSearchable && (
          <FilterContainer className="dropdown__menu-filter">
            <FilterInput
              type="text"
              width={inputWidth}
              value={filter}
              onChange={handleFilterChange}
              placeholder={filterPlaceholder}
            />
            <SearchIcon viewBox={searchIcon.viewBox}>
              <path
                d={searchIcon.paths[0].d}
                stroke={searchIcon.paths[0].stroke}
                strokeLinecap={searchIcon.paths[0].strokeLinecap}
                strokeLinejoin={searchIcon.paths[0].strokeLinejoin}
                fill={searchIcon.paths[0].fill}
              />
            </SearchIcon>
          </FilterContainer>
        )}
        <Content
          className="dropdown__menu-content"
          data-testid="dropdown-menu-content"
        >
          {items.map((item: any, index) => {
            const icon = item.iconName
              ? svgs[item.iconName as IconTypes]
              : undefined;
            return multiSelect ? (
              <SelectionBox
                key={index}
                checked={
                  selectedItems?.length > 0
                    ? selectedItems?.some((i: any) => i === item[valueProp])
                    : false
                }
                name={item[labelProp]}
                onChange={() => handleItemClick(item)}
              />
            ) : (
              <FilterContainer key={index}>
                <div
                  className="dropdown__menu-item"
                  onClick={() => onItemSelect(item)}
                  // className={item.compulsoryItem ? 'compulsory' : ''}  // TODO
                >
                  {item[labelProp]}
                  {item.dropdownOptions && (
                    <DropdownIcon
                      isSelected={itemId === item[valueProp]}
                      viewBox={svgs['chevron-right'].viewBox}
                    >
                      <path
                        d={svgs['chevron-right'].paths[0].d}
                        stroke={
                          isSelected
                            ? '#c4c4c4'
                            : svgs['chevron-right'].paths[0].stroke
                        }
                        strokeLinecap={
                          svgs['chevron-right'].paths[0].strokeLinecap
                        }
                        strokeLinejoin={
                          svgs['chevron-right'].paths[0].strokeLinejoin
                        }
                        fill={svgs['chevron-right'].paths[0].fill}
                      />
                    </DropdownIcon>
                  )}
                  {!item.dropdownOptions && icon && (
                    <SearchIcon viewBox={icon.viewBox}>
                      <path
                        d={icon.paths[0].d}
                        stroke={icon.paths[0].stroke}
                        strokeLinecap={(icon.paths[0] as any).strokeLinecap}
                        strokeLinejoin={(icon.paths[0] as any).strokeLinejoin}
                        fill={icon.paths[0].fill}
                      />
                    </SearchIcon>
                  )}
                </div>
                {itemId === item[valueProp] && (
                  //   <SecondDropdownMenu width={100} isChild={true}>
                  <DropdownDefaultMenuComponent
                    isChild
                    inputWidth={100}
                    ref={ref}
                    items={item.dropdownOptions || []}
                    selectedItems={item.dropdownOptions || []}
                    handleItemClick={handleItemClick}
                    handleFilterChange={handleFilterChange}
                    searchIcon={svgs['search-sm']}
                    filter={filter}
                    labelProp={labelProp}
                    valueProp={valueProp}
                  />
                  //   </SecondDropdownMenu>
                )}
              </FilterContainer>
            );
          })}
        </Content>
      </DropdownSelectDefaultMenu>
    );
  }
);
