import type { Meta, StoryObj } from '@storybook/react';
import { useForm } from 'react-hook-form';
import { action } from '@storybook/addon-actions';
import {
  DynamicActivationButton,
  ActivationButtonV2 as ActivationButton,
  createSimpleStates,
  DEFAULT_ACTIVATION_STATES,
} from './DynamicActivationButton';

// Wrapper component to handle form context
const StoryWrapper = ({
  children,
  defaultValues = {},
}: {
  children: React.ReactNode;
  defaultValues?: any;
}) => {
  const { control, watch } = useForm({ defaultValues });
  const watchedValues = watch();

  return (
    <div style={{ padding: '20px', minHeight: '200px' }}>
      <div style={{ marginBottom: '20px' }}>{children}</div>
      <div
        style={{
          padding: '10px',
          backgroundColor: '#f5f5f5',
          borderRadius: '4px',
          fontSize: '12px',
          fontFamily: 'monospace',
        }}
      >
        <strong>Form Values:</strong>
        <pre>{JSON.stringify(watchedValues, null, 2)}</pre>
      </div>
    </div>
  );
};

const meta: Meta<typeof DynamicActivationButton> = {
  title: 'Components/DynamicActivationButton',
  component: DynamicActivationButton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A flexible toggle button component that works with object arrays and allows custom property mapping for values and labels.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    currentState: {
      description: 'The current selected state value',
      control: 'text',
    },
    name: {
      description: 'The name attribute for the form field',
      control: 'text',
    },
    label: {
      description: 'Optional label displayed above the toggle button',
      control: 'text',
    },
    bottomDivider: {
      description: 'Whether to show a divider below the component',
      control: 'boolean',
    },
    states: {
      description: 'Array of state objects to choose from',
      control: 'object',
    },
    valueProp: {
      description: 'Property name to use as the form value',
      control: 'text',
    },
    nameProp: {
      description: 'Property name to use as the display label',
      control: 'text',
    },
    onChange: {
      description: 'Callback function called when state changes',
      action: 'changed',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample data for stories
const priorityStates = [
  { value: 'low', label: 'Low Priority', color: '#green' },
  { value: 'medium', label: 'Medium Priority', color: '#orange' },
  { value: 'high', label: 'High Priority', color: '#red' },
];

const environmentStates = [
  { envId: 'dev', environmentName: 'Development', isActive: true },
  { envId: 'staging', environmentName: 'Staging', isActive: false },
  { envId: 'prod', environmentName: 'Production', isActive: false },
];

const statusStates = [
  { id: 'draft', name: 'Draft', description: 'Work in progress' },
  { id: 'review', name: 'Under Review', description: 'Pending approval' },
  { id: 'published', name: 'Published', description: 'Live and active' },
  { id: 'archived', name: 'Archived', description: 'No longer active' },
];

// Basic story with simple value/label structure
export const Default: Story = {
  args: {
    name: 'priority',
    states: priorityStates,
    valueProp: 'value',
    nameProp: 'label',
    currentState: 'medium',
    label: 'Select Priority Level',
    bottomDivider: false,
    onChange: action('state-changed'),
  },
  render: (args) => {
    const { control } = useForm({
      defaultValues: { [args.name]: args.currentState },
    });

    return (
      <StoryWrapper defaultValues={{ [args.name]: args.currentState }}>
        <DynamicActivationButton {...args} control={control} />
      </StoryWrapper>
    );
  },
};

// Story with custom object structure
export const CustomObjectStructure: Story = {
  args: {
    name: 'environment',
    states: environmentStates,
    valueProp: 'envId',
    nameProp: 'environmentName',
    currentState: 'staging',
    label: 'Deployment Environment',
    bottomDivider: true,
    onChange: action('environment-changed'),
  },
  render: (args) => {
    const { control } = useForm({
      defaultValues: { [args.name]: args.currentState },
    });

    return (
      <StoryWrapper defaultValues={{ [args.name]: args.currentState }}>
        <DynamicActivationButton {...args} control={control} />
      </StoryWrapper>
    );
  },
};

// Story with four states
export const FourStates: Story = {
  args: {
    name: 'status',
    states: statusStates,
    valueProp: 'id',
    nameProp: 'name',
    currentState: 'review',
    label: 'Document Status',
    bottomDivider: false,
    onChange: action('status-changed'),
  },
  render: (args) => {
    const { control } = useForm({
      defaultValues: { [args.name]: args.currentState },
    });

    return (
      <StoryWrapper defaultValues={{ [args.name]: args.currentState }}>
        <DynamicActivationButton {...args} control={control} />
      </StoryWrapper>
    );
  },
};

// Story using the convenience ActivationButton component
export const ConvenienceComponent: Story = {
  render: () => {
    const { control } = useForm({
      defaultValues: { activation: 'ENABLED' },
    });

    return (
      <StoryWrapper defaultValues={{ activation: 'ENABLED' }}>
        <ActivationButton
          name="activation"
          control={control}
          currentState="ENABLED"
          label="System Activation Status"
          bottomDivider={true}
          onChange={action('activation-changed')}
        />
      </StoryWrapper>
    );
  },
};

// Story with simple states created using helper function
export const WithSimpleStatesHelper: Story = {
  render: () => {
    const simpleStates = createSimpleStates(
      ['draft', 'review', 'published'],
      (value) => value.charAt(0).toUpperCase() + value.slice(1)
    );

    const { control } = useForm({
      defaultValues: { documentStatus: 'draft' },
    });

    return (
      <StoryWrapper defaultValues={{ documentStatus: 'draft' }}>
        <DynamicActivationButton
          name="documentStatus"
          control={control}
          states={simpleStates}
          valueProp="value"
          nameProp="label"
          currentState="draft"
          label="Document Status (Using Helper)"
          onChange={action('document-status-changed')}
        />
      </StoryWrapper>
    );
  },
};

// Story without label
export const WithoutLabel: Story = {
  args: {
    name: 'mode',
    states: [
      { mode: 'light', display: 'Light' },
      { mode: 'dark', display: 'Dark' },
      { mode: 'auto', display: 'Auto' },
    ],
    valueProp: 'mode',
    nameProp: 'display',
    currentState: 'auto',
    bottomDivider: false,
    onChange: action('mode-changed'),
  },
  render: (args) => {
    const { control } = useForm({
      defaultValues: { [args.name]: args.currentState },
    });

    return (
      <StoryWrapper defaultValues={{ [args.name]: args.currentState }}>
        <DynamicActivationButton {...args} control={control} />
      </StoryWrapper>
    );
  },
};

// Story with numeric values
export const WithNumericValues: Story = {
  args: {
    name: 'speed',
    states: [
      { level: 1, description: 'Slow' },
      { level: 2, description: 'Normal' },
      { level: 3, description: 'Fast' },
      { level: 4, description: 'Turbo' },
    ],
    valueProp: 'level',
    nameProp: 'description',
    currentState: 2,
    label: 'Processing Speed',
    bottomDivider: true,
    onChange: action('speed-changed'),
  },
  render: (args) => {
    const { control } = useForm({
      defaultValues: { [args.name]: args.currentState },
    });

    return (
      <StoryWrapper defaultValues={{ [args.name]: args.currentState }}>
        <DynamicActivationButton {...args} control={control} />
      </StoryWrapper>
    );
  },
};

// Story showing multiple components
export const MultipleComponents: Story = {
  render: () => {
    const { control } = useForm({
      defaultValues: {
        priority: 'high',
        environment: 'dev',
        activation: 'ENABLED',
      },
    });

    return (
      <StoryWrapper
        defaultValues={{
          priority: 'high',
          environment: 'dev',
          activation: 'ENABLED',
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
          <DynamicActivationButton
            name="priority"
            control={control}
            states={priorityStates}
            valueProp="value"
            nameProp="label"
            currentState="high"
            label="Priority Level"
            bottomDivider={true}
            onChange={action('priority-changed')}
          />

          <DynamicActivationButton
            name="environment"
            control={control}
            states={environmentStates}
            valueProp="envId"
            nameProp="environmentName"
            currentState="dev"
            label="Environment"
            bottomDivider={true}
            onChange={action('environment-changed')}
          />

          <ActivationButton
            name="activation"
            control={control}
            currentState="ENABLED"
            label="System Status"
            onChange={action('activation-changed')}
          />
        </div>
      </StoryWrapper>
    );
  },
};

// Interactive playground story
export const Playground: Story = {
  args: {
    name: 'playground',
    states: [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
      { value: 'option3', label: 'Option 3' },
    ],
    valueProp: 'value',
    nameProp: 'label',
    currentState: 'option2',
    label: 'Interactive Playground',
    bottomDivider: true,
    onChange: action('playground-changed'),
  },
  render: (args) => {
    const { control } = useForm({
      defaultValues: { [args.name]: args.currentState },
    });

    return (
      <StoryWrapper defaultValues={{ [args.name]: args.currentState }}>
        <DynamicActivationButton {...args} control={control} />
      </StoryWrapper>
    );
  },
};
