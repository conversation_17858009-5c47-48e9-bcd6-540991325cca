import React, { FunctionComponent, useEffect, useState } from 'react';
import { Control, Controller, RegisterOptions } from 'react-hook-form';
import styled from 'styled-components';
import { Divider } from '../../Dividers';

// Generic interface that works with object arrays
export interface DynamicActivationButtonProps<
  T extends Record<string, any>,
  V extends keyof T,
  N extends keyof T
> {
  currentState?: T[V];
  onChange?: (value: T[V]) => void;
  name: string;
  rules?: RegisterOptions;
  control?: Control;
  label?: string;
  bottomDivider?: boolean;
  states: T[] | ReadonlyArray<T>;
  valueProp: V;
  nameProp: N;
}

const ActivationButtonContainer = styled.div`
  display: grid;
  grid-auto-flow: row;
  gap: ${(props) => props.theme.GapSm};
  justify-items: center;
  margin-bottom: 1rem;
`;

const ActivationButtonLabel = styled.div`
  display: grid;
  text-align: center;
`;

const Container = styled.div<{ noOfStates: number }>`
  width: 90%;
  height: 30px;
  background: ${(props) => props.theme.ColorsTypographyPrimary};
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid ${(props) => props.theme.ColorsUtilityColorFocus};
  cursor: pointer;
  display: grid;
  grid-template-columns: ${(props) => `repeat(${props.noOfStates}, 1fr)`};
  align-items: center;
  position: relative;
  column-gap: ${(props) => props.theme.SpacingXs};
`;

const ToggleBar = styled.div<{ activeState: number; noOfStates: number }>`
  --barWidth: calc(100% / ${(props) => props.noOfStates});
  width: var(--barWidth);
  height: 30px;
  position: absolute;
  background: ${(props) =>
    props.activeState === -1 ? 'none' : props.theme.ColorsUtilityColorFocus};
  transform: translateX(
    calc(${(props) => props.activeState} * var(--barWidth))
  );
  // transition: transform 0.1s ease-in-out;
`;

const Label = styled.div<{ active: boolean }>`
  color: ${(props) =>
    props.active
      ? props.theme.ColorsIconColorPrimary
      : props.theme.ColorsTypographyInverse};
  background-color: ${(props) =>
    props.active ? props.theme.ColorsUtilityColorFocus : 'transparent'};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  display: grid;
  place-items: center;
  height: 100%;
  z-index: 1;
`;

const DividerDiv = styled.div`
  width: 80%;
`;

/**
 * A generic button component that allows toggling between multiple states defined as objects.
 * Uses valueProp and nameProp to determine which object fields to use for the form value and display label.
 *
 * @param {DynamicActivationButtonProps<T, V, N>} props - The props containing states array, prop mappings, and callbacks.
 * @returns {JSX.Element} The rendered DynamicActivationButton component.
 */
export function DynamicActivationButton<
  T extends Record<string, any>,
  V extends keyof T,
  N extends keyof T
>({
  currentState,
  onChange,
  rules,
  name,
  control,
  label,
  bottomDivider,
  states,
  valueProp,
  nameProp,
}: DynamicActivationButtonProps<T, V, N>): JSX.Element {
  // Internal state to manage the active state of the button
  const [activeState, setActiveState] = useState<T[V] | undefined>(
    currentState
  );

  useEffect(() => {
    setActiveState(currentState);
  }, [currentState]);

  /**
   * Finds the state object by its value.
   * @param {T[V]} value - The value to search for.
   * @returns {T | undefined} - The state object with the matching value, or undefined if not found.
   */
  const findStateByValue = (value?: T[V]): T | undefined => {
    if (value === undefined) return undefined;
    return states.find((state) => state[valueProp] === value);
  };

  /**
   * Returns the index for the given state value in the states array.
   * @param {T[V]} value - The state value to get the index for.
   * @returns {number} - The index representing the state, or -1 if not found.
   */
  const getStateIndex = (value?: T[V]): number => {
    if (value === undefined) return -1;
    return states.findIndex((state) => state[valueProp] === value);
  };

  /**
   * Gets the display label for a state object.
   * @param {T} state - The state object to get the label for.
   * @returns {string} - The display label for the state.
   */
  const getDisplayLabel = (state: T): string => {
    return String(state[nameProp]);
  };

  /**
   * Gets the value for a state object.
   * @param {T} state - The state object to get the value for.
   * @returns {T[V]} - The value for the state.
   */
  const getStateValue = (state: T): T[V] => {
    return state[valueProp];
  };

  /**
   * Component for rendering individual toggle labels
   */
  const ToggleBandLabel = ({
    active,
    onClick,
    children,
  }: {
    active: boolean;
    onClick: () => void;
    children: React.ReactNode;
  }) => (
    <Label active={active} onClick={onClick}>
      {children}
    </Label>
  );

  /**
   * Handles clicking on a label and changes the active state.
   * If onChange is provided, the new state value is passed to the callback.
   *
   * @param {T} state - The state object that was clicked.
   * @param {Function} setFormState - React Hook Form's onChange function.
   */
  const handleClick = (state: T, setFormState?: (value: T[V]) => void) => {
    const stateValue = getStateValue(state);
    setActiveState(stateValue); // Update internal state
    if (onChange) {
      onChange(stateValue); // Trigger the callback if available
    }
    if (setFormState) {
      setFormState(stateValue); // Update form state
    }
  };

  const activeIndex = getStateIndex(activeState);

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      defaultValue={activeState}
      render={({ field: { onChange: setFormState, value } }) => (
        <ActivationButtonContainer>
          {label && <ActivationButtonLabel>{label}</ActivationButtonLabel>}
          <Container noOfStates={states.length}>
            {/* <ToggleBar activeState={activeIndex} noOfStates={states.length} /> */}
            {Array.isArray(states) &&
              states.length > 0 &&
              (states || [])?.map((state, idx) => (
                <ToggleBandLabel
                  key={String(getStateValue(state))}
                  active={activeIndex === idx}
                  onClick={() => handleClick(state, setFormState)}
                >
                  {getDisplayLabel(state)}
                </ToggleBandLabel>
              ))}
          </Container>
          {bottomDivider && (
            <DividerDiv>
              <Divider background="primary" size="fullwidth" height="thin" />
            </DividerDiv>
          )}
        </ActivationButtonContainer>
      )}
    />
  );
}

// Predefined activation states as objects for convenience
export interface ActivationStateV2 {
  id: string;
  name: string;
  displayName: string;
}

export const DEFAULT_ACTIVATION_STATES: readonly ActivationStateV2[] = [
  { id: 'DISABLED', name: 'DISABLED', displayName: 'Disabled' },
  { id: 'ENABLED', name: 'ENABLED', displayName: 'Enabled' },
  { id: 'PENDING', name: 'PENDING', displayName: 'Pending' },
] as const;

// Convenience component for the original activation use case
export const ActivationButtonV2: FunctionComponent<
  Omit<
    DynamicActivationButtonProps<ActivationStateV2, 'id', 'displayName'>,
    'states' | 'valueProp' | 'nameProp'
  > & {
    states?: readonly ActivationStateV2[];
  }
> = ({ states = DEFAULT_ACTIVATION_STATES, ...props }) => (
  <DynamicActivationButton
    {...props}
    states={states}
    valueProp="id"
    nameProp="displayName"
  />
);

// Type helper for creating state objects
export type StateObject<T extends string | number = string> = {
  value: T;
  label: string;
  [key: string]: any;
};

// Helper function to create simple state objects
export const createSimpleStates = <T extends string | number>(
  values: T[],
  labelMapper?: (value: T) => string
): StateObject<T>[] => {
  return values.map((value) => ({
    value,
    label: labelMapper ? labelMapper(value) : String(value),
  }));
};
