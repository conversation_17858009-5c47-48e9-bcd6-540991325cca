import React, { FunctionComponent, useState } from 'react';
import { Control, Controller, RegisterOptions } from 'react-hook-form';
import styled from 'styled-components';

export interface SwitchProps {
  isOn: boolean;
  onChange: (value: boolean) => void;
  enabled?: string;
  disabled?: string;
  name: string;
  rules?: RegisterOptions;
  control?: Control;
  label?: string;
}

const SwitchContainer = styled.div`
  width: 60px;
  height: 30px;
  padding-left: 4px;
  padding-right: 4px;
  background: ${(props) => props.theme.ColorsTypographyPrimary};
  border-radius: 25px;
  overflow: hidden;
  border: 1px solid ${(props) => props.theme.ColorsUtilityColorFocus};
  cursor: pointer;
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  position: relative;
`;

const ToggleBar = styled.div<{ isOn: boolean }>`
  width: 36px;
  height: 30px;
  position: absolute;
  background: ${(props) => props.theme.ColorsUtilityColorFocus};
  background: ${(props) => props.theme.ColorsUtilityColorFocus};
  border-radius: 25px;
  transform: translateX(${(props) => (props.isOn ? '32px' : '0')});
  transition: transform 0.2s ease-in-out;
  left: 0;
`;

const Label = styled.div<{ isOn: boolean; isEnabledLabel?: boolean }>`
  color: ${props => 
    props.isOn
      ? props.theme.ColorsIconColorPrimary 
      : props.theme.ColorsTypographyInverse
  };
  font-size: ${(props) => props.theme.FontSize2}px;
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  position: relative;
  margin: 4px;
  ${props => props.isEnabledLabel && 'padding-left: 7px;'}
`;

/**
 * A toggle switch component that can be turned on/off.
 *
 * @example
 * <Switch isOn={true} onChange={() => console.log('switch changed')} />
 *
 * @param {boolean} isOn - Whether the switch is currently on.
 * @param {Function} onChange - Callback when the switch is toggled.
 */
export const Switch: FunctionComponent<SwitchProps> = ({ 
  isOn, 
  onChange, 
  enabled = 'On', 
  disabled = 'Off', 
  rules, 
  name, 
  control 
}) => {
  const [isTrue, setIsTrue] = useState(isOn);
  /**
   * Handles the toggle of the switch component, by calling the onChange 
   * callback with the negated value of the current isOn state.
   */
  const handleToggle = () => {
    setIsTrue(!isTrue);
    onChange(!isTrue);
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ 
        field: { name, onChange },
        formState
      }) => (
        <SwitchContainer onClick={handleToggle}>
          <ToggleBar isOn={isTrue} />
          <Label isOn={!isTrue}>{disabled}</Label>
          <Label isOn={isTrue} isEnabledLabel>{enabled}</Label>
        </SwitchContainer>
      )}
    />
  );
};
