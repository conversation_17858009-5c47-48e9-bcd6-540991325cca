import { useMediaQuery, useTheme } from '@mui/material';
import React from 'react';
import styled from 'styled-components';
import { Text } from '../../../Fragments/Text/Text';
import { SubscriptionCard } from '../SubscriptionCard/SubscriptionCard';

interface SubscriptionFeeProps {
  children?: React.ReactNode;
}

const Border = styled.div`
  width: 100%;
  height: 100%;
  border: 0.5px solid ${(props) => props?.theme.ColorsStrokesDefault};
  border-radius: ${(props) => props.theme?.RadiusMd};
  justify-self: center;
`;

const SubscriptionFeeWrapper = styled.div<SubscriptionFeeProps>`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: auto 1fr;
  justify-items: center;
`;

const FullWidthText = styled.div`
  grid-column: 1 / 4;
  grid-row: 1;
  max-width: 838px;
  padding-bottom: 30px;
  word-wrap: normal;
`;

const IncludedContainer = styled.div`
  grid-column: 1 / 3;
  grid-row: 2;
  width: 100%;
`;

const JobPay = styled.div`
  grid-row: 2;
  width: 100%;
  align-self: center;
  display: grid;
  justify-content: end;
  justify-items: end;
`;

const Container = styled.div<{ padding: string }>`
  padding: ${({ padding }) => padding};
`;

export const JobFeeCard = ({ children }: SubscriptionFeeProps) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.up('md'));
  return (
    <SubscriptionCard>
      <SubscriptionFeeWrapper>
        <FullWidthText>
          <Text
            textItems={[
              {
                text: '**Job fee:  <b>After 1 July 2025</b> job fee is changed at <b>3%</b> of awarded job. Limited to maximum R1 000 per awarded job.',
                options: { format: 'heading', type: 'sub-heading' },
              },
            ]}
          />
        </FullWidthText>
        <IncludedContainer>
          <Text
            textItems={[
              {
                text: 'Job fee covers:',
                options: { format: 'heading', type: 'sub-heading' },
              },
              {
                text: '• Job allocation fee',
                options: { format: 'heading', type: 'sub-heading' },
              },
              {
                text: '• Support',
                options: { format: 'heading', type: 'sub-heading' },
              },
              {
                text: '• Access to working capital financing',
                options: { format: 'heading', type: 'sub-heading' },
              },
              {
                text: '• Preferential rates and Supplier discounts',
                options: { format: 'heading', type: 'sub-heading' },
              },
              {
                text: '• Customer warranty',
                options: { format: 'heading', type: 'sub-heading' },
              },
            ]}
          />
        </IncludedContainer>
        <JobPay>
          <Text
            textItems={[
              {
                text: 'R50 to R1 000',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                  style: { justifyItems: 'end' },
                },
              },
              {
                text: 'per job awarded',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                  style: { justifyItems: 'end' },
                },
              },
            ]}
          />
        </JobPay>
      </SubscriptionFeeWrapper>
    </SubscriptionCard>
  );
};

export const SubscriptionFeeCard = ({ children }: SubscriptionFeeProps) => {
  return (
    <SubscriptionCard>
      <SubscriptionFeeWrapper>
        <FullWidthText>
          <Text
            textItems={[
              {
                text: 'If you have paid a 4-Sure joining fee on one of our other platforms, then you will not have to pay for this onboarding fee.',
                options: { format: 'heading', type: 'sub-heading' },
              },
            ]}
          />
        </FullWidthText>
      </SubscriptionFeeWrapper>
    </SubscriptionCard>
  );
};
