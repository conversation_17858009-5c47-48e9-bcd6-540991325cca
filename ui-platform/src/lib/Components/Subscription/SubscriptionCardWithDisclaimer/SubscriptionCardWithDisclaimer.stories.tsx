import { Meta, StoryObj } from '@storybook/react/';
import { SubscriptionBasic } from '../SubscriptionBasic/SubscriptionBasic';
import SubscriptionCardWithDisclaimer from './SubscriptionCardWithDisclaimer';

const meta: Meta<typeof SubscriptionCardWithDisclaimer> = {
  component: SubscriptionCardWithDisclaimer,
  title: 'Components/Subscription/SubscriptionCardWithDisclaimer',
};
export default meta;

type Story = StoryObj<typeof SubscriptionCardWithDisclaimer>;

export const Basic: Story = {
  args: {},
};
