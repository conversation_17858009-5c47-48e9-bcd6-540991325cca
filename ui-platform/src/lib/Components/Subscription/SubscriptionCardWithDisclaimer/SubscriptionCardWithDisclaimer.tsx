// external module imports
import React, { ComponentPropsWithRef } from 'react';
import styled from 'styled-components';
// internal module imports
import {
  HoverItem,
  useHoverInteraction,
} from '../../../Engine/hooks/useHoverInteraction';
import {
  JobFeeCard,
  SubscriptionFeeCard,
} from '../DisclaimerCards/DisclaimerCards';
import { SubscriptionCard } from '../SubscriptionCard/SubscriptionCard';
import {
  SubscriptionContentFactory,
  SubscriptionRow,
} from '../SubscriptionFactory/SubscriptionFactory';
export interface ISubscriptionCardWithDisclaimerProps {
  _?: any;
}
const SubscriptionWrapper = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto auto;
  grid-auto-rows: auto;
  justify-items: center;
  width: 100%;
  grid-row-gap: 2rem;
`;

const PackageTierSelection = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  width: 100%;
`;

const DisclaimerMap = {
  'once-off-onboarding-fee': <SubscriptionFeeCard />,
  'job-fee-per-job': <JobFeeCard />,
};

const checkKeyInDisclaimerMap = (
  key: string
): key is keyof typeof DisclaimerMap => {
  return (
    key in DisclaimerMap &&
    DisclaimerMap[key as keyof typeof DisclaimerMap] !== undefined
  );
};

export default function SubscriptionCardWithDisclaimer(
  props: ISubscriptionCardWithDisclaimerProps
) {
  const {
    hoveredItem,
    handleMouseEnter,
    handleTouchStart,
    clearHover,
    isItemHovered,
  } = useHoverInteraction({
    onHoverStart: (item) => console.log('hover start', item),
    onHoverEnd: (item) => console.log('hover end', item),
    onTouchStart: (item) => console.log('touch start', item),
    onTouchEnd: (item) => console.log('touch end', item),
  });
  return (
    <SubscriptionWrapper>
      <PackageTierSelection>
        <SubscriptionCard>
          <SubscriptionContentFactory
            type="title"
            args={{
              style: {
                paddingBottom: '1rem',
                width: '100%',
              },
              props: {
                textItems: [
                  {
                    text: 'Basic Tier Subscription',
                    options: { format: 'heading', type: 'sub-heading' },
                  },
                ],
              },
            }}
          />
          <SubscriptionRow
            onMouseEnter={() =>
              handleMouseEnter({ key: 'once-off-onboarding-fee' })
            }
            onMouseLeave={() => clearHover()}
            onTouchStart={() =>
              handleTouchStart({ key: 'once-off-onboarding-fee' })
            }
            onTouchEnd={() => clearHover()}
          >
            <SubscriptionContentFactory
              type="checked-feature-item"
              args={{
                label: 'Once off onboarding fee',
                checked: true,
                hovered: isItemHovered('once-off-onboarding-fee'),
                disabled: true,
              }}
            />
            <SubscriptionContentFactory
              type="feature-detail-description-subtext"
              args={{
                style: {
                  width: '100%',
                  display: 'grid',
                  justifyContent: 'end',
                  alignItems: 'center',
                  justifyItems: 'end',
                },
                props: {
                  textItems: [
                    {
                      text: 'R2 995 Excl VAT',
                      options: { format: 'heading', type: 'sub-heading' },
                    },
                    {
                      text: 'Once off',
                      options: {
                        format: 'paragraph',
                        type: 'value',
                        style: { margin: '0' },
                      },
                    },
                  ],
                },
              }}
            />
          </SubscriptionRow>
          <SubscriptionRow
            onMouseEnter={() =>
              handleMouseEnter({ key: 'basic-tier-subscription-pack' })
            }
            onMouseLeave={() => clearHover()}
            onTouchStart={() =>
              handleTouchStart({ key: 'basic-tier-subscription-pack' })
            }
            onTouchEnd={() => clearHover()}
          >
            <SubscriptionContentFactory
              type="checked-feature-item"
              args={{
                label: 'Subscription package - Basic tier',
                checked: true,
              }}
            />
            <SubscriptionContentFactory
              type="feature-detail-description-subtext"
              args={{
                style: {
                  width: '100%',
                  display: 'grid',
                  justifyContent: 'end',
                  alignItems: 'center',
                  justifyItems: 'end',
                },
                props: {
                  textItems: [
                    {
                      text: '<b>Free</b>',
                      options: { format: 'heading', type: 'sub-heading' },
                    },
                  ],
                },
              }}
            />
          </SubscriptionRow>
          <SubscriptionRow
            onMouseEnter={() => handleMouseEnter({ key: 'job-fee-per-job' })}
            onMouseLeave={() => clearHover()}
            onTouchStart={() => handleTouchStart({ key: 'job-fee-per-job' })}
            onTouchEnd={() => clearHover()}
          >
            <SubscriptionContentFactory
              type="checked-feature-item"
              args={{
                label: 'Job Fee - per awarded job**',
                checked: true,
              }}
            />
            <SubscriptionContentFactory
              type="feature-detail-description-subtext"
              args={{
                style: {
                  width: '100%',
                  display: 'grid',
                  justifyContent: 'end',
                  alignItems: 'center',
                  justifyItems: 'end',
                },
                props: {
                  textItems: [
                    {
                      text: '<b>Free</b> Till <b>1 July 2025</b>',
                      options: {
                        format: 'heading',
                        type: 'sub-heading',
                        style: { textAlign: 'right' },
                      },
                    },
                  ],
                },
              }}
            />
          </SubscriptionRow>
        </SubscriptionCard>
      </PackageTierSelection>
      {hoveredItem && hoveredItem.key && (
        <PackageTierSelection>
          {checkKeyInDisclaimerMap(hoveredItem.key) && (
            <div>{DisclaimerMap[hoveredItem.key]}</div>
          )}
        </PackageTierSelection>
      )}
    </SubscriptionWrapper>
  );
}
