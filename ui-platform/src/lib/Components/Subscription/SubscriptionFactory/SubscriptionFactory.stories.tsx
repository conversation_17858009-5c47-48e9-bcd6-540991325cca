import { Checkbox } from '@mui/material';
import { Meta, StoryObj } from '@storybook/react/';
import { SubscriptionContentFactory } from './SubscriptionFactory';

const meta: Meta<typeof SubscriptionContentFactory> = {
  component: SubscriptionContentFactory,
  title: 'Components/Subscription/SubscriptionFactory',
};
export default meta;

type Story = StoryObj<typeof SubscriptionContentFactory>;

export const CheckboxItem: Story = {
  args: {
    type: 'checked-feature-item',
    args: {
      label: 'Checkbox Item',
      checked: true,
      hovered: false,
      disabled: false,
    },
  },
};
export const TitleItem: Story = {
  args: {
    type: 'title',
    args: {
      props: {
        textItems: [
          {
            text: 'Title Item',
            options: { format: 'heading', type: 'sub-heading' },
          },
        ],
      },
    },
  },
};
export const FeatureDetailDescriptionItem: Story = {
  args: {
    type: 'feature-detail-description',
    args: {
      props: {
        textItems: [
          {
            text: 'Feature Detail Description Item',
            options: { format: 'heading', type: 'sub-heading' },
          },
        ],
      },
    },
  },
};
export const FeatureDetailDescriptionSubtextItem: Story = {
  args: {
    type: 'feature-detail-description-subtext',
    args: {
      props: {
        textItems: [
          {
            text: 'Feature Detail Subtext Item',
            options: { format: 'heading', type: 'sub-heading' },
          },
        ],
      },
    },
  },
};
export const FeatureDetailItem: Story = {
  args: {
    type: 'feature-detail',
    args: {
      featureItemDetails: 'Feature Detail Item',
      featureItemLabel: 'Feature Label Item',
    },
  },
};
