// external module imports
import React, {
  ComponentProps,
  ComponentPropsWithRef,
  ReactNode,
  useMemo,
} from 'react';
import styled from 'styled-components';
// internal module imports
import {
  HoverItem,
  UseHoverInteractionProps,
} from '../../../Engine/hooks/useHoverInteraction';
import { Text } from '../../../Fragments/Text/Text';
import { FragmentStyleHOC } from '../../../Utilities';
import Checkbox from '../../Inputs/CheckBoxes/CheckBoxMolecule/CheckBoxMolecule';

const SubscriptionDescriptionDetail = styled(Text)`
  grid-column: 1 / span 2;
  grid-row: 1;
  grid-gap: 1rem;
  display: grid;
`;
const CardDescription = FragmentStyleHOC({
  Component: SubscriptionDescriptionDetail,
});

const BlockOverlay = styled.div<{
  isItemHovered: boolean;
  isItemDisabled: boolean;
}>`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 100;
  pointer-events: none;
  display: ${(props) => (props.isItemDisabled ? 'block' : 'none')};
`;

const SubscriptionCheckItem = styled.div<{ isItemDisabled?: boolean }>`
  grid-column: 1 / span 2;
  grid-row: 1;
  grid-gap: 1rem;
  position: relative;
  filter: ${(props) => (props.isItemDisabled ? 'grayscale(100%)' : 'none')};
  pointer-events: ${(props) => (props.isItemDisabled ? 'none' : 'auto')};
`;
const StyledCardTitle = styled(Text)`
  grid-column: 1 / span 3;
  grid-row: 1;
  grid-gap: 1rem;
`;
const CardTitle = FragmentStyleHOC({ Component: StyledCardTitle });
const SubscriptionDescriptionDetailSubtext = styled(Text)`
  grid-column: 3 / span 1;
  grid-row: 1;
  grid-gap: 1rem;
  display: grid;
  justify-content: end;
  justify-items: end;
  text-align: end;
`;
const CardSubText = FragmentStyleHOC({
  Component: SubscriptionDescriptionDetailSubtext,
});

interface SubscriptionFeatureItemProps {
  featureItemLabel?: ReactNode;
  featureItemDetails?: ReactNode;
}

interface SubscriptionCheckboxItemProps {
  label: string;
  checked: boolean;
  hovered?: boolean;
  disabled?: boolean;
}
const SubscriptionCheckboxItem = ({
  label,
  checked,
  hovered,
  disabled,
}: SubscriptionCheckboxItemProps) => {
  const isHovered = useMemo(() => hovered ?? false, [hovered]);
  const isDisabled = useMemo(() => disabled ?? true, [disabled]);
  return (
    <SubscriptionCheckItem isItemDisabled={isDisabled}>
      <Checkbox name={label} checked={checked} />
      {isDisabled && (
        <BlockOverlay isItemHovered={isHovered} isItemDisabled={isDisabled} />
      )}
    </SubscriptionCheckItem>
  );
};

const SubscriptionFeatureItem = ({
  featureItemLabel,
  featureItemDetails,
}: SubscriptionFeatureItemProps) => {
  return (
    <>
      {featureItemLabel && featureItemLabel}
      {featureItemDetails && featureItemDetails}
    </>
  );
};

type CheckedFeatureItem = {
  type: 'checked-feature-item';
  // args: SubscriptionFeatureItemProps;
};

type SubscriptionTitleType = {
  type: 'title';
  // args: ComponentProps<typeof CardTitle>;
};

type FeatureDetailType = {
  type: 'feature-detail';
  // args: SubscriptionFeatureItemProps;
};

type FeatureDetailDescriptionType = {
  type: 'feature-detail-description';
};

type FeatureDetailDescriptionSubtextType = {
  type: 'feature-detail-description-subtext';
};

type SubscriptionContentType<T> = T extends {
  type: 'checked-feature-item';
}
  ? SubscriptionCheckboxItemProps
  : T extends { type: 'title' }
  ? ComponentProps<typeof CardTitle>
  : T extends {
      type: 'feature-detail-description';
    }
  ? ComponentProps<typeof SubscriptionDescriptionDetail>
  : T extends {
      type: 'feature-detail-description-subtext';
    }
  ? ComponentProps<typeof SubscriptionDescriptionDetailSubtext>
  : SubscriptionFeatureItemProps;

type SubscriptionContentFactoryArgs =
  | SubscriptionContentType<CheckedFeatureItem>
  | SubscriptionContentType<SubscriptionTitleType>
  | SubscriptionContentType<FeatureDetailDescriptionType>
  | SubscriptionContentType<FeatureDetailDescriptionSubtextType>
  | SubscriptionContentType<FeatureDetailType>;

type SubscriptionContentFactoryProps = {
  type:
    | 'checked-feature-item'
    | 'title'
    | 'feature-detail'
    | 'feature-detail-description'
    | 'feature-detail-description-subtext';
  args: SubscriptionContentFactoryArgs;
};
export const SubscriptionContentFactory = ({
  type,
  args,
}: SubscriptionContentFactoryProps) => {
  switch (type) {
    case 'checked-feature-item':
      return (
        <SubscriptionCheckboxItem
          label={(args as SubscriptionCheckboxItemProps).label}
          checked={(args as SubscriptionCheckboxItemProps).checked}
          hovered={(args as SubscriptionCheckboxItemProps).hovered}
          disabled={(args as SubscriptionCheckboxItemProps).disabled}
        />
      );
    case 'title':
      return (
        <CardTitle
          className={(args as ComponentProps<typeof CardTitle>).className}
          props={(args as ComponentProps<typeof CardTitle>).props}
          style={(args as ComponentProps<typeof CardTitle>).style}
        />
      );
    case 'feature-detail-description':
      return (
        <CardDescription
          className={(args as ComponentProps<typeof CardTitle>).className}
          props={(args as ComponentProps<typeof CardTitle>).props}
          style={(args as ComponentProps<typeof CardTitle>).style}
        />
      );
    case 'feature-detail-description-subtext':
      return (
        <CardSubText
          className={(args as ComponentProps<typeof CardTitle>).className}
          style={(args as ComponentProps<typeof CardTitle>).style}
          props={(args as ComponentProps<typeof CardTitle>).props}
        />
      );
    case 'feature-detail':
    default:
      return (
        <SubscriptionFeatureItem
          featureItemDetails={
            (args as SubscriptionFeatureItemProps).featureItemDetails
          }
          featureItemLabel={
            (args as SubscriptionFeatureItemProps).featureItemLabel
          }
        />
      );
  }
};
interface SubscriptionRowHoverProps {
  onMouseEnter?: (item: HoverItem) => void;
  onMouseLeave?: (item?: HoverItem) => void;
  onTouchStart?: (item: HoverItem) => void;
  onTouchEnd?: (item?: HoverItem) => void;
}
export const SubscriptionRow = styled.div<
  Omit<
    ComponentPropsWithRef<'div'>,
    'onMouseEnter' | 'onMouseLeave' | 'onTouchStart' | 'onTouchEnd'
  > &
    SubscriptionRowHoverProps
>`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr;
  grid-column: 1 / span 3;
  grid-row: 1;
  grid-row-gap: 1.5rem;
  align-items: center;
  padding-bottom: 1.5rem;
`;
