'use client';
// import external modules
import Keycloak from 'keycloak-js';
import { assocPath, flatten, mergeDeepRight, path, uniq } from 'ramda';
import { ActionFunction, redirect } from 'react-router-dom';
// import internal modules
import { TemplateLiteralLogger } from '../../Utilities';
import { ErrorState, getErrorStore } from '../hooks';
import { FetchConfig } from '../models';
import { useAppStore } from '../useAppStore';
import {
  ProcessUrlTemplateOptions,
  ProcessedUrlResult,
  processUrlTemplateAdvanced,
} from './evaluateStringExpression';
import { renderTemplate, renderTemplateObject } from './render-template';
import { transformations } from './transformations';

const dbg = new TemplateLiteralLogger({
  prefix: '👨‍💻[Evaluate String Expression]:',
  enabled: true,
  options: { style: { backgroundColor: '#DBF0D6', color: '#557942' } },
});

export function objectTransformMapper(
  inputObject: { [key: string]: any },
  mapper: { [id: string]: string | any[] }
) {
  return Object.entries(mapper)?.reduce((acc, [formPath, storePath]) => {
    // const valueExists = path(formPath?.split('.'), inputObject) !== undefined && path(formPath?.split('.'), inputObject) !== null  && path(formPath?.split('.'), inputObject) !== '';
    const valueOnForm = path(formPath?.split('.'), inputObject);
    const valueExists = typeof valueOnForm !== 'boolean' ? !!valueOnForm : true;
    if (typeof storePath === 'string') {
      return valueExists
        ? mergeDeepRight(
            acc,
            assocPath(
              storePath?.split('.'),
              path(formPath?.split('.'), inputObject),
              {}
            )
          )
        : acc;
    } else if (
      Array.isArray(storePath) &&
      typeof storePath[0] === 'string' &&
      !storePath[0].startsWith('@func:')
    ) {
      return storePath?.reduce((innerAcc, innerStorePath) => {
        const innerObj = valueExists
          ? mergeDeepRight(
              innerAcc,
              assocPath(
                innerStorePath?.split('.'),
                path(formPath?.split('.'), inputObject),
                {}
              )
            )
          : innerAcc;
        return mergeDeepRight(acc, innerObj);
      }, {});
    } else if (
      Array.isArray(storePath) &&
      typeof storePath[0] === 'string' &&
      storePath[0].startsWith('@func:')
    ) {
      const [funcWithPrefix, pathToStore] = storePath;
      const func = funcWithPrefix.replace('@func:', '');
      const transformed = transformations[func](
        path(formPath?.split('.'), inputObject)
      );
      return valueExists
        ? mergeDeepRight(
            acc,
            assocPath(pathToStore?.split('.'), transformed, {})
          )
        : acc;
    } else if (Array.isArray(storePath) && Array.isArray(storePath[0])) {
      return storePath?.reduce((inner1, funcToPathArray) => {
        const [funcWithPrefix, pathToStore] = funcToPathArray;
        const func = funcWithPrefix.replace('@func:', '');
        const transformed = transformations[func](
          path(formPath?.split('.'), inputObject)
        );
        const innerObj = valueExists
          ? mergeDeepRight(
              inner1,
              assocPath(pathToStore?.split('.'), transformed, {})
            )
          : inner1;
        // const innerObj1 = valueExists
        //   ? R.mergeDeepRight(inner1, R.assocPath(pathToStore.split('.'), R.path(formPath.split('.'), formValues), {}))
        //   : inner1;
        return mergeDeepRight(acc, innerObj);
      }, {});
    }
  }, {});
}

export function renderTemplateStringOnClient(
  templateObj: { template: string },
  storeValues: any
) {
  // const responses = await Promise.all(templateObj.fetchCalls
  //     .map( async fc => {
  //        const response = await fetch(fc.url);
  //        return {[fc.key]: await response.json()};
  //     }));

  const data: any = storeValues;
  console.log({ storeValues });

  const template = templateObj.template;
  const interpolatedString = template.replace(/{([^}]+)}/g, (match, group) => {
    let prop = '';
    let func = '';
    if (group.includes('(')) {
      const [fn, propPath] = group.split('(');
      const [p] = propPath.split(')');
      prop = p;
      func = fn;
    } else {
      prop = group;
    }
    const value = getNestedProperty(data, prop.trim()) ?? '';
    // console.log({func, prop, value, data})
    // const funcsMap = { toUpperCase }
    if (func) {
      const transformation = transformations[func];
      return transformation(value);
    }
    return value;
  });
  return interpolatedString;
}

export function getNestedProperty(
  obj: { [key: string]: any },
  path: string,
  defaultValue?: any
): any {
  const props = path.split('.');
  let result = obj;
  for (const prop of props) {
    result = result[prop];
    if (
      typeof result === 'undefined' ||
      (typeof result !== 'boolean' && !result)
    ) {
      return defaultValue;
    }
  }
  return result;
}

export function extractValues(
  defaultValues: { [key: string]: any },
  store: any
) {
  return Object.entries(defaultValues).reduce((acc, [key, value]) => {
    let val: any;

    if (typeof value === 'string' && value.startsWith('$')) {
      const storePath = value.replace('$', '');
      val = getNestedProperty(store, storePath, '');
    } else if (!!value && typeof value === 'object' && !Array.isArray(value)) {
      val = extractValues(value, store);
    } else {
      val = value;
    }
    return {
      ...acc,
      [key]: val,
    };
  }, {});
}

export async function makeFetchCalls(
  fetchCalls: FetchConfig[],
  keycloak: Keycloak,
  request?: Request,
  envObject?: any,
  tokenPrefix?: 'Bearer' | 'Token',
  options?: {
    debug?: boolean;
    processUrlTemplateOptions?: Omit<ProcessUrlTemplateOptions, 'debug'>;
  }
): Promise<Record<string, any>> {
  const authTokenPrefix = tokenPrefix || 'Bearer';
  if (keycloak?.isTokenExpired()) {
    keycloak.login();
  }

  let queryParams = {};
  if (request) {
    const url = new URL(request.url);
    const params = new URLSearchParams(url.search);

    // Convert query parameters to an object
    queryParams = Object.fromEntries(params.entries());
  }

  const errorStore = getErrorStore() as ErrorState;

  const fetchResultsObject: Record<string, any> = (
    await Promise.all(
      fetchCalls.map(async (fc) => {
        dbg.configure({
          prefix: '👨‍💻[Make Fetch Calls]:',
          enabled: options?.debug || fc?.options?.debug,
          options: { style: { backgroundColor: '#DBF0D6', color: '#FF8200' } },
        });

        let reqInit: RequestInit;

        if (fc.method === 'POST') {
          const rawBody = { ...fc.body, ...queryParams };
          const bodyWithParams = renderTemplateObject(
            rawBody,
            useAppStore.getState() || {}
          );

          // Check if all values in the body are truthy
          const allTruthy = Object.values(bodyWithParams).every(
            (value) => !!value || typeof value === 'boolean' || value === 0
          );

          const isBodyEmpty = Object.keys(bodyWithParams).length === 0;

          if (!allTruthy && !isBodyEmpty) {
            console.log(
              `Skipping fetch call for ${fc.key} due to falsy values in body`
            );
            return { [fc.key]: null };
          }

          reqInit = {
            method: fc.method,
            body: JSON.stringify(bodyWithParams),
            headers: {
              'Content-Type': 'application/json',
              authorization: `${fc.tokenPrefix || authTokenPrefix} ${
                keycloak?.token
              }`,
            },
          };
        } else {
          reqInit = {
            method: fc.method,
            headers: {
              'Content-Type': 'application/json',
              authorization: `${fc.tokenPrefix || authTokenPrefix} ${
                keycloak?.token
              }`,
            },
          };
        }

        const preurl = renderTemplate(fc.url, useAppStore.getState() || {});
        dbg.log`Render template preurl: ${preurl}`;
        const { url, metadata } = processUrlTemplateAdvanced(
          preurl as string,
          useAppStore.getState() || {},
          {
            ...(fc?.options || options?.processUrlTemplateOptions),
            debug: fc.options?.debug || options?.debug,
          }
        );
        let finalUrl = url;
        if (envObject) {
          finalUrl = renderTemplateStringOnClient(
            { template: finalUrl as string },
            envObject
          );
          dbg.log`Url template processing metadata: ${metadata}`;
        }

        try {
          const response = await fetch(finalUrl as string, reqInit);

          const result = await response.json();
          const actualData = fc.slicePath
            ? getNestedProperty(result, fc.slicePath)
            : result;
          let subResult = {} as { [x: string]: any } | undefined;
          if (fc.successFetchCalls && fc?.successFetchCalls?.length > 0) {
            const calls = fc.successFetchCalls?.reduce(
              (acc: FetchConfig[], call: FetchConfig) => {
                return [
                  ...acc,
                  {
                    ...call,
                    body: extractValues(call.body || {}, actualData),
                  },
                ];
              },
              []
            );
            subResult = await makeFetchCalls(
              calls,
              keycloak,
              request,
              envObject,
              tokenPrefix,
              options
            );
          }

          return { [fc.key]: actualData, ...subResult };
        } catch (error) {
          errorStore.addError({
            key: `make-fetch-calls-${Date.now()}`,
            message:
              error instanceof Error
                ? `${error.message}: Please try refreshing the page`
                : 'Unknown error: Try refreshing the website',
            source: 'server',
            stackTrace: error instanceof Error ? error.stack : undefined,
          });
          // Return object with fetch key preserving the promise shape
          return { [fc.key]: null };
        }
      })
    )
  ).reduce<Record<string, any>>((acc, res) => {
    // Merge the current result with accumulator, defaulting to empty object if undefined
    return { ...acc, ...(res || {}) };
  }, {});

  return fetchResultsObject;
}

type ActionFunctionGeneratorType = (
  keycloak: Keycloak,
  envObject: any,
  tokenPrefix?: 'Bearer' | 'Token'
) => ActionFunction;

export const actionFunctionGenerator: ActionFunctionGeneratorType = (
  keycloak: Keycloak,
  envObject: any,
  tokenPrefix?: 'Bearer' | 'Token'
) => {
  const errorStore = getErrorStore() as ErrorState;

  const func: ActionFunction = async ({ request }) => {
    const formData = await request.formData();
    const url = formData.get('url') as string;
    const redirectPath = formData.get('redirect') as string;
    const bodySlicePath = formData.get('bodySlicePath') as string;
    const authTokenPrefix = tokenPrefix || 'Bearer';

    // const headersStr = formData.get('headers') as string;
    const headers = {};
    // if (headersStr) {
    //   headers = JSON.parse(headersStr);
    // }

    let res: any;
    const theFile = formData.get('file') as any;
    const purpose = formData.get('purpose') as any;
    const fileIsAList = formData.get('list') as any;
    const staff_id = formData.get('staff_id') as any;
    const sp_id = formData.get('sp_id') as any;

    //
    let newUrl = '';
    if (envObject) {
      newUrl = renderTemplateStringOnClient({ template: url }, envObject);
    } else {
      newUrl = url;
    }

    if (theFile) {
      //
      const uploadData = new FormData();
      uploadData.append('file', theFile);
      uploadData.append('purpose', purpose);
      uploadData.append('list', fileIsAList);
      if (staff_id) {
        uploadData.append('staff_id', staff_id);
      }
      if (sp_id) {
        uploadData.append('sp_id', sp_id);
      }
      try {
        res = await fetch(newUrl, {
          method: 'POST',
          body: uploadData,
          headers: {
            ...headers,
            authorization: `${authTokenPrefix} ${keycloak?.token}`,
          },
        });
      } catch (error) {
        errorStore.addError({
          key: `action-function-generator-file-upload-${Date.now()}`,
          message:
            error instanceof Error
              ? `${error.message}: Please try refreshing the page`
              : 'Unknown error: Try refreshing the website',
          source: 'server',
          stackTrace: error instanceof Error ? error.stack : undefined,
        });
      }
    } else {
      const dataStr = formData.get('data') as string;
      const data = JSON.parse(dataStr);
      const dt = bodySlicePath ? getNestedProperty(data, bodySlicePath) : data;

      try {
        res = await fetch(newUrl, {
          method: 'POST',
          body: JSON.stringify(dt),
          headers: {
            'Content-Type': 'application/json; charset=UTF-8',
            ...headers,
            authorization: `${authTokenPrefix} ${keycloak?.token}`,
          },
        });
      } catch (error) {
        errorStore.addError({
          key: `action-function-generator-submit-${Date.now()}`,
          message:
            error instanceof Error
              ? `${error.message}: Please try refreshing the page`
              : 'Unknown error: Try refreshing the website',
          source: 'server',
          stackTrace: error instanceof Error ? error.stack : undefined,
        });
      }
    }

    const result = await res.json();

    if (redirectPath) {
      return redirect(redirectPath);
    } else {
      return result || {};
    }
  };
  return func;
};

// Function to evaluate form  Expression
export const evaluateFormConditionExpression = (
  expression: string,
  $store: any,
  $form?: any,
  $formState?: any
) => {
  try {
    // Use Function constructor to evaluate the expression
    const func = new Function(
      '$store',
      '$form',
      '$formState',
      `return ${expression};`
    );
    return func($store, $form, $formState);
  } catch (error) {
    console.error('Error evaluating expression:', error);
    return false;
  }
};

export function applyTemplateToObject(obj: any, context: any): any {
  if (typeof obj === 'string') {
    return renderTemplateStringOnClient({ template: obj }, context);
  } else if (Array.isArray(obj)) {
    return obj.map((item) => applyTemplateToObject(item, context));
  } else if (typeof obj === 'object' && obj !== null) {
    return Object.keys(obj).reduce((acc, key) => {
      acc[key] = applyTemplateToObject(obj[key], context);
      return acc;
    }, {} as any);
  }
  return obj;
}
