import React from 'react';
import {
  evalStringExpression,
  parseTemplateUrl,
  processTemplates,
} from './evaluateStringExpression';

export const EvaluateStringExpressionDemo = () => {
  const store = {
    user: { name: 'Alice', age: 30 },
    company: '4SURE',
    arr: [{ x: 1 }, { x: 2 }],
    value: 42,
  };

  // evalStringExpression demo
  const evalString = evalStringExpression('#{user.name}', store); // "Alice"
  const evalJS = evalStringExpression('js:{2*3}', {}); // 6
  const evalParam = evalStringExpression('@param', { foo: 'bar' }); // { foo: 'bar' }
  const evalNestedParam = evalStringExpression('@param:{user.name}', {
    user: { name: 'Bob' },
  }); // "Bob"

  // processTemplates demo
  const processedObj = processTemplates(
    {
      greeting: '#Hello {user.name}!',
      company: '#{company}',
      numbers: [1, 2, '#{value}'],
      nested: { arr: ['#{user.age}', '#{company}'] },
    },
    store
  );
  // {
  //   greeting: "Hello Alice!",
  //   company: "4SURE",
  //   numbers: [1, 2, 42],
  //   nested: { arr: [30, "4SURE"] }
  // }

  // parseTemplateUrl demo
  const parsedUrl = parseTemplateUrl(
    '{api}/users?userId=#{userId}$token?filter=$filter'
  );
  // parsedUrl.groups, parsedUrl.baseUrl, parsedUrl.expressions

  return (
    <div>
      <h2>evalStringExpression</h2>
      <div>
        <b>#user.name:</b> {String(evalString)}
      </div>
      <div>
        <b>js:2*3:</b> {String(evalJS)}
      </div>
      <div>
        <b>@param:</b> {JSON.stringify(evalParam)}
      </div>
      <div>
        <b>@param:&#123;user.name&#125;:</b> {String(evalNestedParam)}
      </div>

      <h2>processTemplates</h2>
      <pre>{JSON.stringify(processedObj, null, 2)}</pre>

      <h2>parseTemplateUrl</h2>
      <pre>{JSON.stringify(parsedUrl, null, 2)}</pre>
    </div>
  );
};
