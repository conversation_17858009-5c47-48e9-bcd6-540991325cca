# String Expression Processing Engine

A comprehensive template string processing system that supports multiple expression syntaxes for dynamic content generation and data transformation.

## Table of Contents

- [Overview](#overview)
- [Installation](#installation)
- [Quick Start](#quick-start)
- [Expression Syntaxes](#expression-syntaxes)
- [API Reference](#api-reference)
- [Advanced Usage](#advanced-usage)
- [Error Handling](#error-handling)
- [Performance](#performance)
- [Security](#security)
- [Migration Guide](#migration-guide)
- [Examples](#examples)

## Overview

The String Expression Processing Engine provides a unified way to process dynamic content using multiple expression syntaxes. It's designed for flexibility, performance, and ease of use in modern JavaScript/TypeScript applications.

### Key Features

- 🔤 **Multiple Syntaxes**: Support for templates, store paths, JavaScript, and parameters
- 🔄 **Recursive Processing**: Handle nested objects and arrays automatically
- 🌐 **URL Template Parsing**: Specialized support for URL template analysis
- 🛡️ **Error Resilient**: Graceful handling of invalid expressions and missing data
- ⚡ **Performance Optimized**: Efficient processing of complex data structures
- 🔒 **Secure**: Sandboxed JavaScript execution environment

## Installation

```bash
npm install string-expression-engine
```

```typescript
import { 
  evalStringExpression, 
  processTemplates, 
  parseTemplateUrl 
} from 'string-expression-engine';
```

## Quick Start

```typescript
const store = {
  user: { name: 'Alice', age: 30 },
  company: 'ACME Corp'
};

// Basic expression evaluation
const result1 = evalStringExpression('#{user.name}', store); // 'Alice'
const result2 = evalStringExpression('js:{user.age * 2}', store); // 60

// Process complex objects
const template = {
  greeting: 'Hello #{user.name}!',
  isAdult: 'js:{user.age >= 18}',
  company: '$company'
};

const processed = processTemplates(template, store);
// Result: { greeting: 'Hello Alice!', isAdult: true, company: 'ACME Corp' }
```

## Expression Syntaxes

### 1. Template Strings (`#`)

Process template strings with variable substitution using curly braces.

```typescript
// Simple property access
'#{user.name}' → 'Alice'

// Nested properties
'#{user.preferences.theme}' → 'dark'

// Mixed content
'#Hello {user.name}, welcome to {company}!' → 'Hello Alice, welcome to ACME Corp!'
```

**Use Cases:**
- Dynamic content generation
- Internationalization strings
- Configuration templates

### 2. Store Paths (`$`)

Access nested object properties with optional array filtering.

```typescript
// Simple property access
'$user.name' → 'Alice'

// Nested properties
'$user.preferences.theme' → 'dark'

// Array filtering
'$products?category=tools' → [filtered products]

// Complex queries
'$orders?status=completed&amount>100' → [filtered orders]
```

**Query Syntax:**
- `property=value`: Filter by exact match
- `property>value`: Greater than comparison
- `property<value`: Less than comparison
- `property!=value`: Not equal comparison

**Use Cases:**
- Data extraction from complex objects
- Dynamic filtering
- Configuration path resolution

### 3. JavaScript Evaluation (`js:`)

Execute JavaScript expressions with access to the data context.

```typescript
// Simple calculations
'js:{user.age * 2}' → 60

// Array methods
'js:{products.length}' → 5

// Complex logic
'js:{user.age >= 18 ? "adult" : "minor"}' → 'adult'

// Built-in functions
'js:{Math.round(price * 1.2)}' → 24

// String manipulation
'js:{user.name.toUpperCase()}' → 'ALICE'
```

**Available Context:**
- All properties from the data object
- Standard JavaScript built-ins (Math, Date, etc.)
- Array and Object methods
- String manipulation functions

**Use Cases:**
- Dynamic calculations
- Conditional logic
- Data transformation
- Complex business rules

## API Reference

### `evalStringExpression(value: string, obj: any): any`

Evaluates a single expression against a data object.

**Parameters:**
- `value` (string): The expression to evaluate (if not a string, returns as-is)
- `obj` (any): The data object for evaluation context

**Returns:** The processed result

**Examples:**
```typescript
evalStringExpression('#{user.name}', { user: { name: 'Alice' } }); // 'Alice'
evalStringExpression('js:{1 + 2}', {}); // 3
evalStringExpression('$user.name', { user: { name: 'Bob' } }); // 'Bob'
evalStringExpression('plain text', {}); // 'plain text'
```

### `processTemplates(obj: any, storeObj: any): any`

Recursively processes template expressions in objects and arrays.

**Parameters:**
- `obj` (any): The object/array/value to process
- `storeObj` (any): The data store for template evaluation

**Returns:** A new object with all templates processed

**Examples:**
```typescript
const template = {
  user: '#{user.name}',
  scores: ['js:{score1}', 'js:{score2}'],
  nested: { value: '$config.setting' }
};

processTemplates(template, storeData);
```

### `parseTemplateUrl(url: string): ParseResult`

Parses a URL template and extracts expression information.

**Parameters:**
- `url` (string): The template URL to parse

**Returns:** `ParseResult` object containing:
- `groups` (string[]): Array of URL segments
- `baseUrl` (string): The base URL pattern
- `expressions` (Expression[]): Array of found expressions

**Expression Object:**
```typescript
interface Expression {
  type: 'hash' | 'javascript' | 'variable' | 'query';
  content: string;
  fullMatch: string;
}
```

**Examples:**
```typescript
const url = '{api}/users/#{userId}?token=$authToken';
const parsed = parseTemplateUrl(url);

console.log(parsed.baseUrl); // '{api}/users'
console.log(parsed.expressions.length); // 2
console.log(parsed.expressions[0].type); // 'hash'
```

## Advanced Usage

### Nested Object Processing

```typescript
const complexTemplate = {
  user: {
    profile: {
      name: '#{user.firstName} #{user.lastName}',
      age: '#{user.age}',
      isVip: 'js:{user.purchases > 1000}'
    },
    preferences: {
      theme: '$user.settings.theme',
      notifications: 'js:{user.settings.email && user.settings.push}'
    }
  },
  metadata: {
    generated: 'js:{new Date().toISOString()}',
    version: '$app.version'
  }
};

const result = processTemplates(complexTemplate, dataStore);
```

### Array Processing with Templates

```typescript
const listTemplate = [
  {
    id: '#{item.id}',
    name: '#{item.name}',
    price: 'js:{item.price * (1 + tax)}',
    available: '$inventory?productId={item.id}'
  }
];

// Process array of items
const items = [
  { id: 1, name: 'Widget', price: 10 },
  { id: 2, name: 'Gadget', price: 20 }
];

const processed = items.map(item => 
  processTemplates(listTemplate[0], { item, tax: 0.1, inventory })
);
```

### Dynamic URL Generation

```typescript
const urlTemplate = '{apiBase}/{version}/users/{userId}/orders?status=$filter&limit=js:{Math.min(requestedLimit, maxLimit)}';

const config = {
  apiBase: 'https://api.example.com',
  version: 'v2',
  userId: 123,
  filter: 'completed',
  requestedLimit: 50,
  maxLimit: 25
};

// Parse the URL structure
const parsed = parseTemplateUrl(urlTemplate);

// Generate the actual URL by processing templates
const finalUrl = processTemplates(urlTemplate, config);
```

## Error Handling

The system is designed to handle errors gracefully without throwing exceptions:

### Missing Properties

```typescript
const store = { user: { name: 'Alice' } };

// Missing property returns empty string
evalStringExpression('$user.age', store); // ''

// Template functions handle missing data
evalStringExpression('#{user.age}', store); // ''

// JavaScript expressions return undefined for missing properties
evalStringExpression('js:{user.age}', store); // undefined
```

### Invalid Expressions

```typescript
// Malformed JavaScript expressions are caught
evalStringExpression('js:{invalid syntax}', store); // Returns original string

// Query parsing errors are logged but don't crash
evalStringExpression('$products?invalid=query=syntax', store);
// Logs error, returns original array
```

### Array Query Errors

```typescript
const store = {
  products: [
    { name: 'Widget', category: 'tools', price: 10 },
    { name: 'Gadget', category: 'electronics', price: 20 }
  ]
};

// Invalid query operations are handled gracefully
evalStringExpression('$products?price>invalid', store);
// Logs: "Error executing query for products: ..."
// Returns: original products array
```

## Performance

### Optimization Strategies

The engine includes several performance optimizations:

1. **Expression Caching**: Compiled expressions are cached for reuse
2. **Lazy Evaluation**: Complex expressions are only evaluated when needed
3. **Efficient Parsing**: URL parsing uses optimized regex patterns
4. **Memory Management**: Minimal object creation during processing

### Performance Tips

```typescript
// Prefer store paths for simple property access
'$user.name' // Faster than '#{user.name}'

// Cache complex computations
const precomputed = evalStringExpression('js:{expensive.calculation()}', store);
const template = { result: precomputed }; // Reuse the value

// Use specific queries instead of post-processing
'$products?category=tools' // Better than processing all products
```

### Benchmarking

```typescript
// Measure performance for your use case
console.time('template-processing');
const result = processTemplates(largeTemplate, dataStore);
console.timeEnd('template-processing');
```

## Security

### Sandboxed Execution

JavaScript expressions run in a controlled environment:

```typescript
// Safe operations
'js:{Math.random()}' // ✅ Built-in Math functions
'js:{user.name.toUpperCase()}' // ✅ String methods
'js:{products.filter(p => p.active)}' // ✅ Array methods

// Restricted operations
'js:{require("fs")}' // ❌ Node.js modules blocked
'js:{window.location.href}' // ❌ DOM access blocked
'js:{eval("malicious code")}' // ❌ eval() blocked
```

### Input Validation

```typescript
// Validate store paths
function validateStorePath(path: string): boolean {
  return /^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(path);
}

// Sanitize template input
function sanitizeTemplate(template: string): string {
  return template.replace(/[<>'"]/g, '');
}
```

### Best Practices

1. **Validate Input**: Always validate user-provided templates
2. **Limit Scope**: Provide minimal necessary data to evaluation context
3. **Monitor Usage**: Log and monitor template execution for anomalies
4. **Timeout Protection**: Implement timeouts for complex JavaScript expressions

## Migration Guide

### From Version 1.x to 2.x

**Breaking Changes:**
- `evalStringExpression` now returns original value for non-strings
- Query syntax updated to support more operators
- URL parsing improved with better regex patterns

**Migration Steps:**

```typescript
// Old (v1.x)
const result = evalStringExpression(value, store);
if (typeof result === 'undefined') {
  // Handle undefined
}

// New (v2.x)
const result = evalStringExpression(value, store);
// Non-string values pass through unchanged
// Undefined only for missing JS expression results
```

### New Features in v2.x

- Enhanced URL template parsing
- Improved error handling
- Better performance optimization
- Expanded query syntax support

## Examples

### E-commerce Product Catalog

```typescript
const productTemplate = {
  name: '#{product.name}',
  price: 'js:{product.price.toFixed(2)}',
  discountPrice: 'js:{product.price * (1 - product.discount)}',
  inStock: '$inventory?sku={product.sku}',
  category: '$categories?id={product.categoryId}',
  description: '#{product.description}',
  rating: 'js:{Math.round(product.reviews.reduce((sum, r) => sum + r.rating, 0) / product.reviews.length)}',
  url: '/products/#{product.slug}?ref=$tracking.source'
};

const products = [
  {
    name: 'Wireless Headphones',
    price: 99.99,
    discount: 0.1,
    sku: 'WH001',
    categoryId: 'electronics',
    description: 'High-quality wireless headphones',
    slug: 'wireless-headphones',
    reviews: [{ rating: 5 }, { rating: 4 }]
  }
];

const processedProducts = products.map(product => 
  processTemplates(productTemplate, { 
    product, 
    inventory: stockData, 
    categories: categoryData,
    tracking: { source: 'homepage' }
  })
);
```

### Configuration Management

```typescript
const configTemplate = {
  database: {
    host: '$env.DB_HOST',
    port: 'js:{parseInt(env.DB_PORT) || 5432}',
    name: '$env.DB_NAME',
    ssl: 'js:{env.NODE_ENV === "production"}'
  },
  api: {
    baseUrl: '#{env.API_BASE_URL}',
    timeout: 'js:{parseInt(env.API_TIMEOUT) || 30000}',
    retries: 'js:{parseInt(env.API_RETRIES) || 3}'
  },
  features: {
    enableCache: 'js:{env.CACHE_ENABLED === "true"}',
    logLevel: '$env.LOG_LEVEL',
    maxUsers: 'js:{parseInt(env.MAX_CONCURRENT_USERS) || 1000}'
  }
};

const config = processTemplates(configTemplate, { env: process.env });
```

### Dynamic Form Generation

```typescript
const formTemplate = {
  fields: [
    {
      name: 'username',
      type: 'text',
      label: '#{labels.username}',
      required: 'js:{validation.username.required}',
      minLength: '$validation.username.minLength',
      placeholder: '#{placeholders.username}'
    },
    {
      name: 'email',
      type: 'email',
      label: '#{labels.email}',
      required: 'js:{validation.email.required}',
      pattern: '$validation.email.pattern'
    },
    {
      name: 'age',
      type: 'number',
      label: '#{labels.age}',
      min: '$validation.age.min',
      max: '$validation.age.max',
      step: 'js:{validation.age.step || 1}'
    }
  ],
  submitButton: {
    text: '#{buttons.submit}',
    disabled: 'js:{!form.isValid}',
    loading: '$form.isSubmitting'
  }
};

const formConfig = processTemplates(formTemplate, {
  labels: { username: 'Username', email: 'Email', age: 'Age' },
  placeholders: { username: 'Enter your username' },
  buttons: { submit: 'Create Account' },
  validation: {
    username: { required: true, minLength: 3 },
    email: { required: true, pattern: '^[^@]+@[^@]+\\.[^@]+$' },
    age: { min: 13, max: 120 }
  },
  form: { isValid: true, isSubmitting: false }
});
```

### API Response Transformation

```typescript
const responseTemplate = {
  success: 'js:{response.status >= 200 && response.status < 300}',
  data: '$response.data',
  pagination: {
    page: '$response.page',
    totalPages: 'js:{Math.ceil(response.total / response.limit)}',
    hasNext: 'js:{response.page < Math.ceil(response.total / response.limit)}',
    hasPrev: 'js:{response.page > 1}'
  },
  meta: {
    timestamp: 'js:{new Date().toISOString()}',
    processingTime: 'js:{Date.now() - startTime}',
    requestId: '$response.requestId',
    version: '$api.version'
  }
};

const transformedResponse = processTemplates(responseTemplate, {
  response: apiResponse,
  startTime: requestStartTime,
  api: { version: '2.1' }
});
```

## TypeScript Support

The library includes comprehensive TypeScript definitions:

```typescript
interface ParseResult {
  groups: string[];
  baseUrl: string;
  expressions: Array<{
    type: 'hash' | 'javascript' | 'variable' | 'query';
    content: string;
    fullMatch: string;
  }>;
}

// Type-safe evaluation
function evalStringExpression<T = any>(value: string, obj: T): any;
function processTemplates<T = any, U = any>(obj: T, storeObj: U): T;
function parseTemplateUrl(url: string): ParseResult;
```