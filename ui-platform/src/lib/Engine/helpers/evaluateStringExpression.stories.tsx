import { <PERSON>a, StoryObj } from '@storybook/react';
import { EvaluateStringExpressionDemo } from './evaluateStringExpression.demo';

const meta: Meta<typeof EvaluateStringExpressionDemo> = {
  component: EvaluateStringExpressionDemo,
  title: 'Engine/String Expression Processor',
  parameters: {
    docs: {
      description: {
        component: `
# String Expression Processing Engine

A powerful template string processing system that supports multiple expression syntaxes for dynamic content generation.

## Features

- **Template Strings (#)**: Process template strings with variable substitution
- **Store Paths ($)**: Access nested object properties with query support
- **JavaScript Evaluation (js:)**: Execute JavaScript expressions with data context
- **Parameter Extraction (@param)**: Extract and process parameter values
- **URL Template Parsing**: Parse and analyze complex URL templates
- **Recursive Processing**: Process nested objects and arrays recursively

## Expression Syntaxes

### 1. Template Strings (\`#\`)
Process template strings with curly brace variable substitution:
\`\`\`
'#{user.name}' → 'Alice'
'#Hello {user.name}!' → 'Hello Alice!'
\`\`\`

### 2. Store Paths (\`$\`)
Access object properties with optional array filtering:
\`\`\`
'$user.name' → 'Alice'
'$products?category=tools' → [filtered array]
\`\`\`

### 3. JavaScript Evaluation (\`js:\`)
Execute JavaScript code with access to data context:
\`\`\`
'js:{user.age * 2}' → 60
'js:{products.length}' → 3
\`\`\`

### 4. Parameter Extraction (\`@param\`)
Extract values from parameter objects:
\`\`\`
'@param' → entire parameter object
'@param:{user.name}' → 'Alice'
\`\`\`

## API Reference

### \`evalStringExpression(value: string, obj: any): any\`

Evaluates a single string expression against a data object.

**Parameters:**
- \`value\`: The expression string to evaluate
- \`obj\`: The data object to use for evaluation

**Returns:** The processed result

### \`processTemplates(obj: any, storeObj: any): any\`

Recursively processes template expressions in objects and arrays.

**Parameters:**
- \`obj\`: The object/array/value to process
- \`storeObj\`: The data store for template evaluation

**Returns:** A new object with all templates processed

### \`parseTemplateUrl(url: string): ParseResult\`

Parses a URL template string and extracts expression information.

**Parameters:**
- \`url\`: The template URL to parse

**Returns:** Object containing:
- \`groups\`: Array of URL segments
- \`baseUrl\`: The base URL pattern
- \`expressions\`: Array of found expressions with metadata

## Usage Examples

### Basic Expression Evaluation
\`\`\`typescript
const store = { user: { name: 'Alice', age: 30 } };

// Template string
evalStringExpression('#{user.name}', store); // 'Alice'

// Store path
evalStringExpression('$user.name', store); // 'Alice'

// JavaScript
evalStringExpression('js:{user.age * 2}', store); // 60
\`\`\`

### Complex Object Processing
\`\`\`typescript
const template = {
  title: 'User: #{user.name}',
  isAdult: 'js:{user.age >= 18}',
  details: {
    email: '$user.email',
    preferences: ['#{user.theme}', '#{user.language}']
  }
};

const result = processTemplates(template, store);
\`\`\`

### URL Template Parsing
\`\`\`typescript
const url = '{api}/users/#{userId}?token=$authToken';
const parsed = parseTemplateUrl(url);
// Returns detailed information about URL structure and expressions
\`\`\`

## Error Handling

The system is designed to handle errors gracefully:
- Invalid JavaScript expressions return the original template
- Missing object properties return appropriate defaults
- Malformed queries are handled without throwing errors
- Circular references are protected against

## Performance Considerations

- Templates are processed on-demand (no caching)
- JavaScript expressions are executed in a sandboxed context
- Deep object traversal is optimized for common use cases
- Large arrays with queries may impact performance

## Security Notes

- JavaScript expressions run in a sandboxed environment
- No access to global objects or dangerous APIs
- Template processing is safe for user-generated content
- URL parsing handles malformed input gracefully
        `,
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof EvaluateStringExpressionDemo>;

/**
 * Interactive demo showcasing all features of the string expression processing engine.
 * Try different expression syntaxes and see real-time results.
 */
export const InteractiveDemo: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: `
This interactive demo demonstrates all the capabilities of the string expression processing engine.

**Key Features Demonstrated:**
- Real-time expression evaluation with live preview
- Comprehensive examples of all expression syntaxes
- Complex nested object and array processing
- URL template parsing with detailed analysis
- Error handling and edge case management

**Try It Out:**
Use the interactive tester to experiment with different expression patterns and see immediate results.
        `,
      },
    },
  },
};

/**
 * Examples of basic expression syntaxes and their outputs.
 */
export const BasicExpressions: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: `
Demonstrates the four main expression syntaxes:

1. **Template Strings (#)**: \`#{user.name}\` → \`Alice\`
2. **Store Paths ($)**: \`$user.email\` → \`<EMAIL>\`
3. **JavaScript (js:)**: \`js:{user.age * 2}\` → \`60\`
4. **Parameters (@param)**: \`@param:{user.name}\` → \`Alice\`

Each syntax serves different use cases and can be combined for powerful template processing.
        `,
      },
    },
  },
};

/**
 * Complex examples showing nested object and array processing.
 */
export const ComplexTemplates: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: `
Shows how the engine handles complex nested structures:

- **Nested Objects**: Templates within objects at multiple levels
- **Array Processing**: Template evaluation within arrays
- **Mixed Types**: Combining different expression syntaxes
- **Dynamic Calculations**: JavaScript expressions with data manipulation

Perfect for configuration objects, API responses, and dynamic content generation.
        `,
      },
    },
  },
};

/**
 * URL template parsing examples with detailed breakdown.
 */
export const UrlTemplatesParsing: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: `
Demonstrates URL template parsing capabilities:

- **Expression Detection**: Identifies different expression types in URLs
- **Structure Analysis**: Breaks down URL components and patterns
- **Base URL Extraction**: Separates base URL from dynamic parts
- **Query Parameter Handling**: Processes complex query structures

Useful for API endpoint generation, dynamic routing, and URL construction.
        `,
      },
    },
  },
};

/**
 * Examples showing error handling and edge cases.
 */
export const ErrorHandling: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: `
Demonstrates robust error handling:

- **Missing Properties**: Graceful handling of undefined values
- **Invalid JavaScript**: Safe fallback for malformed expressions
- **Circular References**: Protection against infinite loops
- **Malformed Templates**: Sensible defaults for invalid syntax

The system prioritizes stability and predictable behavior over strict validation.
        `,
      },
    },
  },
};

/**
 * Performance benchmarks and optimization examples.
 */
export const PerformanceExamples: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: `
Performance considerations and optimization strategies:

- **Large Object Processing**: Efficient handling of complex data structures
- **Array Queries**: Optimized filtering and search operations
- **Recursive Processing**: Balanced depth-first traversal
- **Memory Management**: Minimal memory footprint for template processing

Best practices for high-performance template processing in production environments.
        `,
      },
    },
  },
};

/**
 * Real-world use cases and integration examples.
 */
export const RealWorldExamples: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: `
Practical applications and integration patterns:

- **Configuration Management**: Dynamic config object generation
- **API Response Processing**: Transform API data with templates
- **User Interface Generation**: Dynamic UI content creation
- **Report Generation**: Template-based report creation
- **Email Templates**: Dynamic email content generation

These examples show how to integrate the expression engine into real applications.
        `,
      },
    },
  },
};

/**
 * Migration guide and upgrade considerations.
 */
export const MigrationGuide: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: `
**Migration from Legacy Systems:**

If you're upgrading from a previous templating system:

1. **Syntax Changes**: Update expression prefixes to new format
2. **API Updates**: Adjust function calls to new parameter structure
3. **Error Handling**: Review error handling patterns
4. **Performance**: Test with production data volumes

**Breaking Changes:**
- Expression syntax has been standardized
- Error handling behavior has improved
- Performance characteristics may differ

**Backwards Compatibility:**
- Most existing templates will continue to work
- Deprecated patterns will show warnings
- Migration utilities available for bulk updates
        `,
      },
    },
  },
};
