import { beforeEach, describe, expect, it } from 'vitest';
import {
  evalStringExpression,
  parseTemplateUrl,
  processTemplates,
} from './evaluateStringExpression';

describe('evalStringExpression', () => {
  let mockStore: any;

  beforeEach(() => {
    mockStore = {
      user: {
        name: 'Alice',
        age: 30,
        email: '<EMAIL>',
        preferences: { theme: 'dark', language: 'en' },
      },
      company: '4SURE',
      api: { baseUrl: 'https://api.example.com', version: 'v1' },
      arr: [
        { x: 1, name: 'first' },
        { x: 2, name: 'second' },
      ],
      value: 42,
      products: [
        { id: 1, name: 'Widget A', price: 10.99, category: 'tools' },
        { id: 2, name: 'Widget B', price: 15.99, category: 'electronics' },
      ],
      emptyArray: [],
      nullValue: null,
      undefinedValue: undefined,
      zeroValue: 0,
      falseValue: false,
      emptyString: '',
    };
  });

  describe('Non-string inputs', () => {
    it('returns the original value if not a string', () => {
      expect(evalStringExpression(123, mockStore)).toBe(123);
      expect(evalStringExpression(null, mockStore)).toBeNull();
      expect(evalStringExpression(undefined, mockStore)).toBeUndefined();
      expect(evalStringExpression(true, mockStore)).toBe(true);
      expect(evalStringExpression([], mockStore)).toEqual([]);
      expect(evalStringExpression({}, mockStore)).toEqual({});
    });
  });

  describe('@param syntax', () => {
    it('returns param if value is "@param"', () => {
      const param = { foo: 'bar', nested: { value: 42 } };
      expect(evalStringExpression('@param', param)).toBe(param);
    });

    it('returns nested param value for "@param:{path}"', () => {
      const param = {
        foo: { bar: 'baz' },
        deep: { nested: { value: 'found' } },
      };
      expect(evalStringExpression('@param:{foo.bar}', param)).toBe('baz');
      expect(evalStringExpression('@param:{deep.nested.value}', param)).toBe(
        'found'
      );
    });

    it('returns undefined for missing nested param', () => {
      const param = { foo: {} };
      expect(evalStringExpression('@param:{foo.bar}', param)).toBeUndefined();
      expect(
        evalStringExpression('@param:{missing.path}', param)
      ).toBeUndefined();
    });

    it('handles @param with various data types', () => {
      const param = {
        number: 42,
        boolean: true,
        array: [1, 2, 3],
        object: { nested: 'value' },
      };
      expect(evalStringExpression('@param:{number}', param)).toBe(42);
      expect(evalStringExpression('@param:{boolean}', param)).toBe(true);
      expect(evalStringExpression('@param:{array}', param)).toEqual([1, 2, 3]);
      expect(evalStringExpression('@param:{object}', param)).toEqual({
        nested: 'value',
      });
    });
  });

  describe('$ syntax (store paths)', () => {
    it('returns store value for simple paths', () => {
      expect(evalStringExpression('$user.name', mockStore)).toBe('Alice');
      expect(evalStringExpression('$company', mockStore)).toBe('4SURE');
      expect(evalStringExpression('$value', mockStore)).toBe(42);
    });

    it('returns store value for nested paths', () => {
      expect(evalStringExpression('$user.preferences.theme', mockStore)).toBe(
        'dark'
      );
      expect(evalStringExpression('$api.baseUrl', mockStore)).toBe(
        'https://api.example.com'
      );
    });

    it('handles falsy values correctly', () => {
      expect(evalStringExpression('$nullValue', mockStore)).toBeNull();
      expect(
        evalStringExpression('$undefinedValue', mockStore)
      ).toBeUndefined();
      expect(evalStringExpression('$zeroValue', mockStore)).toBe(0);
      expect(evalStringExpression('$falseValue', mockStore)).toBe(false);
      expect(evalStringExpression('$emptyString', mockStore)).toBe('');
    });

    it('returns empty string for missing paths', () => {
      expect(evalStringExpression('$missing.path', mockStore)).toBe('');
      expect(evalStringExpression('$user.missing', mockStore)).toBe('');
    });

    it('handles array queries with ?', () => {
      expect(evalStringExpression('$arr?x=2', mockStore)).toEqual([
        { x: 2, name: 'second' },
      ]);
      expect(
        evalStringExpression('$products?category=tools', mockStore)
      ).toEqual([{ id: 1, name: 'Widget A', price: 10.99, category: 'tools' }]);
    });

    it('handles array queries with no matches', () => {
      expect(evalStringExpression('$arr?x=999', mockStore)).toEqual([]);
      expect(
        evalStringExpression('$products?category=nonexistent', mockStore)
      ).toEqual([]);
    });

    it('handles array queries on empty arrays', () => {
      expect(evalStringExpression('$emptyArray?x=1', mockStore)).toEqual([]);
    });

    it('handles malformed queries gracefully', () => {
      // Should not throw errors for malformed queries
      expect(() =>
        evalStringExpression('$arr?invalid', mockStore)
      ).not.toThrow();
      expect(() => evalStringExpression('$arr?', mockStore)).not.toThrow();
    });
  });

  describe('# syntax (template strings)', () => {
    it('returns template-processed value for simple paths', () => {
      expect(evalStringExpression('#user.name', mockStore)).toBe('Alice');
      expect(evalStringExpression('#company', mockStore)).toBe('4SURE');
    });

    it('handles complex template strings', () => {
      expect(evalStringExpression('#Hello {user.name}!', mockStore)).toBe(
        'Hello Alice!'
      );
      expect(
        evalStringExpression(
          '#User {user.name} is {user.age} years old',
          mockStore
        )
      ).toBe('User Alice is 30 years old');
    });

    it('handles missing template variables gracefully', () => {
      expect(evalStringExpression('#Hello {user.missing}!', mockStore)).toBe(
        'Hello {user.missing}!'
      );
    });

    it('handles nested template variables', () => {
      expect(evalStringExpression('#{user.preferences.theme}', mockStore)).toBe(
        'dark'
      );
      expect(
        evalStringExpression('#{api.baseUrl}/#{api.version}', mockStore)
      ).toBe('https://api.example.com/v1');
    });
  });

  describe('js: syntax (JavaScript evaluation)', () => {
    it('evaluates simple JavaScript expressions', () => {
      expect(evalStringExpression('js:{2*3}', mockStore)).toBe(6);
      expect(evalStringExpression('js:{1+2}', mockStore)).toBe(3);
      expect(evalStringExpression('js:{Math.max(1,2,3)}', mockStore)).toBe(3);
    });

    it('evaluates JavaScript with store data access', () => {
      expect(evalStringExpression('js:{user.age * 2}', mockStore)).toBe(60);
      expect(
        evalStringExpression('js:{user.name.toUpperCase()}', mockStore)
      ).toBe('ALICE');
    });

    it('evaluates complex JavaScript expressions', () => {
      expect(evalStringExpression('js:{products.length}', mockStore)).toBe(2);
      expect(
        evalStringExpression(
          'js:{products.filter(p => p.category === "tools").length}',
          mockStore
        )
      ).toBe(1);
    });

    it('handles JavaScript expressions with return statements', () => {
      expect(evalStringExpression('js:return 2*3', mockStore)).toBe(6);
      expect(evalStringExpression('js:return user.age > 18', mockStore)).toBe(
        true
      );
    });

    it('handles JavaScript errors gracefully', () => {
      // Should not throw for invalid JS
      expect(() =>
        evalStringExpression('js:{invalid.syntax.here}', mockStore)
      ).not.toThrow();
    });
  });

  describe('Plain strings', () => {
    it('returns value as-is for plain strings', () => {
      expect(evalStringExpression('hello', mockStore)).toBe('hello');
      expect(evalStringExpression('', mockStore)).toBe('');
      expect(evalStringExpression('   ', mockStore)).toBe('   ');
    });

    it('returns strings that look like templates but are not', () => {
      expect(evalStringExpression('not a template', mockStore)).toBe(
        'not a template'
      );
      expect(evalStringExpression('#{not a template}', mockStore)).toBe(
        '#{not a template}'
      );
    });
  });

  describe('Edge cases', () => {
    it('handles empty store object', () => {
      expect(evalStringExpression('$missing', {})).toBe('');
      expect(evalStringExpression('#missing', {})).toBe('missing');
      expect(evalStringExpression('js:{1+1}', {})).toBe(2);
    });

    it('handles null/undefined store', () => {
      expect(evalStringExpression('$missing', null)).toBe('');
      expect(evalStringExpression('#missing', undefined)).toBe('missing');
    });

    it('handles circular references in objects', () => {
      const circular: any = { name: 'test' };
      circular.self = circular;

      expect(evalStringExpression('$name', circular)).toBe('test');
      expect(() => evalStringExpression('$self.name', circular)).not.toThrow();
    });
  });
});

describe('processTemplates', () => {
  const storeObj = {
    user: { name: 'Alice', age: 30, email: '<EMAIL>' },
    company: '4SURE',
    value: 42,
    arr: [1, 2, 3],
    nested: { arr: [{ x: 1 }, { x: 2 }] },
    products: [
      { id: 1, name: 'Widget A', price: 10.99 },
      { id: 2, name: 'Widget B', price: 15.99 },
    ],
  };

  describe('String processing', () => {
    it('processes string templates', () => {
      expect(processTemplates('Hello #{user.name}!', storeObj)).toBe(
        'Hello Alice!'
      );
      expect(processTemplates('$user.email', storeObj)).toBe(
        '<EMAIL>'
      );
      expect(processTemplates('js:{user.age * 2}', storeObj)).toBe(60);
    });

    it('processes mixed template types', () => {
      expect(processTemplates('@param:{user.name}', storeObj)).toBe('Alice');
    });
  });

  describe('Array processing', () => {
    it('processes array templates', () => {
      expect(
        processTemplates(['#{user.name}', '#{company}'], storeObj)
      ).toEqual(['Alice', '4SURE']);
      expect(processTemplates(['$user.name', '$value'], storeObj)).toEqual([
        'Alice',
        42,
      ]);
    });

    it('processes mixed arrays', () => {
      expect(processTemplates(['#{user.name}', 42, 'plain'], storeObj)).toEqual(
        ['Alice', 42, 'plain']
      );
    });

    it('processes nested arrays', () => {
      const input = [
        ['#{user.name}', '#{company}'],
        ['$value', 'js:{1+1}'],
      ];
      expect(processTemplates(input, storeObj)).toEqual([
        ['Alice', '4SURE'],
        [42, 2],
      ]);
    });
  });

  describe('Object processing', () => {
    it('processes object templates', () => {
      const input = {
        user: '#{user.name}',
        company: '#{company}',
        age: '#{user.age}',
      };
      expect(processTemplates(input, storeObj)).toEqual({
        user: 'Alice',
        company: '4SURE',
        age: 30,
      });
    });

    it('processes nested objects', () => {
      const input = {
        user: {
          name: '#{user.name}',
          email: '$user.email',
          doubleAge: 'js:{user.age * 2}',
        },
        meta: {
          company: '#{company}',
          timestamp: 'js:{new Date().getFullYear()}',
        },
      };

      const result = processTemplates(input, storeObj);
      expect(result.user.name).toBe('Alice');
      expect(result.user.email).toBe('<EMAIL>');
      expect(result.user.doubleAge).toBe(60);
      expect(result.meta.company).toBe('4SURE');
      expect(typeof result.meta.timestamp).toBe('number');
    });
  });

  describe('Complex nested structures', () => {
    it('processes deeply nested objects and arrays', () => {
      const input = {
        arr: ['#{user.name}', 5, { deep: '#{company}' }],
        nested: {
          arr: ['#{user.age}', '#{company}'],
          obj: {
            level1: {
              level2: {
                value: 'js:{user.age + value}',
              },
            },
          },
        },
      };

      const result = processTemplates(input, storeObj);
      expect(result.arr).toEqual(['Alice', 5, { deep: '4SURE' }]);
      expect(result.nested.arr).toEqual([30, '4SURE']);
      expect(result.nested.obj.level1.level2.value).toBe(72);
    });

    it('processes arrays of objects with templates', () => {
      const input = [
        { name: '#{user.name}', age: '#{user.age}' },
        { company: '#{company}', value: 'js:{value * 2}' },
      ];

      const result = processTemplates(input, storeObj);
      expect(result).toEqual([
        { name: 'Alice', age: 30 },
        { company: '4SURE', value: 84 },
      ]);
    });
  });

  describe('Non-template values', () => {
    it('returns non-template values as is', () => {
      expect(processTemplates(123, storeObj)).toBe(123);
      expect(processTemplates(null, storeObj)).toBe(null);
      expect(processTemplates(undefined, storeObj)).toBe(undefined);
      expect(processTemplates(true, storeObj)).toBe(true);
      expect(processTemplates('plain string', storeObj)).toBe('plain string');
    });

    it('processes mixed template and non-template values', () => {
      const input = {
        template: '#{user.name}',
        plain: 'hello',
        number: 42,
        boolean: true,
        null: null,
      };

      expect(processTemplates(input, storeObj)).toEqual({
        template: 'Alice',
        plain: 'hello',
        number: 42,
        boolean: true,
        null: null,
      });
    });
  });

  describe('Empty structures', () => {
    it('handles empty objects and arrays', () => {
      expect(processTemplates({}, storeObj)).toEqual({});
      expect(processTemplates([], storeObj)).toEqual([]);
    });

    it('handles empty nested structures', () => {
      const input = { empty: {}, emptyArr: [], nested: { empty: {} } };
      expect(processTemplates(input, storeObj)).toEqual({
        empty: {},
        emptyArr: [],
        nested: { empty: {} },
      });
    });
  });

  describe('Error handling', () => {
    it('handles errors gracefully', () => {
      const input = {
        valid: '#{user.name}',
        invalid: 'js:{throw new Error("test")}',
        missing: '#{missing.path}',
      };

      expect(() => processTemplates(input, storeObj)).not.toThrow();
    });

    it('handles circular references', () => {
      const circular: any = { name: 'test' };
      circular.self = circular;

      const input = { name: '#{name}', circular: '#{self}' };
      expect(() => processTemplates(input, circular)).not.toThrow();
    });
  });
});

describe('parseTemplateUrl', () => {
  describe('Basic parsing', () => {
    it('parses simple URLs with expressions', () => {
      const url = '{api}/users#userId$token';
      const result = parseTemplateUrl(url);

      expect(result).toHaveProperty('groups');
      expect(result).toHaveProperty('baseUrl');
      expect(result).toHaveProperty('expressions');
      expect(Array.isArray(result.groups)).toBe(true);
      expect(Array.isArray(result.expressions)).toBe(true);
    });

    it('handles URLs with no expressions', () => {
      const url = 'https://example.com/path';
      const result = parseTemplateUrl(url);

      expect(result.groups.length).toBeGreaterThan(0);
      expect(result.expressions.length).toBe(0);
      expect(result.groups[0]).toBe(url);
    });

    it('handles empty strings', () => {
      const result = parseTemplateUrl('');
      expect(result.groups).toEqual([]);
      expect(result.baseUrl).toBe('');
      expect(result.expressions).toEqual([]);
    });
  });

  describe('Expression types', () => {
    it('parses hash expressions', () => {
      const url = 'https://example.com#hash1#hash2';
      const result = parseTemplateUrl(url);

      const hashExpressions = result.expressions.filter(
        (e) => e.type === 'hash'
      );
      expect(hashExpressions.length).toBe(2);
      expect(hashExpressions[0].content).toBe('hash1');
      expect(hashExpressions[1].content).toBe('hash2');
    });

    it('parses javascript expressions', () => {
      const url = 'https://example.com/js:{return 1+1}/path';
      const result = parseTemplateUrl(url);

      const jsExpressions = result.expressions.filter(
        (e) => e.type === 'javascript'
      );
      expect(jsExpressions.length).toBe(1);
      expect(jsExpressions[0].content).toBe('{return 1+1}');
    });

    it('parses variable expressions', () => {
      const url = 'https://example.com/$var1/$var2.property';
      const result = parseTemplateUrl(url);

      const varExpressions = result.expressions.filter(
        (e) => e.type === 'variable'
      );
      expect(varExpressions.length).toBe(2);
      expect(varExpressions[0].content).toBe('var1');
      expect(varExpressions[1].content).toBe('var2.property');
    });

    it('parses query parameter expressions', () => {
      const url = 'https://example.com/path?foo=$bar&baz=$qux';
      const result = parseTemplateUrl(url);

      const queryExpressions = result.expressions.filter(
        (e) => e.type === 'query'
      );
      const varExpressions = result.expressions.filter(
        (e) => e.type === 'variable'
      );

      expect(queryExpressions.length).toBe(2);
      expect(varExpressions.length).toBe(2);
      expect(queryExpressions[0].content).toBe('?foo=');
      expect(queryExpressions[1].content).toBe('&baz=');
    });
  });

  describe('Complex URL patterns', () => {
    it('parses mixed expression types', () => {
      const url = '{api}/users#userId$token?filter=$filter&js:{limit}';
      const result = parseTemplateUrl(url);

      expect(result.expressions.some((e) => e.type === 'hash')).toBe(true);
      expect(result.expressions.some((e) => e.type === 'variable')).toBe(true);
      expect(result.expressions.some((e) => e.type === 'query')).toBe(true);
      expect(result.expressions.some((e) => e.type === 'javascript')).toBe(
        true
      );
    });

    it('parses multiple hashes and variables', () => {
      const url = 'https://example.com#hash1#hash2$var1$var2';
      const result = parseTemplateUrl(url);

      expect(result.expressions.filter((e) => e.type === 'hash').length).toBe(
        2
      );
      expect(
        result.expressions.filter((e) => e.type === 'variable').length
      ).toBe(2);
    });

    it('parses complex real-world URLs', () => {
      const url =
        '{protocol}://{host}/api/{version}/users/{userId}#metadata$authToken?search=$query&limit=js:{Math.min(limit, 100)}&offset=$offset';
      const result = parseTemplateUrl(url);

      expect(result.expressions.length).toBeGreaterThan(0);
      expect(result.groups.length).toBeGreaterThan(0);
    });
  });

  describe('Base URL extraction', () => {
    it('extracts base URL correctly', () => {
      const url = '{api.baseUrl}/users#userId';
      const result = parseTemplateUrl(url);

      expect(result.baseUrl).toBe('{api.baseUrl}/users');
    });

    it('handles URLs without base URL patterns', () => {
      const url = 'https://example.com/path#hash';
      const result = parseTemplateUrl(url);

      expect(result.baseUrl).toBe('');
    });
  });

  describe('Edge cases', () => {
    it('handles malformed URLs gracefully', () => {
      const malformedUrls = [
        '#',
        "'",
        'js:',
        '{incomplete',
        'multiple##hashes',
        '$variables',
        'js:{unclosed',
      ];

      malformedUrls.forEach((url) => {
        expect(() => parseTemplateUrl(url)).not.toThrow();
      });
    });

    it('handles URLs with special characters', () => {
      const url =
        'https://example.com/path?q=hello%20world&encoded=%3Ctest%3E#fragment';
      const result = parseTemplateUrl(url);

      expect(result.groups.length).toBeGreaterThan(0);
      expect(() => parseTemplateUrl(url)).not.toThrow();
    });

    it('handles very long URLs', () => {
      const longUrl = 'https://example.com/' + 'a'.repeat(1000) + '#hash$var';
      const result = parseTemplateUrl(longUrl);

      expect(result.expressions.length).toBe(2);
    });

    it('handles URLs with no expressions but query parameters', () => {
      const url = 'https://example.com/path?foo=bar&baz=qux';
      const result = parseTemplateUrl(url);

      expect(result.expressions.length).toBe(0);
      expect(result.groups.length).toBe(1);
    });
  });

  describe('Groups and expressions consistency', () => {
    it('maintains consistency between groups and expressions', () => {
      const url = 'https://example.com#hash$var?param=$value';
      const result = parseTemplateUrl(url);

      // Count template expressions in groups
      const templateGroups = result.groups.filter(
        (g) => g.startsWith('#') || g.startsWith("'") || g.startsWith('js:')
      );

      // Should have some correlation with expressions
      expect(result.expressions.length).toBeGreaterThan(0);
    });

    it('handles adjacent expressions correctly', () => {
      const url = 'test#hash1#hash2$var1$var2';
      const result = parseTemplateUrl(url);

      expect(result.expressions.length).toBe(4);
      expect(result.groups.length).toBeGreaterThan(4);
    });
  });
});
