import { jwtDecode } from "jwt-decode";
import { dropLast } from "ramda";
import cryptoJS from "crypto-js";

const extractDateTime = (datetimeString: string) => {
  const dateTime = new Date(datetimeString);
  const year = dateTime.getFullYear();
  const month = String(dateTime.getMonth() + 1).padStart(2, "0");
  const day = String(dateTime.getDate()).padStart(2, "0");
  const date = `${year}-${month}-${day}`;
  const time = dateTime.toLocaleTimeString([], { hour12: false });
  return { date, time };
};

const appointmentTypes: any = {
  1: "A.S.A.P",
  2: "Any Time",
  3: "Between",
  4: "After",
  5: "Before",
  6: "At",
};

const transformClaimDescription = (job: any) => {
  const desc: string = job?.claim?.loan_information?.claimdescription || "";
  if (desc?.endsWith(".")) {
    return (dropLast(1, desc.split("")) as string[])
      .join("")
      .replace(new RegExp("\n", "g"), "<br />");
  } else {
    return desc.replace(new RegExp("\n", "g"), "<br />");
  }
};

function transformCoordinates(location: string) {
  const [x, y] = location.split(",").map((pt) => parseFloat(pt));
  return `${x} , ${y}`;
}

const getExcessHow = (num: string | number) => {
  const mode: any = {
    1: "Cash",
    2: "EFT",
    3: "Off Invoice",
    4: "Access Bond",
    5: "Credit Card",
    6: "Debit Card",
    7: "Transaction Account",
    8: "Bond Account",
  };
  return num ? mode[num] : "";
};

const encrypt = (obj: any) => {
  const str = JSON.stringify(obj);
  return cryptoJS.AES.encrypt(str, "shenanigans").toString();
};

// MENUITEM COMMAND FUNCTIONS //
const getPdfJobDetails = (job: any) => {
  const getCellNumber = (str: string): string | undefined =>
    str && str !== "" ? str : undefined;
  let appointmentDate = "",
    appointmentTime = "";
  if (
    job?.office_use?.requestedappointmentdate ||
    job?.office_use?.requestedappointmenttime
  ) {
    appointmentDate = job?.office_use?.requestedappointmentdate;
    appointmentTime =
      `${job?.office_use?.appointment_type} ${job?.office_use?.requestedappointmenttime}` +
      (job?.office_use.appointment_type_id === 3 &&
      job?.office_use?.requestedappointmentendtime
        ? ` - ${job?.office_use?.requestedappointmentendtime}`
        : "");
  } else if (job?.appointment && job?.appointment[0]) {
    const latestAppointment = job.appointment[0];
    const datetime = extractDateTime(latestAppointment.range_start);
    appointmentDate = datetime.date;
    appointmentTime = `${
      appointmentTypes[latestAppointment.appointment_type]
    } ${datetime.time}`;
  }
  return {
    //voucher info
    voucher_type: job?.claim?.loan_information?.voucher_type,
    voucher_address: job?.claim?.address,
    voucher_phone: job?.claim?.applicant?.contact_number,
    special_instructions: job?.claim?.loan_information?.whatmatters,
    claim_id: job?.claim?.id,

    claim_type: job?.claim?.type,
    handler:
      job?.handler_details?.full_name ?? job?.claim?.loan_information?.handler,
    policy_number: job?.claim?.loan_information?.policy_number,
    handlercontact:
      job?.handler_details?.contact_number ??
      job?.claim?.loan_information?.handlercontact,
    mid: job?.claim?.mid,
    id: job?.id,
    handleremail:
      job?.handler_details?.email_address ??
      job.claim.loan_information.handleremail,
    what_matters: job?.claim?.loan_information?.whatmatters,
    details: transformClaimDescription(job),
    appointment: {
      date: appointmentDate,
      time: appointmentTime,
      onsiteperson: job?.claim?.loan_information?.onsiteperson,
      contact_number: job?.claim?.loan_information?.onsitecontact,
      onsitenotes: job?.claim.loan_information.onsitenotes,
    },
    insured_address: {
      street: job?.claim?.loan_information?.propertystreetaddress,
      suburb: job?.claim?.loan_information?.propertysuburb,
      city: job?.claim?.loan_information?.propertycity,
      province: job?.claim?.loan_information?.claimprovince,
      gps_coordinates: transformCoordinates(job?.claim?.location),
    },
    property_details: job?.claim.loan_information.suminsured,
    insured_details: {
      name: `${job?.claim?.applicant?.first_name} ${job?.claim?.applicant?.surname}`,
      cellnumber:
        getCellNumber(job?.claim?.applicant?.contact_number) ??
        getCellNumber(job?.claim?.loan_information?.contactnumber) ??
        getCellNumber(job?.claim?.loan_information?.cellnumber) ??
        getCellNumber(job?.claim?.loan_information?.onsitecontact) ??
        "N/A",
    },
    excess_details: {
      standard_excess: job?.excess[0]?.amount,
      excess_how: getExcessHow(job?.excess[0]?.how_collect),
    },
  };
};

export const getPdfUrl = async (
  token: string,
  job_id: string, 
  apiBaseUrl: string,
  pdfUrl: string
): Promise<string> => {
  const decodedToken: any = jwtDecode(token);
  const response = await fetch(`${apiBaseUrl}/v1/job_action/get_job`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `token ${token}`,
      },
      body: JSON.stringify({ job_id }),
    }
  );

  const res = await response.json();
  const job = res.payload;
  const encryptedData = encrypt({
    client: decodedToken.client,
    job: getPdfJobDetails(job),
  });
  // window.open(
  //   `${import.meta.env.VITE_PDF_URL}templates/${
  //     'job-card'
  //   }/${encodeURIComponent(encryptedData)}`,
  //   '_blank'
  // );

  return `${
    pdfUrl
  }templates/${"job-card"}/${encodeURIComponent(encryptedData)}`;
};

export const openPdfUrl = async (token: string, job_id: string, apiBaseUrl: string, pdfUrl: string) => {
   // 1️⃣ Immediately create the new tab as part of the user-initiated click.
  //    Browsers treat this as an allowed pop-up.
  const popup: any = window.open('about:blank', '_blank');

  try {
    // 2️⃣ Wait for the Promise that gives you the real URL.
    const url = await getPdfUrl(token, job_id, apiBaseUrl, pdfUrl);

    // 3️⃣ Navigate the tab to the resolved URL.
    console.log({pdfUrl: url})
    popup.location.href = url;                   
  } catch (err) {
    // Optional: close the empty tab and inform the user.
    popup.close();
    console.error(err);
    alert('Sorry—could not open the file.');
  }
};

