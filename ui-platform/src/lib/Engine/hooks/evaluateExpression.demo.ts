/**
 * De<PERSON> script to test the refactored evaluateExpression function
 * This demonstrates the mixed context functionality
 */

// Mock the dependencies for testing
const mockStore = {
  user: { name: 'StoreUser', role: 'admin' },
  config: { theme: 'dark' },
  mockState: 'test-store-value'
};

const mockEvalStringExpression = (value: string, obj: any) => {
  if (value.startsWith('$')) {
    const path = value.substring(1);
    return getNestedValue(obj, path);
  } else if (value.startsWith('#')) {
    const template = value.substring(1);
    return `resolved-${template}`;
  } else if (value.startsWith('js:')) {
    const expression = value.substring(3);
    try {
      return eval(expression);
    } catch {
      return value;
    }
  }
  return value;
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

// Simplified version of the refactored evaluateExpression for testing
const evaluateExpression = (
  payload: any, 
  param?: any, 
  storeState?: any,
  visited = new WeakSet()
): any => {
  // Handle primitive types and null/undefined
  if (payload === null || payload === undefined || typeof payload !== 'object') {
    return evaluateStringExpression(payload, param, storeState);
  }

  // Circular reference detection
  if (visited.has(payload)) {
    console.warn('Circular reference detected, returning original payload');
    return payload;
  }

  // Cache store state on first call
  if (!storeState) {
    storeState = mockStore;
  }

  // Handle arrays
  if (Array.isArray(payload)) {
    visited.add(payload);
    const result = payload.map((item) => 
      evaluateExpression(item, param, storeState, visited)
    );
    visited.delete(payload);
    return result;
  }

  // Handle objects
  visited.add(payload);
  const result = Object.entries(payload).reduce((acc, [key, value]) => {
    const evaluatedKey = evaluateStringExpression(key, param, storeState);
    acc[evaluatedKey] = evaluateExpression(value, param, storeState, visited);
    return acc;
  }, {} as Record<string, any>);
  
  visited.delete(payload);
  return result;
};

const evaluateStringExpression = (
  value: any,
  param?: any,
  storeState?: any
): any => {
  if (typeof value !== 'string') {
    return value;
  }

  if (value === '@param') {
    return param;
  }

  if (value.startsWith('@param:{')) {
    const expression = value.substring(8, -1); // Remove '@param:{' and '}'
    return getNestedValue(param, expression);
  }

  if (value.startsWith('#') || value.startsWith('$') || value.startsWith('js:')) {
    return mockEvalStringExpression(value, storeState || mockStore);
  }

  if (value.includes('#{') || value.includes('${') || value.includes('js:')) {
    return mockEvalStringExpression(value, storeState || mockStore);
  }

  return value;
};

// Test cases
console.log('=== Testing Mixed Context Functionality ===\n');

// Test case 1: Basic mixed array
const test1 = evaluateExpression(
  ['@param:{user.name}', 'male'],
  { user: { name: 'Marcus' } }
);
console.log('Test 1 - Basic mixed array:');
console.log('Input:', ['@param:{user.name}', 'male']);
console.log('Param:', { user: { name: 'Marcus' } });
console.log('Result:', test1);
console.log('Expected: ["Marcus", "male"]\n');

// Test case 2: Complex mixed array
const test2 = evaluateExpression(
  ['@param:{user.name}', 'male', '#{someStoreValue}'],
  { user: { name: 'Marcus' } }
);
console.log('Test 2 - Complex mixed array:');
console.log('Input:', ['@param:{user.name}', 'male', '#{someStoreValue}']);
console.log('Param:', { user: { name: 'Marcus' } });
console.log('Result:', test2);
console.log('Expected: ["Marcus", "male", "resolved-someStoreValue"]\n');

// Test case 3: Mixed object
const test3 = evaluateExpression(
  {
    userName: '@param:{user.name}',
    gender: 'male',
    theme: '$config.theme',
    computed: 'js:2 + 3'
  },
  { user: { name: 'Marcus' } }
);
console.log('Test 3 - Mixed object:');
console.log('Result:', test3);
console.log('Expected: { userName: "Marcus", gender: "male", theme: "dark", computed: 5 }\n');

// Test case 4: Nested mixed structure
const test4 = evaluateExpression(
  {
    profile: {
      name: '@param:{user.name}',
      settings: {
        theme: '$config.theme',
        preferences: ['@param:{user.role}', 'notifications']
      }
    }
  },
  { user: { name: 'Marcus', role: 'user' } }
);
console.log('Test 4 - Nested mixed structure:');
console.log('Result:', JSON.stringify(test4, null, 2));

export { evaluateExpression, evaluateStringExpression };
