import { TemplateLiteralLogger } from '../../Utilities/templateLiteralLogger';
const log = TemplateLiteralLogger.createLog(
  {
    prefix: '🪵[Use Client Actions Async log]:',
    enabled: true,
    options: { style: { backgroundColor: '#efefef', color: '#017812' } },
  },
  'log'
);
/**
 * Demonstration script showing the difference between concurrent and sequential execution
 * Run this in a browser console or Node.js environment to see the timing differences
 */

// Simulate the async action execution with delays
const simulateAsyncAction = (name: string, delay: number): Promise<void> => {
  log`🚀 Starting ${name} (${delay}ms delay)`;
  return new Promise((resolve) => {
    setTimeout(() => {
      log`✅ Completed ${name}`;
      resolve();
    }, delay);
  });
};

// Demo 1: Concurrent execution (without await)
export async function demoConcurrentExecution() {
  log`\n=== DEMO 1: CONCURRENT EXECUTION (WITHOUT AWAIT) ===`;
  log`
    callClientActionAsync(action1); callClientActionAsync(action2); callClientActionAsync(action3);
  `;

  const startTime = Date.now();

  // ❌ This is what happens when you don't use await
  // All actions start immediately and run in parallel
  const promise1 = simulateAsyncAction('Action 1', 100);
  const promise2 = simulateAsyncAction('Action 2', 150);
  const promise3 = simulateAsyncAction('Action 3', 80);

  // Wait for all to complete
  await Promise.all([promise1, promise2, promise3]);

  const endTime = Date.now();
  log`⏱️  Total time: ${
    endTime - startTime
  }ms (should be ~150ms - the longest action)`;
  log`❗ Notice: All actions started immediately, completed in parallel\n`;
}

// Demo 2: Sequential execution (with await)
export async function demoSequentialExecution() {
  log`\n=== DEMO 2: SEQUENTIAL EXECUTION (WITH AWAIT) ===`;
  log`
    await callClientActionAsync(action1); await callClientActionAsync(action2); await callClientActionAsync(action3);
  `;

  const startTime = Date.now();

  // ✅ This is what happens when you use await
  // Each action waits for the previous one to complete
  await simulateAsyncAction('Action 1', 100);
  await simulateAsyncAction('Action 2', 150);
  await simulateAsyncAction('Action 3', 80);

  const endTime = Date.now();
  log`⏱️  Total time: ${
    endTime - startTime
  }ms (should be ~330ms - sum of all actions)`;
  log`✅ Notice: Actions executed one after another, in order\n`;
}

// Demo 3: Using helper functions
export async function demoHelperFunctions() {
  log`\n=== DEMO 3: USING HELPER FUNCTIONS ===`;

  const actions = [
    () => simulateAsyncAction('Helper Action 1', 50),
    () => simulateAsyncAction('Helper Action 2', 75),
    () => simulateAsyncAction('Helper Action 3', 60),
  ];

  // Sequential helper
  log`📋 callClientActionsSequentially([action1, action2, action3])`;
  const sequentialStart = Date.now();
  for (const action of actions) {
    await action();
  }
  const sequentialTime = Date.now() - sequentialStart;
  log(`⏱️  Sequential time: ${sequentialTime}ms\n`);

  // Concurrent helper
  log`🚀 callClientActionsConcurrently([action1, action2, action3])`;
  const concurrentStart = Date.now();
  await Promise.all(actions.map((action) => action()));
  const concurrentTime = Date.now() - concurrentStart;
  log`⏱️  Concurrent time: ${concurrentTime}ms\n`;

  log`📊 Speed difference: ${Math.round(
    (sequentialTime / concurrentTime) * 100
  )}% faster with concurrent execution`;
}

// Demo 4: Real-world scenarios
export async function demoRealWorldScenarios() {
  log`\n=== DEMO 4: REAL-WORLD SCENARIOS ===`;

  // Scenario 1: Form submission (must be sequential)
  log`📝 Form Submission Workflow (Sequential):`;
  const formStart = Date.now();
  await simulateAsyncAction('Validate Form', 50);
  await simulateAsyncAction('Submit Data', 200);
  await simulateAsyncAction('Navigate to Success', 30);
  const formTime = Date.now() - formStart;
  log`⏱️  Form submission time: ${formTime}ms\n`;

  // Scenario 2: Dashboard loading (can be concurrent)
  log`📊 Dashboard Loading (Concurrent):`;
  const dashboardStart = Date.now();
  await Promise.all([
    simulateAsyncAction('Load User Data', 150),
    simulateAsyncAction('Load Notifications', 100),
    simulateAsyncAction('Load Recent Activity', 120),
    simulateAsyncAction('Load Weather Widget', 80),
  ]);
  const dashboardTime = Date.now() - dashboardStart;
  log`⏱️  Dashboard loading time: ${dashboardTime}ms\n`;

  // Scenario 3: Batch processing with limits
  log`🔄 Batch Processing (Controlled Concurrency):`;
  const batchStart = Date.now();
  const batchActions = Array.from(
    { length: 10 },
    (_, i) => () => simulateAsyncAction(`Batch Item ${i + 1}`, 30)
  );

  // Process in batches of 3
  const batchSize = 3;
  for (let i = 0; i < batchActions.length; i += batchSize) {
    const batch = batchActions.slice(i, i + batchSize);
    await Promise.all(batch.map((action) => action()));
  }
  const batchTime = Date.now() - batchStart;
  log`⏱️  Batch processing time: ${batchTime}ms (10 items, 3 at a time)\n`;
}

// Demo 5: Refactored Factory Function
export async function demoFactoryFunction() {
  log`\n=== DEMO 5: REFACTORED FACTORY FUNCTION ===`;

  // Simulate the callClientAction factory function
  const simulateCallClientAction = async (
    config: any | any[],
    async?: boolean,
    concurrencyLimit?: number
  ): Promise<void> => {
    if (Array.isArray(config)) {
      if (config.length === 0) return;

      // Case 1: Array with concurrency limit
      if (concurrencyLimit && concurrencyLimit > 0) {
        log(`🔄 Case 1: Array with concurrency limit (${concurrencyLimit})`);
        const batchSize = concurrencyLimit;
        for (let i = 0; i < config.length; i += batchSize) {
          const batch = config.slice(i, i + batchSize);
          await Promise.all(
            batch.map((action) =>
              simulateAsyncAction(action.name, action.delay)
            )
          );
        }
        return;
      }

      // Case 2: Array with async=true (sequential)
      if (async === true) {
        log`📋 Case 2: Array with async=true (sequential execution)`;
        for (const action of config) {
          await simulateAsyncAction(action.name, action.delay);
        }
        return;
      }

      // Case 3: Array with async=false or undefined (concurrent)
      log`🚀 Case 3: Array with async=false/undefined (concurrent execution)`;
      await Promise.all(
        config.map((action) => simulateAsyncAction(action.name, action.delay))
      );
      return;
    } else {
      // Single action cases
      if (config.async === true) {
        log`⚡ Case 4: Single action with explicit async property`;
        await simulateAsyncAction(config.name, config.delay);
      } else if (async === true) {
        log`🔄 Case 5: Single action with async parameter override`;
        await simulateAsyncAction(config.name, config.delay);
      } else {
        log`⚡ Case 6: Single action - synchronous execution`;
        log`🚀 Starting ${config.name} (sync)`;
        log`✅ Completed ${config.name} (sync)`;
      }
    }
  };

  const actions = [
    { name: 'Factory Action 1', delay: 50 },
    { name: 'Factory Action 2', delay: 75 },
    { name: 'Factory Action 3', delay: 60 },
  ];

  // Test Case 1: Concurrency limit
  log`\n--- Testing Case 1: Concurrency Limit ---`;
  const case1Start = Date.now();
  await simulateCallClientAction(actions, false, 2);
  log`⏱️  Time: ${Date.now() - case1Start}ms\n`;

  // Test Case 2: Sequential
  log`--- Testing Case 2: Sequential ---`;
  const case2Start = Date.now();
  await simulateCallClientAction(actions, true);
  log`⏱️  Time: ${Date.now() - case2Start}ms\n`;

  // Test Case 3: Concurrent
  log`--- Testing Case 3: Concurrent ---`;
  const case3Start = Date.now();
  await simulateCallClientAction(actions, false);
  log`⏱️  Time: ${Date.now() - case3Start}ms\n`;

  // Test Case 4: Single with async property
  log`--- Testing Case 4: Single with async property ---`;
  await simulateCallClientAction({
    name: 'Single Async',
    delay: 30,
    async: true,
  });

  // Test Case 5: Single with async parameter
  log`\n--- Testing Case 5: Single with async parameter ---`;
  await simulateCallClientAction(
    { name: 'Single Param Async', delay: 30 },
    true
  );

  // Test Case 6: Single synchronous
  log`\n--- Testing Case 6: Single synchronous ---`;
  simulateCallClientAction({ name: 'Single Sync', delay: 30 });
  log``;
}

// Demo 6: Error handling patterns
export async function demoErrorHandling() {
  log`\n=== DEMO 6: ERROR HANDLING PATTERNS ===`;

  const failingAction = (name: string): Promise<void> => {
    log`🚀 Starting ${name}`;
    return new Promise((_, reject) => {
      setTimeout(() => {
        log(`❌ Failed ${name}`);
        reject(new Error(`${name} failed`));
      }, 50);
    });
  };

  // Pattern 1: Fail fast (Promise.all)
  log`⚡ Fail Fast Pattern (Promise.all):`;
  try {
    await Promise.all([
      simulateAsyncAction('Success Action 1', 100),
      failingAction('Failing Action'),
      simulateAsyncAction('Success Action 2', 150),
    ]);
  } catch (error) {
    log`🛑 Execution stopped on first error\n`;
  }

  // Pattern 2: Continue on errors (Promise.allSettled)
  log`🔄 Continue on Errors Pattern (Promise.allSettled):`;
  const results = await Promise.allSettled([
    simulateAsyncAction('Success Action 1', 50),
    failingAction('Failing Action'),
    simulateAsyncAction('Success Action 2', 75),
  ]);

  const successful = results.filter((r) => r.status === 'fulfilled').length;
  const failed = results.filter((r) => r.status === 'rejected').length;
  log`📊 Results: ${successful} successful, ${failed} failed\n`;
}

// Demo 7: withAsync Function
export async function demoWithAsync() {
  log`\n=== DEMO 7: withAsync FUNCTION ===`;

  const actions = [
    () => simulateAsyncAction('WithAsync Action 1', 50),
    () => simulateAsyncAction('WithAsync Action 2', 75),
    () => simulateAsyncAction('WithAsync Action 3', 60),
  ];

  // Sequential mode (default)
  log`📋 withAsync - Sequential Mode (default):`;
  const sequentialStart = Date.now();
  for (const action of actions) {
    await action();
  }
  const sequentialTime = Date.now() - sequentialStart;
  log`⏱️  Sequential time: ${sequentialTime}ms\n`;

  // Concurrent mode
  log`🚀 withAsync - Concurrent Mode:`;
  const concurrentStart = Date.now();
  await Promise.all(actions.map((action) => action()));
  const concurrentTime = Date.now() - concurrentStart;
  log`⏱️  Concurrent time: ${concurrentTime}ms\n`;

  // Concurrent with limit
  log`🔄 withAsync - Concurrent with Limit (2):`;
  const limitedStart = Date.now();
  const batchSize = 2;
  for (let i = 0; i < actions.length; i += batchSize) {
    const batch = actions.slice(i, i + batchSize);
    await Promise.all(batch.map((action) => action()));
  }
  const limitedTime = Date.now() - limitedStart;
  log`⏱️  Limited concurrent time: ${limitedTime}ms\n`;

  log`📊 Performance comparison:`;
  log`- Sequential: ${sequentialTime}ms`;
  log`- Concurrent: ${concurrentTime}ms`;
  log`- Limited (2): ${limitedTime}ms`;
}

// Demo 8: createBatch Function
export async function demoCreateBatch() {
  log`\n=== DEMO 8: createBatch FUNCTION ===`;

  // Simulate the createBatch function
  const simulateCreateBatch = () => {
    const actions: Array<() => Promise<void>> = [];

    return {
      add: (action: () => Promise<void>) => {
        actions.push(action);
        log`➕ Added action to batch (total: ${actions.length})`;
      },
      execute: async (options?: {
        mode?: 'sequential' | 'concurrent';
        concurrencyLimit?: number;
      }) => {
        const { mode = 'sequential', concurrencyLimit } = options || {};
        log`🚀 Executing batch of ${actions.length} actions in ${mode} mode${
          concurrencyLimit ? ` with limit ${concurrencyLimit}` : ''
        }`;

        if (mode === 'concurrent') {
          if (concurrencyLimit && concurrencyLimit > 0) {
            const batchSize = concurrencyLimit;
            for (let i = 0; i < actions.length; i += batchSize) {
              const batch = actions.slice(i, i + batchSize);
              await Promise.all(batch.map((action) => action()));
            }
          } else {
            await Promise.all(actions.map((action) => action()));
          }
        } else {
          for (const action of actions) {
            await action();
          }
        }
      },
      size: () => actions.length,
      clear: () => {
        const oldSize = actions.length;
        actions.length = 0;
        log`🧹 Cleared batch (removed ${oldSize} actions)`;
      },
    };
  };

  const batch = simulateCreateBatch();

  // Add actions to batch
  log`📦 Creating and populating batch:`;
  batch.add(() => simulateAsyncAction('Batch Action 1', 30));
  batch.add(() => simulateAsyncAction('Batch Action 2', 40));
  batch.add(() => simulateAsyncAction('Batch Action 3', 35));
  batch.add(() => simulateAsyncAction('Batch Action 4', 25));

  log`📊 Batch size: ${batch.size()}\n`;

  // Execute sequentially
  log`--- Sequential Execution ---`;
  const seqStart = Date.now();
  await batch.execute({ mode: 'sequential' });
  log`⏱️  Sequential time: ${Date.now() - seqStart}ms\n`;

  // Clear and add new actions
  batch.clear();
  batch.add(() => simulateAsyncAction('New Batch Action 1', 20));
  batch.add(() => simulateAsyncAction('New Batch Action 2', 30));
  batch.add(() => simulateAsyncAction('New Batch Action 3', 25));

  // Execute concurrently
  log`--- Concurrent Execution ---`;
  const concStart = Date.now();
  await batch.execute({ mode: 'concurrent' });
  log`⏱️  Concurrent time: ${Date.now() - concStart}ms\n`;

  // Clear and add more actions for limited execution
  batch.clear();
  for (let i = 1; i <= 6; i++) {
    batch.add(() => simulateAsyncAction(`Limited Action ${i}`, 20));
  }

  // Execute with concurrency limit
  log`--- Limited Concurrent Execution (limit: 2) ---`;
  const limitStart = Date.now();
  await batch.execute({ mode: 'concurrent', concurrencyLimit: 2 });
  log`⏱️  Limited concurrent time: ${Date.now() - limitStart}ms\n`;
}

// Demo 9: Enhanced Error Handling and Validation
export async function demoEnhancedErrorHandling() {
  log`\n=== DEMO 9: ENHANCED ERROR HANDLING & VALIDATION ===`;

  // Simulate the enhanced callClientAction with validation
  const simulateEnhancedCallClientAction = (config: any): void => {
    // Handle null/undefined inputs gracefully
    if (config === null || config === undefined) {
      log`⚠️  Warning: Received null or undefined config, ignoring action`;
      return;
    }

    // Handle invalid config types
    if (typeof config !== 'object') {
      log`⚠️  Warning: Received invalid config type, expected object`;
      return;
    }

    // Handle missing action property
    if (!('action' in config)) {
      log`⚠️  Warning: Received config without action property, ignoring action`;
      return;
    }

    // Handle invalid action type
    if (typeof config.action !== 'string') {
      log`⚠️  Warning: Received config with invalid action type, expected string`;
      return;
    }

    // Handle unknown actions
    const knownActions = ['log', 'navigate', 'timeout', 'conditional'];
    if (!knownActions.includes(config.action)) {
      log`⚠️  Warning: Unknown action '${config.action}', no handler available`;
      return;
    }

    log`✅ Valid action '${config.action}' executed successfully`;
  };

  log`🧪 Testing enhanced validation:`;

  // Test 1: Null input
  log`\n--- Test 1: Null Input ---`;
  simulateEnhancedCallClientAction(null);

  // Test 2: Undefined input
  log`\n--- Test 2: Undefined Input ---`;
  simulateEnhancedCallClientAction(undefined);

  // Test 3: Invalid type (string)
  log`\n--- Test 3: Invalid Type (string) ---`;
  simulateEnhancedCallClientAction('invalid');

  // Test 4: Missing action property
  log`\n--- Test 4: Missing Action Property ---`;
  simulateEnhancedCallClientAction({ type: 'clientAction', payload: [] });

  // Test 5: Invalid action type (number)
  log`\n--- Test 5: Invalid Action Type (number) ---`;
  simulateEnhancedCallClientAction({
    type: 'clientAction',
    action: 123,
    payload: [],
  });

  // Test 6: Unknown action
  log`\n--- Test 6: Unknown Action ---`;
  simulateEnhancedCallClientAction({
    type: 'clientAction',
    action: 'unknownAction',
    payload: [],
  });

  // Test 7: Valid action
  log`\n--- Test 7: Valid Action ---`;
  simulateEnhancedCallClientAction({
    type: 'clientAction',
    action: 'log',
    payload: ['test'],
  });

  log`\n✅ Enhanced error handling ensures robust execution`;
}

// Demo 10: Extended ActionConfig Properties
export async function demoExtendedActionConfig() {
  log`\n=== DEMO 10: EXTENDED ACTION CONFIG PROPERTIES ===`;

  // Simulate extended action configs
  const basicAction = {
    type: 'clientAction',
    action: 'log',
    payload: ['Basic action'],
  };

  const asyncAction = {
    type: 'clientAction',
    action: 'log',
    payload: ['Async action'],
    async: true,
  };

  const debugAction = {
    type: 'clientAction',
    action: 'log',
    payload: ['Debug action'],
    debug: true,
  };

  const limitedAction = {
    type: 'clientAction',
    action: 'log',
    payload: ['Limited action'],
    concurrencyLimit: 2,
  };

  const fullExtendedAction = {
    type: 'clientAction',
    action: 'log',
    payload: ['Full extended action'],
    async: true,
    debug: true,
    concurrencyLimit: 3,
  };

  log`📋 Action Config Examples:`;
  log`\n1. Basic Action:`;
  log`   ${JSON.stringify(basicAction, null, 2)}`;

  log`\n2. Async Action (async: true):`;
  log`   ${JSON.stringify(asyncAction, null, 2)}`;

  log`\n3. Debug Action (debug: true):`;
  log`   ${JSON.stringify(debugAction, null, 2)}`;

  log`\n4. Limited Action (concurrencyLimit: 2):`;
  log`   ${JSON.stringify(limitedAction, null, 2)}`;

  log`\n5. Full Extended Action (all properties):`;
  log`   ${JSON.stringify(fullExtendedAction, null, 2)}`;

  log`\n🔧 Extended Properties:`;
  log`- async: boolean - Forces async execution when true`;
  log`- debug: boolean - Enables debug logging for the action`;
  log`- concurrencyLimit: number - Limits concurrent execution`;
}

// Run all demos
export async function runAllDemos() {
  log`🎭 useClientActions Async Execution Patterns Demo`;
  log`================================================`;

  await demoConcurrentExecution();
  await demoSequentialExecution();
  await demoHelperFunctions();
  await demoRealWorldScenarios();
  await demoFactoryFunction();
  await demoErrorHandling();
  await demoWithAsync();
  await demoCreateBatch();
  await demoEnhancedErrorHandling();
  await demoExtendedActionConfig();

  log`🎉 Demo completed! Key takeaways:`;
  log`1. Without await: Actions run concurrently (parallel)`;
  log`2. With await: Actions run sequentially (one after another)`;
  log`3. Use helper functions for complex scenarios`;
  log`4. The refactored factory function provides a unified interface`;
  log`5. Choose the right pattern based on your use case`;
  log`6. Handle errors appropriately for your workflow`;
  log`7. New withAsync function provides flexible execution modes`;
  log`8. createBatch allows collecting and executing actions together`;
  log`9. Enhanced error handling ensures robust execution`;
  log`10. Extended ActionConfig supports async, debug, and concurrency properties`;
}

// Uncomment to run the demo
// runAllDemos().catch(console.error);
