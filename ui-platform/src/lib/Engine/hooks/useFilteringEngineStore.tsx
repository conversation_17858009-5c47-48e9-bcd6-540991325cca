'use client';
import { create } from 'zustand';

// Define the type for filter functions
export type FilterFunction = {
    name: string;
    filterFn: (item: any) => boolean;
    getSearchResults?: () => any[];
};

export interface FilteringEngineState {
    filterFunctions: FilterFunction[];
    setFilterFunctions: (functions: FilterFunction[]) => void;
    addFilterFunction: (filterFunction: FilterFunction) => void;
    removeFilterFunction: (name: string) => void;
    clearFilterFunctions: () => void;
    getFilteredData: (data: any[]) => any[];
}

export const useFilteringEngineStore = create<FilteringEngineState>()((set, get) => ({
    filterFunctions: [],
    setFilterFunctions: (functions: FilterFunction[]) => set({ filterFunctions: functions }),
    addFilterFunction: (filterFn: FilterFunction) => set((state) => ({
        filterFunctions: [...state.filterFunctions, filterFn],
    })),
    removeFilterFunction: (name: string) => set((state) => {
        
        const newFilterFunctions = [...state.filterFunctions.filter((filter) => filter.name !== name)];
                
        return {
            filterFunctions: newFilterFunctions,
        };
    }),
    clearFilterFunctions: () => set({ filterFunctions: [] }),
    getFilteredData: (data: any[]) => {
        return data.filter((item) => {
            return get().filterFunctions.every((filterFn) => filterFn.filterFn(item));
        });
    },
    
}));

