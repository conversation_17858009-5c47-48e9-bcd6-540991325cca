import { useCallback, useRef, useState } from 'react';

export interface HoverItem {
  key: string;
  data?: any;
}

export interface UseHoverInteractionProps {
  onHoverStart?: (item?: HoverItem) => void;
  onHoverEnd?: (item?: HoverItem) => void;
  onTouchStart?: (item?: HoverItem) => void;
  onTouchEnd?: (item?: HoverItem) => void;
}

export const useHoverInteraction = ({
  onHoverStart,
  onHoverEnd,
  onTouchStart,
  onTouchEnd,
}: UseHoverInteractionProps = {}) => {
  const [hoveredItem, setHoveredItem] = useState<HoverItem | null>(null);
  const [touchActive, setTouchActive] = useState(false);
  const touchTimeoutRef = useRef<NodeJS.Timeout>();

  const callbacksRef = useRef({
    onHoverStart,
    onHoverEnd,
    onTouchStart,
    onTouchEnd,
  });

  callbacksRef.current = {
    onHoverStart,
    onHoverEnd,
    onTouchStart,
    onTouchEnd,
  };

  const handleMouseEnter = useCallback(
    (item: HoverItem) => {
      if (touchActive) return;

      setHoveredItem(item);
      callbacksRef.current.onHoverStart?.(item);
    },
    [touchActive]
  );

  const handleMouseLeave = useCallback(() => {
    if (touchActive) return;

    setHoveredItem((prevItem: HoverItem | null) => {
      if (prevItem) {
        callbacksRef.current.onTouchEnd?.(prevItem);
        return null;
      }
      callbacksRef.current.onHoverEnd?.();
      return null;
    });
  }, [touchActive]);

  const handleTouchStart = useCallback((item: HoverItem) => {
    setTouchActive(true);
    setHoveredItem(item);
    callbacksRef.current.onTouchStart?.(item);

    // Clear touch active state after delay
    if (touchTimeoutRef.current) {
      clearTimeout(touchTimeoutRef.current);
    }
    touchTimeoutRef.current = setTimeout(() => {
      setTouchActive(false);
    }, 300);
  }, []);

  const handleTouchEnd = useCallback(() => {
    setHoveredItem((prevItem) => {
      if (prevItem) {
        callbacksRef.current.onTouchEnd?.(prevItem);
        return null;
      }
      callbacksRef.current.onTouchEnd?.();
      return null;
    });
  }, []);

  const clearHover = useCallback(() => {
    setHoveredItem(null);
    setTouchActive(false);
  }, []);

  const isItemHovered = useCallback(
    (key: string) => {
      return hoveredItem?.key === key;
    },
    [hoveredItem?.key]
  );

  return {
    handleMouseEnter,
    handleMouseLeave,
    handleTouchStart,
    handleTouchEnd,
    hoveredItem,
    clearHover,
    isItemHovered,
    touchActive,
  } as const;
};
