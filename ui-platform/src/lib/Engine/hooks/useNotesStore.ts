import { create } from 'zustand';
import { persist } from 'zustand/middleware';


export interface Note {
  id: number;
  author_name: string;
  author_contact: string;
  author_company: string;
  author_roles: { id: number; description: string; }[];
  timestamp: string;
  message: string;
  application?: number;
  state?: number;
  author?: number;
  staffmembers?: any[];
  target?: any;
  source_note?: any;
  job?: number;
  priority?: number;
  customer?: any;
  safe_name?: string;
  purpose?: any;
  customer_name?: string;
}

interface NotesState {
  notes: Note[];
  isLoading: boolean;
  error: string | null;
  // fetchNotes: (jobId: number, baseUrl: string) => Promise<void>;
  addNote: (message: string, jobId: number, token: string, addNoteUrl: string) => Promise<void>;
  setNotes: (response: { success: boolean; payload: Note[] }) => void;
}

export const useNotesStore = create<NotesState>()(
  persist(
    (set, get) => ({
      notes: [],
      isLoading: false,
      error: null,


      addNote: async (message: string, jobId: number, token?: string | undefined, addNoteUrl?: string | undefined) => {
        set({ isLoading: true, error: null });

        // console.log('addNote in useNotesStore', message, jobId, addNoteUrl, token);

        if(!token || !addNoteUrl) {
          set({ error: 'Authentication token is required', isLoading: false });
          return;
        }
        
        // Optimistically add the note to the UI
        const tempNote = {
          message: message,
          author_name: '',
          author_contact: '',
          author_company: '',
          author_roles: [],
          timestamp: new Date().toISOString(),
          id: Date.now(), // temporary ID
        } as Note;
        // console.log('tempNote', tempNote);
        set(state => ({ notes: [...state.notes, tempNote] }));

        try {
          const response = await fetch(`${addNoteUrl}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Token ${token}`,
            },
            body: JSON.stringify({ message, job_id: jobId }),
          });

          if (!response.ok) throw new Error('Failed to add note on the job');
          
          const data = await response.json();
          // Update the note with the real server data
          console.log('NOTE PAYLOAD', data.payload);
          
          // Ensure we have valid data before updating
          if (!data.success || !data.payload) {
            throw new Error('Invalid server response format');
          }

          // Ensure all required fields are present
          const validNote = {
            ...data.payload,
            author_name: data.payload.author_name || '',
            author_contact: data.payload.author_contact || '',
            author_company: data.payload.author_company || '',
            author_roles: Array.isArray(data.payload.author_roles) ? data.payload.author_roles : [],
            timestamp: data.payload.timestamp || new Date().toISOString(),
            message: data.payload.message || message,
          };

          set(state => ({
            notes: state.notes.map(n => 
              n.id === tempNote.id ? validNote : n
            )
          }));
          
        } catch (error) {
          // Revert the optimistic update
          set(state => ({
            notes: state.notes.filter(n => n.id !== tempNote.id),
            error: error instanceof Error ? error.message : 'Failed to add note: ERROR'
          }));
        } finally {
          set({ isLoading: false });
        }
      },

      setNotes: (response) => {
        if (!response.success || !Array.isArray(response.payload)) {
          set({ error: 'Failed to fetch notes', isLoading: false });
          return;
        }
        
        // Ensure all notes have required fields
        const validNotes = response.payload.map(note => ({
          ...note,
          author_name: note.author_name || '',
          author_contact: note.author_contact || '',
          author_company: note.author_company || '',
          author_roles: Array.isArray(note.author_roles) ? note.author_roles : [],
          timestamp: note.timestamp || new Date().toISOString(),
          message: note.message || '',
        }));

        set({ notes: validNotes, isLoading: false, error: null });
      },
    }),
    {
      name: 'notes-store',
    }
  )
);