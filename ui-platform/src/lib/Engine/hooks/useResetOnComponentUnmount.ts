import { useEffect, useMemo } from 'react';
import { useResetStore } from '../useAppStore';

export const useResetOnComponentUnmount = (
  exemptKeys: string[],
  options: {
    enableCleanup?: boolean;
    softReset?: boolean;
    referenceDep?: any;
  } = {
    enableCleanup: false,
    softReset: true,
    referenceDep: '',
  }
) => {
  const resetStore = useResetStore();
  const deps = useMemo(() => {
    if (options.referenceDep) return [options.referenceDep];
    return [];
  }, [options.referenceDep]);

  useEffect(() => {
    return () => {
      if (!options.enableCleanup || !options.referenceDep) {
        return;
      }
      resetStore(exemptKeys, { softReset: options.softReset });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [...deps]);
};
