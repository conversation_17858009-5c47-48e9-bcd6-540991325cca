import { MappedJob } from "./mapped-job";

export interface MappedClaim {
    id: number;
    applicant?: {
      first_name?: string | null;
      surname?: string | null;
      id_number?: string | null;
      claimantpoliceynum?: string | null;
      bondnumber?: string | null;
      generated?: string | null;
      local_file?: string | null;
      created_by?: string | null;
    } | null;

     application_creator?: {
      id?: number;
      full_name?: string | null;
      roles?: number[] | null;
     } | null;
     application_date?: string | null;
     state?: number | null;
     state_change_date?: string | null;
     location?: number | null;
     modified_date?: string | null;
     mid?: string | null;
     sub_section?: number | null;
     jobs?: MappedJob[] | null;     
     note_count?: number | null;
     repudiation?: any | null;
     deceased?: any[] | null;
     form_start?: string | null;
     assessor?: any | null;
     cat_code?: any | null;
     claim_type_id?: number | null;
     private_banking?: number | null;
     unread_note_count?: number | null;
     source?: string | null;
     source_id?: number | null;
     source_key?: string | null;

     // CUSTOM FRONTEND PROPS
     stateTextDisplay?: string | null;
     customer?: string | null;
     permissionGranted?: boolean | null;
     formattedDate?: string | null;
     filters: {
        states: number[];
        value: number;
     }
  }