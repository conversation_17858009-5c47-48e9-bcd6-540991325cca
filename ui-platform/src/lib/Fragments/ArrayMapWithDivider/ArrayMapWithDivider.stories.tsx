import { <PERSON>a, StoryObj } from '@storybook/react';
import { ArrayMapWithDivider } from './ArrayMapWithDivider';

const meta: Meta<typeof ArrayMapWithDivider> = {
  component: ArrayMapWithDivider,
  title: 'Fragments/ArrayMapWithDivider',
};

export default meta;

type Story = StoryObj<typeof ArrayMapWithDivider>;

export const Overview: Story = {
  args: {
    events: [
      {
        timestamp: '2025-06-09T16:06:03.554609+02:00',
        event_source: 'Claim Connect: 1',
        event_initiator: 'update_client_subscription: monnaFO',
      },
      {
        timestamp: '2025-06-09T16:05:46.520142+02:00',
        event_source:
          'Claim Connect: 4 due to Suspected Fraud: optional: String maxing 255 characters.',
        event_initiator: 'update_client_subscription: monnaFO',
      },
      {
        timestamp: '2025-06-09T16:04:19.397824+02:00',
        event_source:
          'Claim Connect: 4 due to Cleared of wrongdoing: optional: String maxing 255 characters.',
        event_initiator: 'update_client_subscription: monnaFO',
      },
      {
        timestamp: '2025-06-09T16:00:41.272056+02:00',
        event_source: 'Claim Connect: 4 due to No reason provided: ',
        event_initiator: 'update_client_subscription: monnaFO',
      },
    ],
  },
};
