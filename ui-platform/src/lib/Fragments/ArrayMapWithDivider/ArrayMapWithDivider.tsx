import React from 'react';
import styled from 'styled-components';
import { Divider } from '../../Components/Dividers/Divider';
import { KeyValueList } from '../../Fragments/KeyValueList/KeyValueList';

// Updated Props type for API response
export type EventItem = {
  timestamp: string;
  event_source: string;
  event_initiator: string;
};

type Props = {
  events: EventItem[];
};

const Container = styled.div`
  display: grid;
  grid-auto-flow: row;
  align-content: center;
  justify-items: center;
`;

const Text = styled.div`
  text-align: center;
  font-size: ${(props) => props.theme.FontSize5}px;
  font-weight: 200;
  font-family: Inter;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  word-wrap: break-word;
`;

const ListDivider = styled(Divider)`
  background: ${(props) => props.theme.ColorsStrokesDefault};
  margin: 2rem;
`;

/**
 * Renders a list of event items with dividers.
 *
 * @param {Props} props - The component props.
 * @param {EventItem[]} props.events - The events to be displayed.
 * @return {JSX.Element} The rendered event list component.
 */
export const ArrayMapWithDivider = ({ events }: Props) => {
  return (
    <Container>
      {events.map((event, idx) => (
        <React.Fragment
          key={event.timestamp + event.event_source + event.event_initiator}
        >
          <KeyValueList
            numbering={false}
            heading={''}
            data={event}
            width={'70%'}
            itemMargin={'10px'}
            align={'left'}
            colouredHeading={{
              headingString: 'Change details:',
              headingColour: 'alternative',
            }}
            colour={'default'}
            size={'medium'}
            textTransform={'default'}
          />
          {idx < events.length - 1 && (
            <ListDivider
              size="fullWidth"
              background="primary"
              type="dotted"
              state="grey"
              height="mid"
            />
          )}
        </React.Fragment>
      ))}
    </Container>
  );
};
