import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { ThemeProvider } from 'styled-components';
import { desktopDark } from '../../themes';
import { DynamicActivationButtons } from './DynamicActivationButtons';

// Mock theme (adjust according to your actual theme)
const mockTheme = {
  SpacingSm: '8px',
  SpacingXs: '4px',
  GapSm: '8px',
  ColorsTypographyPrimary: '#ffffff',
  ColorsTypographyInverse: '#000000',
  ColorsUtilityColorFocus: '#007bff',
  FontSize3: 14,
  FontFamiliesInter: 'Inter, sans-serif',
  FontWeightsInter3: 500,
};

// Mock activation states
const mockActivationStates = [
  { id: 1, name: 'DISABLED', displayName: 'Disabled' },
  { id: 2, name: 'ENABLED', displayName: 'Enabled' },
  { id: 3, name: 'EVALUATING', displayName: 'Evaluating' },
  { id: 4, name: 'PENDING', displayName: 'Pending' },
] as const;

// Mock service items (using the new flexible structure)
const mockServiceItems = [
  {
    name: 'analytics_service',
    label: 'Analytics Service',
    tenant_id: 'tenant-001',
    client_id: 'analytics-client',
    service_type: 'analytics',
    priority: 'high',
    control: undefined,
    rules: { required: 'Please select a status' },
  },
  {
    name: 'notification_service',
    label: 'Notification Service',
    tenant_id: 'tenant-002',
    client_id: 'notification-client',
    service_type: 'communication',
    priority: 'medium',
    control: undefined,
    rules: { required: 'Please select a status' },
  },
  {
    name: 'reporting_service',
    label: 'Reporting Service',
    tenant_id: 'tenant-003',
    client_id: 'reporting-client',
    service_type: 'analytics',
    priority: 'medium',
    control: undefined,
    rules: { required: 'Please select a status' },
  },
  {
    name: 'integration_service',
    label: 'Integration Service',
    tenant_id: 'tenant-004',
    client_id: 'integration-client',
    service_type: 'integration',
    priority: 'low',
    control: undefined,
    rules: { required: 'Please select a status' },
  },
];

// Mock store data
const mockStore = {
  id: 'company-123',
  sp_profile: {
    details: {
      id: 'sp-profile-456',
    },
    companies: [
      { client_id: 'analytics-client', active: 2 }, // ENABLED
      { client_id: 'notification-client', active: 1 }, // DISABLED
      { client_id: 'reporting-client', active: 3 }, // EVALUATING
      { client_id: 'integration-client', active: 4 }, // PENDING
    ],
  },
  sp_enums: {
    client_sub_active_states: mockActivationStates,
  },
};

// Form wrapper component for stories
const FormWrapper: React.FC<{
  children: React.ReactNode;
  onSubmit: (data: any) => void;
  defaultValues?: Record<string, any>;
}> = ({ children, onSubmit, defaultValues = {} }) => {
  const methods = useForm({
    defaultValues: {
      analytics_service: 'ENABLED',
      notification_service: 'DISABLED',
      reporting_service: 'EVALUATING',
      integration_service: 'PENDING',
      tenant_subscriptions_evaluated: false,
      ...defaultValues,
    },
  });

  return (
    <ThemeProvider theme={desktopDark as any}>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <div style={{ maxWidth: '600px', margin: '0 auto', padding: '20px' }}>
            {children}

            {/* Form state display */}
            <div
              style={{
                marginTop: '30px',
                padding: '20px',
                backgroundColor: '#f5f5f5',
                borderRadius: '8px',
                border: '1px solid #ddd',
                color: '#333',
              }}
            >
              <h3 style={{ margin: '0 0 15px 0', fontSize: '18px' }}>
                Current Form State:
              </h3>
              <pre
                style={{
                  margin: 0,
                  fontSize: '12px',
                  whiteSpace: 'pre-wrap',
                  backgroundColor: '#fff',
                  padding: '10px',
                  borderRadius: '4px',
                  border: '1px solid #ccc',
                }}
              >
                {JSON.stringify(methods.watch(), null, 2)}
              </pre>
            </div>

            {/* Form errors display */}
            {Object.keys(methods.formState.errors).length > 0 && (
              <div
                style={{
                  marginTop: '15px',
                  padding: '15px',
                  backgroundColor: '#f8d7da',
                  borderRadius: '8px',
                  border: '1px solid #f5c6cb',
                  color: '#721c24',
                }}
              >
                <h4 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>
                  Form Errors:
                </h4>
                <pre
                  style={{
                    margin: 0,
                    fontSize: '12px',
                    whiteSpace: 'pre-wrap',
                  }}
                >
                  {JSON.stringify(methods.formState.errors, null, 2)}
                </pre>
              </div>
            )}

            {/* Submit button */}
            <div style={{ marginTop: '20px', textAlign: 'center' }}>
              <button
                type="submit"
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '14px',
                }}
              >
                Submit Form
              </button>
            </div>
          </div>
        </form>
      </FormProvider>
    </ThemeProvider>
  );
};

const meta: Meta<typeof DynamicActivationButtons> = {
  title: 'Components/DynamicActivationButtons',
  component: DynamicActivationButtons,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
# DynamicActivationButtons (Enhanced)

A highly flexible component for rendering multiple activation toggle buttons with form integration.

## Key Improvements
- **Flexible Item Structure**: Works with any item structure using \`itemLabelProp\` and \`itemValueProp\`
- **Simplified Props**: Cleaner API with more intuitive property names
- **Custom Submit Handler**: Optional custom submit function for handling state changes
- **Enhanced Type Safety**: Better generic typing for maximum flexibility

## Features
- **Generic Implementation**: Works with any object structure
- **Form Integration**: Built-in React Hook Form support with validation
- **Async Operations**: Supports async state changes and API calls
- **Custom Handlers**: Flexible onChange and submit handlers
- **State Management**: Automatic form state synchronization

## Props
- \`title\`: Display title for the component group
- \`items\`: Array of items to render (must have name and label properties)
- \`itemLabelProp\`: Property name to use for item labels
- \`itemValueProp\`: Property name to use for item values
- \`states\`: Array of possible states for each toggle
- \`valueProp\`: Property name to use for state values
- \`nameProp\`: Property name to use for state display names
- \`onChange\`: Callback for state changes
- \`submit\`: Custom submit function
- \`submitOnChange\`: Auto-submit on state change
- \`store\`: Data store with current states and configurations
        `,
      },
    },
  },
  argTypes: {
    title: {
      control: 'text',
      description: 'Title displayed above the activation buttons',
    },
    divider: {
      control: 'boolean',
      description: 'Show dividers between buttons',
    },
    submitOnChange: {
      control: 'boolean',
      description: 'Automatically submit when state changes',
    },
    onChange: {
      action: 'changed',
      description: 'Callback fired when any item state changes',
    },
    submit: {
      action: 'submitted',
      description: 'Custom submit handler for state changes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof DynamicActivationButtons>;

// Basic story with the new flexible structure
export const Default: Story = {
  render: (args) => (
    <FormWrapper onSubmit={(data) => console.log('Form submitted:', data)}>
      <DynamicActivationButtons
        {...args}
        title="Service Management Dashboard"
        items={mockServiceItems}
        itemLabelProp="label"
        itemValueProp="service_type"
        states={mockActivationStates}
        valueProp="name"
        nameProp="displayName"
        store={mockStore}
        isStory={true}
        divider={true}
      />
    </FormWrapper>
  ),
  args: {
    onChange: (state) => console.log('State changed:', state),
    submit: (data) => console.log('Custom submit called:', data),
  },
};

// Without dividers
export const WithoutDividers: Story = {
  render: (args) => (
    <FormWrapper onSubmit={(data) => console.log('Form submitted:', data)}>
      <DynamicActivationButtons
        {...args}
        title="Compact Service Settings"
        items={mockServiceItems}
        itemLabelProp="label"
        itemValueProp="priority"
        states={mockActivationStates}
        valueProp="name"
        nameProp="displayName"
        store={mockStore}
        isStory={true}
        divider={false}
      />
    </FormWrapper>
  ),
  args: {
    onChange: (state) => console.log('State changed:', state),
    submit: (data) => console.log('Custom submit called:', data),
  },
};

// Submit on change with custom submit handler
export const CustomSubmitHandler: Story = {
  render: (args) => (
    <FormWrapper onSubmit={(data) => console.log('Form submitted:', data)}>
      <DynamicActivationButtons
        {...args}
        title="Auto-Submit with Custom Handler"
        items={mockServiceItems}
        itemLabelProp="label"
        itemValueProp="service_type"
        states={mockActivationStates}
        valueProp="name"
        nameProp="displayName"
        store={mockStore}
        isStory={true}
        submitOnChange={true}
        divider={true}
        submit={(data) => {
          console.log('🚀 Custom submit handler called with:', data);
          // Simulate API call
          return new Promise((resolve) => {
            setTimeout(() => {
              console.log('✅ Custom submit completed');
              resolve(data);
            }, 1000);
          });
        }}
      />
      <div
        style={{
          marginTop: '20px',
          padding: '15px',
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: '4px',
          fontSize: '14px',
        }}
      >
        <strong>Custom Submit Handler:</strong> Changes trigger a custom submit
        function with simulated async processing. Check the browser console for
        detailed logs.
      </div>
    </FormWrapper>
  ),
  args: {
    onChange: (state) => console.log('State changed (custom submit):', state),
  },
};

// Different item properties example
const userPreferenceItems = [
  {
    name: 'email_notifications',
    label: 'Email Notifications',
    description: 'Receive notifications via email',
    category: 'communication',
    defaultValue: 'enabled',
    control: undefined,
  },
  {
    name: 'push_notifications',
    label: 'Push Notifications',
    description: 'Receive push notifications on mobile',
    category: 'communication',
    defaultValue: 'disabled',
    control: undefined,
  },
  {
    name: 'data_analytics',
    label: 'Data Analytics',
    description: 'Allow analytics data collection',
    category: 'privacy',
    defaultValue: 'enabled',
    control: undefined,
  },
  {
    name: 'marketing_emails',
    label: 'Marketing Communications',
    description: 'Receive marketing and promotional emails',
    category: 'marketing',
    defaultValue: 'disabled',
    control: undefined,
  },
];

const binaryStates = [
  { status: 'disabled', title: 'Disabled', active: false },
  { status: 'enabled', title: 'Enabled', active: true },
] as const;

export const UserPreferences: Story = {
  render: (args) => (
    <FormWrapper
      onSubmit={(data) => console.log('User preferences submitted:', data)}
      defaultValues={{
        email_notifications: 'enabled',
        push_notifications: 'disabled',
        data_analytics: 'enabled',
        marketing_emails: 'disabled',
      }}
    >
      <DynamicActivationButtons
        {...args}
        title="User Preference Settings"
        items={userPreferenceItems}
        itemLabelProp="label"
        itemValueProp="category"
        states={binaryStates}
        valueProp="status"
        nameProp="title"
        store={mockStore}
        isStory={true}
        divider={true}
        submit={(data) => {
          console.log('💾 Saving user preference:', data);
          // Simulate preference save
          return Promise.resolve(data);
        }}
      />
      <div
        style={{
          marginTop: '20px',
          padding: '15px',
          backgroundColor: '#d4edda',
          border: '1px solid #c3e6cb',
          borderRadius: '4px',
          fontSize: '14px',
          color: '#333',
        }}
      >
        <strong>Different Data Structure:</strong> This example uses different
        property names (status/title for states, label/category for items) to
        show the flexibility of the component.
      </div>
    </FormWrapper>
  ),
  args: {
    onChange: (state) => console.log('User preference changed:', state),
  },
};

// Complex states with multiple properties
const complexStates = [
  {
    level: 0,
    label: 'Off',
    description: 'Service is disabled',
    color: '#dc3545',
    billing: 'free',
  },
  {
    level: 1,
    label: 'Basic',
    description: 'Basic features enabled',
    color: '#28a745',
    billing: 'basic',
  },
  {
    level: 2,
    label: 'Premium',
    description: 'All features enabled',
    color: '#007bff',
    billing: 'premium',
  },
] as const;

const subscriptionItems = [
  {
    name: 'storage_service',
    label: 'Cloud Storage',
    description: 'File storage and backup service',
    tier: 'basic',
    monthly_cost: 9.99,
    control: undefined,
  },
  {
    name: 'compute_service',
    label: 'Compute Engine',
    description: 'Virtual machine instances',
    tier: 'premium',
    monthly_cost: 29.99,
    control: undefined,
  },
  {
    name: 'database_service',
    label: 'Managed Database',
    description: 'Fully managed database service',
    tier: 'basic',
    monthly_cost: 19.99,
    control: undefined,
  },
];

export const SubscriptionManagement: Story = {
  render: (args) => {
    const defaultValues = subscriptionItems.reduce(
      (acc, item) => ({
        ...acc,
        [item.name]:
          item.tier === 'premium'
            ? 'Premium'
            : item.tier === 'basic'
            ? 'Basic'
            : 'Off',
      }),
      {}
    );

    return (
      <FormWrapper
        onSubmit={(data) =>
          console.log('Subscription changes submitted:', data)
        }
        defaultValues={defaultValues}
      >
        <DynamicActivationButtons
          {...args}
          title="Subscription Management"
          items={subscriptionItems}
          itemLabelProp="label"
          itemValueProp="tier"
          states={complexStates}
          valueProp="label"
          nameProp="label"
          store={mockStore}
          isStory={true}
          divider={true}
          submitOnChange={true}
          submit={(data) => {
            console.log('💳 Processing subscription change:', data);
            // Calculate billing impact
            const selectedState = complexStates.find(
              (s) => s.label === data.label
            );
            console.log('💰 Billing tier:', selectedState?.billing);
            return Promise.resolve(data);
          }}
        />
        <div
          style={{
            marginTop: '20px',
            padding: '15px',
            backgroundColor: '#e7f3ff',
            border: '1px solid #b8daff',
            borderRadius: '4px',
            fontSize: '14px',
          }}
        >
          <strong>Complex States Example:</strong> States contain multiple
          properties (level, description, color, billing) showing how the
          component can work with rich state objects.
        </div>
      </FormWrapper>
    );
  },
  args: {
    onChange: (state) => console.log('Subscription state changed:', state),
  },
};

// Large dataset performance test
const generateMockItems = (count: number) => {
  const categories = [
    'analytics',
    'communication',
    'storage',
    'compute',
    'security',
  ];
  const priorities = ['low', 'medium', 'high', 'critical'];

  return Array.from({ length: count }, (_, i) => ({
    name: `service_${i + 1}`,
    label: `Service ${i + 1}`,
    description: `Description for service ${i + 1}`,
    category: categories[i % categories.length],
    priority: priorities[i % priorities.length],
    tenant_id: `tenant-${String(i + 1).padStart(3, '0')}`,
    client_id: `service-client-${i + 1}`,
    control: undefined,
    rules: { required: 'Please select a status' },
  }));
};

export const LargeDataset: Story = {
  render: (args) => {
    const largeItemList = generateMockItems(15);
    const defaultValues = largeItemList.reduce(
      (acc, item, index) => ({
        ...acc,
        [item.name]:
          mockActivationStates[index % mockActivationStates.length].name,
      }),
      {}
    );

    return (
      <FormWrapper
        onSubmit={(data) => console.log('Large dataset submitted:', data)}
        defaultValues={defaultValues}
      >
        <DynamicActivationButtons
          {...args}
          title="Enterprise Service Portfolio"
          items={largeItemList}
          itemLabelProp="label"
          itemValueProp="category"
          states={mockActivationStates}
          valueProp="name"
          nameProp="displayName"
          store={{
            ...mockStore,
            sp_profile: {
              ...mockStore.sp_profile,
              companies: largeItemList.map((item, index) => ({
                client_id: item.client_id,
                active:
                  mockActivationStates[index % mockActivationStates.length].id,
              })),
            },
          }}
          isStory={true}
          divider={true}
          submit={(data) => {
            console.log('⚡ Bulk operation:', data);
            return Promise.resolve(data);
          }}
        />
        <div
          style={{
            marginTop: '20px',
            padding: '15px',
            backgroundColor: '#e2e3e5',
            border: '1px solid #d6d8db',
            borderRadius: '4px',
            fontSize: '14px',
          }}
        >
          <strong>Performance Test:</strong> {largeItemList.length} services
          rendered with full form integration. Notice how the component
          maintains performance even with larger datasets.
        </div>
      </FormWrapper>
    );
  },
  args: {
    onChange: (state) => console.log('Large dataset state changed:', state),
    submit: (data) => console.log('Large dataset item submitted:', data),
  },
};
