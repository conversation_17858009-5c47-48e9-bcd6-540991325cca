import type { Meta, StoryObj } from '@storybook/react';
import { InformationCardAction } from './InformationCardAction';

const meta: Meta<typeof InformationCardAction> = {
  component: InformationCardAction,
  title: 'Fragments/InformationCardAction',
};
export default meta;
type Story = StoryObj<typeof InformationCardAction>;

export const InformationCard: Story = {
  args: {
    information: ['YOU PRE-QUALIFY FOR JOB FINANCING.', 'GROW YOUR BUSINESS WITHOUT THE CASH FLOW STRAIN'],
    icon: "hand-change",
    type: "info",
    button: {
      btnValue: 'Find out more', onClick: {
        type: 'clientAction',
        action: 'navigate',
        payload: ['/'],
      },
    },
  },
};
