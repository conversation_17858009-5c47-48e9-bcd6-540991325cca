import { ReactNode } from "react";
import styled, { CSSProperties } from "styled-components";
import { Icon, IconTypes, TextButton } from "../../Components";
import { ActionConfig } from "../../Engine";
import { InformationCard } from "../../Components/Cards/InformationCard/InformationCard";

type ContainerProps = 'error' | 'warning' | 'info' | 'default';

type ButtonProps = {
  btnType?: 'button' | 'submit' | 'reset';
  btnValue: ReactNode;
  actiontype?:
  | 'preferred'
  | 'alternative'
  | 'proceed'
  | 'warning'
  | 'default'
  | 'attention';
  size?: 'small' | 'large';
  onClick: ActionConfig;
  className?: string;
  style?: CSSProperties;
}

type InformationCardActionProps = {
  type?: ContainerProps;
  icon?: IconTypes;
  information: string[];
  button?: ButtonProps;
  _callClientAction?: (config: ActionConfig) => void;
}

const InformationCardContainer = styled(InformationCard) <{ type?: ContainerProps; }>`
display: grid;
justify-content: center;
justify-items: center;
`;

const CardIcon = styled(Icon)`
  margin-bottom: 1rem;
`;

const InformationContainer = styled.div`
 text-align: center;
`;

const CardButton = styled(TextButton)`
  margin-top: 2rem;
`;

/**
 * InformationCardAction component is a combination of InformationCard and a button with an optional Icon. The button can be used to trigger an action
 * when clicked. The component can be used to show information to the user and to provide an action to do.
 *
 * @param {InformationCardActionProps} props
 * @prop {ContainerProps} type - The type of the information card. It can be error, warning, default or info.
 * @prop {IconTypes} icon - The icon to display in the card. If not provided, it will be hidden.
 * @prop {string[]} information - The information to display in the card. It can be a string or an array of strings.
 * @prop {ButtonProps} button - The button properties. It can be a string or an object with properties such as btnType, btnValue, actiontype, size, onClick and className.
 * @prop {Function} _callClientAction - The function to call when the button is clicked. It will be called with the onClick property of the button.
 * @returns {ReactElement} The InformationCardAction component.
 *
 * @example
 * <InformationCardAction
 *  type="info"
 *  icon="warning"
 *  information=["You have an error"]
 *  button={
 *    btnValue: 'Find out more', onClick: {
 *      type: 'clientAction',
 *      action: 'navigate',
 *      payload: ['/'],
 *    },
 *  },
 * />
 */
export function InformationCardAction({
  type,
  icon,
  information = [],
  button,
  _callClientAction
}: InformationCardActionProps) {

  /**
   * Handles the button click event.
   * Triggers the client action associated with the button if available.
   *
   * @param {React.MouseEvent<HTMLButtonElement>} e - The button click event.
   */
  const onBtnClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    // Check if the client action function and button are provided
    if (_callClientAction && button) _callClientAction(button.onClick);
  }

  return (
    <InformationCardContainer type={type}>
      {icon && <CardIcon type={icon} size={52} />}
      <InformationContainer>
        {information.map((item, index) => (
          <>
            <span key={index}>{item}</span><br />
          </>
        ))}
      </InformationContainer>
      {button && <CardButton {...button} onClick={onBtnClick} style={{ marginTop: '1rem' }}></CardButton>}
    </InformationCardContainer>
  );
}
