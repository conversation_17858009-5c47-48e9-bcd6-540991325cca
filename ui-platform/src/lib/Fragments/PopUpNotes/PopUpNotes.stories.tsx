import type { Meta, StoryObj } from '@storybook/react';
import React, { useState } from 'react';
import { PopUpNotes } from './PopUpNotes';
import { TextButton } from '../../Components/Buttons/TextButton/TextButton';
import { ThemeProvider } from 'styled-components';

const meta: Meta<typeof PopUpNotes> = {
  component: PopUpNotes,
  title: 'Fragments/PopUpNotes',
  tags: ['autodocs'],
  decorators: [
    (Story) => (
        <Story />
    ),
  ],
  argTypes: {
    onSubmit: { action: 'submitted' },
    onClose: { action: 'closed' },
  },
};

export default meta;
type Story = StoryObj<typeof PopUpNotes>;

export const Default: Story = {
  args: {
    open: true,
  },
};

export const WithButton: Story = {
    render: () => {
        const [isOpen, setIsOpen] = useState(false);

        const handleSubmit = (note: string) => {
            console.log('submitted', note);
            setIsOpen(false);
        };

        const handleClose = () => {
            console.log('closed');
            setIsOpen(false);
        }

        return (
            <div>
                <TextButton btnValue="Open Pop-up" onClick={() => setIsOpen(true)} />
                <PopUpNotes open={isOpen} onClose={handleClose} onSubmit={handleSubmit} />
            </div>
        )
    },
    args: {
        open: false,
    }
} 