import React, { useState } from 'react';
import styled from 'styled-components';
import InvoiceSummaryNotes from '../../Components/BOQ/InvoiceSummaryNotes/InvoiceSummaryNotes';
import { useSetDynamicState } from '../../Engine/useAppStore';

const PopupOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
`;

const PopupContainer = styled.div<{ isMinimized: boolean }>`
  background: #222;
  color: #fff;
  padding: 32px;
  width: 80%;
  height: ${(props) => (props.isMinimized ? 'auto' : '25vh')};
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  box-shadow: 0 -2px 16px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  transition: height 0.3s ease-in-out;
`;

const PopupHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const HeaderTitle = styled.h2`
  margin: 0;
`;

const ControlButton = styled.button`
  background: none;
  border: none;
  color: #fff;
  cursor: pointer;
`;

const MinimizeButton = styled(ControlButton)`
  font-size: 24px;
  margin-right: 10px;
`;

const CloseButton = styled(ControlButton)`
  font-size: 20px;
`;

const PopupContent = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-top: 20px;
`;

const NotesWrapper = styled.div`
  flex: 1;
`;

const SubmitButton = styled.button`
  background: #ff9800;
  color: #222;
  border: none;
  border-radius: 4px;
  padding: 8px 24px;
  font-weight: bold;
  margin-top: 45px;
  cursor: pointer;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
`;

interface PopUpNotesProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (note: string) => void;
}

export const PopUpNotes: React.FC<PopUpNotesProps> = ({ open, onClose, onSubmit }) => {
  const [note, setNote] = useState('');
  const [isMinimized, setIsMinimized] = useState(false);
  const setDynamicState = useSetDynamicState();

  if (!open) return null;

  return (
    <PopupOverlay>
      <PopupContainer isMinimized={isMinimized}>
        <PopupHeader>
          <HeaderTitle>Query</HeaderTitle>
          <div>
            <MinimizeButton onClick={() => setIsMinimized(!isMinimized)}>
              {isMinimized ? '↑' : '↓'}
            </MinimizeButton>
            <CloseButton onClick={onClose}>×</CloseButton>
          </div>
        </PopupHeader>
        {!isMinimized && (
          <PopupContent>
            <NotesWrapper>
              <InvoiceSummaryNotes
                notes={note}
                onNotesChange={setNote}
              />
            </NotesWrapper>
            <SubmitButton
              onClick={() => {
                onSubmit(note);
                setDynamicState('popUpNote', note);
                setNote('');
                onClose();
              }}
              disabled={!note.trim()}
            >
              SUBMIT QUERY
            </SubmitButton>
          </PopupContent>
        )}
      </PopupContainer>
    </PopupOverlay>
  );
};
