import React from 'react';
import { Controller, FieldError, RegisterOptions } from 'react-hook-form';
import styled from 'styled-components';
import Checkbox from '../../Components/Inputs/CheckBoxes/CheckBoxMolecule/CheckBoxMolecule';
import SubscriptionCardWithDisclaimer from '../../Components/Subscription/SubscriptionCardWithDisclaimer/SubscriptionCardWithDisclaimer';
import { Text } from '../Text/Text';

interface SubscriptionProps {
  control?: any;
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  rules?: RegisterOptions;
  name?: string;
  error?: FieldError | null;
  isStory?: boolean;
  canChange?: boolean;
}

const SubscriptionWrapper = styled.div<SubscriptionProps>`
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto;
  justify-items: center;
  width: 100%;
  height: 100%;
  grid-row-gap: 2rem;
`;

const SubscriptionHeader = styled.div`
  justify-items: center;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
`;

const AgreementCheckboxContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  width: 60%;
  justify-content: center;
  justify-items: center;
  padding-bottom: 2rem;
`;

const Selectionbox = styled.div`
  display: grid;
  /* grid-template-columns: 16px auto; */
  align-items: center;
  border-radius: 6px;
  padding: 4px 8px;
  gap: 12px;
`;

export const SubscriptionV2 = ({
  control,
  checked = true,
  onChange,
  name = '',
  rules,
  canChange = true,
}: SubscriptionProps) => {
  const handleSubscriptionChange = (value: boolean) => {
    onChange && onChange(value);
  };
  return (
    <SubscriptionWrapper>
      <SubscriptionHeader>
        <Text
          textItems={[
            {
              text: 'Subscription',
              options: { format: 'heading', type: 'page-heading' },
            },
            {
              text: 'You are signing up for the following subscriptions. Additional features and tiers coming soon',
              options: {
                format: 'heading',
                type: 'sub-heading',
                style: { justifyItems: 'center', textAlign: 'center' },
              },
            },
          ]}
        />
      </SubscriptionHeader>
      <SubscriptionCardWithDisclaimer />
      {canChange && (
        <AgreementCheckboxContainer>
          <Selectionbox key={name}>
            <Controller
              control={control}
              name={name}
              rules={rules}
              defaultValue={checked}
              render={({
                field: { onChange, value },
                fieldState: { error: fieldError },
              }) => (
                <Checkbox
                  name={'I accept the subscription'}
                  onChange={(value) => {
                    handleSubscriptionChange(value);
                    onChange(value);
                  }}
                  checked={checked}
                />
              )}
            />
          </Selectionbox>
        </AgreementCheckboxContainer>
      )}
    </SubscriptionWrapper>
  );
};
