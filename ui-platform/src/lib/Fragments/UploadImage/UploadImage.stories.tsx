import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { UploadImage } from './UploadImage';
import { useAppStore } from '../../Engine';

const meta: Meta<typeof UploadImage> = {
  component: UploadImage,
  title: 'Fragments/UploadImage',
  decorators: [
    (Story) => {
      const imageData = useAppStore((state: any) => state.storyImage);
      return (
        <div>
          <Story />
        </div>
      );
    },
  ],
};
export default meta;

type Story = StoryObj<typeof UploadImage>;

export const Default: Story = {
  args: {
    storeKey: 'storyImage',
  },
};
