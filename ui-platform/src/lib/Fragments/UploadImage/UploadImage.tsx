import React from 'react';
import styled from 'styled-components';
import AddFile from '../../Components/Inputs/AddDocuments/AddFile/AddFile';
import { useAppStore, useSetDynamicState } from '../../Engine';

type Props = {
  storeKey: string;
  dragAndDropMessage?: string;
};

const convertFileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

const PreviewContainer = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
`;

const PreviewImage = styled.img`
  width: 100%;
  object-fit: cover;
`;

const RemoveButton = styled.button`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid #e53935;
  color: #e53935;
  padding: 10px 20px;
  cursor: pointer;
  font-weight: bold;
  border-radius: 4px;
  text-transform: uppercase;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.9);
  }
`;

export function UploadImage({
  storeKey,
  dragAndDropMessage = 'Drag and drop an image, or browse',
}: Props): JSX.Element {
  const setDynamicState = useSetDynamicState();
  const value = useAppStore((state: any) => state[storeKey]);

  const handleFileSelect = (files: FileList) => {
    if (files.length > 0) {
      convertFileToBase64(files[0]).then((base64File) => {
        setDynamicState(storeKey, base64File);
      });
    }
  };

  const removeFile = () => {
    setDynamicState(storeKey, null);
  };

  if (value) {
    return (
      <PreviewContainer>
        <PreviewImage src={value} alt="upload preview" />
        <RemoveButton onClick={removeFile}>Remove File</RemoveButton>
      </PreviewContainer>
    );
  }

  return (
    <AddFile
      onSelect={handleFileSelect}
      dragAndDropMessage={dragAndDropMessage}
    />
  );
}
