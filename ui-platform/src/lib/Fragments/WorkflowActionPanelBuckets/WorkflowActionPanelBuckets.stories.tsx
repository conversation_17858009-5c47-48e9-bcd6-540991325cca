import React, { useState } from 'react';
import { ComponentStory, ComponentMeta } from '@storybook/react';
import { WorkflowActionPanelBuckets } from './WorkflowActionPanelBuckets';

export default {
  title: 'Fragments/WorkflowActionPanelBuckets',
  component: WorkflowActionPanelBuckets,
} as ComponentMeta<typeof WorkflowActionPanelBuckets>;

const Template: ComponentStory<typeof WorkflowActionPanelBuckets> = (args) => {
  const [selectedBucket, setSelectedBucket] = useState<any>(null);
  
  return (
    <div style={{ maxWidth: '400px', padding: '20px', backgroundColor: '#3d4143' }}>
      <WorkflowActionPanelBuckets 
        {...args} 
        onBucketSelect={(bucket) => {
          setSelectedBucket(bucket);
          console.log('Selected bucket:', bucket);
        }}
        onClearBuckets={() => {
          setSelectedBucket(null);
          console.log('Cleared buckets');
        }}
      />
      
      {/* {selectedBucket && (
        <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#1A202C', color: '#fff', borderRadius: '4px' }}>
          <h3>Selected Bucket Data:</h3>
          <pre style={{ whiteSpace: 'pre-wrap' }}>
            {JSON.stringify(selectedBucket, null, 2)}
          </pre>
        </div>
      )} */}
    </div>
  );
};

export const Primary = Template.bind({});

Primary.args = {
  label: 'Bucket Filter',
  buckets: [
    {
      id: 'photos',
      name: 'Photos',
      filterCondition: {
        name: 'documentType',
        key: 'type',
        value: 'photo',
        operator: 'equals'
      }
    },
    {
      id: 'quotations',
      name: 'Quotations',
      filterCondition: {
        name: 'documentType',
        key: 'type',
        value: 'quotation',
        operator: 'equals'
      }
    },
    {
      id: 'invoices',
      name: 'Invoices',
      filterCondition: {
        name: 'documentType',
        key: 'type',
        value: 'invoice',
        operator: 'equals'
      }
    },
    {
      id: 'reports',
      name: 'Reports',
      filterCondition: {
        name: 'documentType',
        key: 'type',
        value: 'report',
        operator: 'equals'
      }
    },
    {
      id: 'latest',
      name: 'Latest Documents',
      filterCondition: {
        name: 'latestDocuments',
        key: 'createdAt',
        value: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
        operator: 'greaterThan'
      }
    }
  ]
};

export const WithSelectedBucket = Template.bind({});

WithSelectedBucket.args = {
  ...Primary.args,
  defaultSelectedBucketId: 'payments',
  buckets: [
    ...Primary.args.buckets,
    {
      id: 'payments',
      name: 'Payments',
      filterCondition: {
        name: 'documentType',
        key: 'type',
        value: 'payment',
        operator: 'equals'
      }
    }
  ]
};
