import React from 'react';
import { ComponentStory, ComponentMeta } from '@storybook/react';
import { WorkflowActionPanelFilters } from './WorkflowActionPanelFilters';
import { FilterCondition } from '../../Engine/models/filter-condition';

export default {
  title: 'Fragments/WorkflowActionPanelFilters',
  component: WorkflowActionPanelFilters,
  parameters: {
    backgrounds: {
      default: 'dark',
      values: [
        { name: 'dark', value: '#1A202C' },
        { name: 'light', value: '#F7FAFC' },
      ],
    },
  },
} as ComponentMeta<typeof WorkflowActionPanelFilters>;

const Template: ComponentStory<typeof WorkflowActionPanelFilters> = (args) => (
  <div style={{ maxWidth: '400px', padding: '20px' }}>
    <WorkflowActionPanelFilters {...args} />
  </div>
);

export const Default = Template.bind({});
Default.args = {
  label: 'Filter Workflow',
  filters: [
    // Range type filter example (for numeric values)
    {
      id: 'job_value',
      name: 'Job Value',
      type: 'range',
    },
    // Select type filter examples (for dropdown selection)
    {
      id: 'job_state',
      name: 'Job State',
      type: 'select',
      options: [
        { id: 12, name: 'New' },
        { id: 13, name: 'In Progress' },
        { id: 14, name: 'Completed' },
        { id: 15, name: 'On Hold' },
        { id: 16, name: 'Cancelled' },
      ],
    },
    {
      id: 'job_type',
      name: 'Job Type',
      type: 'select',
      options: [
        { id: 27, name: 'Repair' },
        { id: 28, name: 'Installation' },
        { id: 29, name: 'Maintenance' },
        { id: 30, name: 'Inspection' },
      ],
    },
    {
      id: 'customer_type',
      name: 'Customer Type',
      type: 'select',
      options: [
        { id: 31, name: 'Residential' },
        { id: 32, name: 'Commercial' },
        { id: 33, name: 'Government' },
      ],
    },
  ],
  // Example of how filters are applied and removed
  onFilterApply: (filter) => console.log('Filter applied:', filter),
  onFilterRemove: (filterId) => console.log('Filter removed:', filterId),
};

export const WithPreselectedFilters = Template.bind({});
WithPreselectedFilters.args = {
  ...Default.args,
  filters: [
    // Range type filter
    {
      id: 'job_value',
      name: 'Job Value',
      type: 'range',
    },
    // Select type filters
    {
      id: 'job_state',
      name: 'Job State',
      type: 'select',
      options: [
        { id: 12, name: 'New' },
        { id: 13, name: 'In Progress'},
        { id: 14, name: 'Completed' },
        { id: 15, name: 'On Hold' },
        { id: 16, name: 'Cancelled'},
      ],
    },
    {
      id: 'priority',
      name: 'Priority',
      type: 'select',
      options: [
        { id: 23, name: 'Low' },
        { id: 24, name: 'Medium'},
        { id: 25, name: 'High',},
        { id: 26, name: 'Urgent' },
      ],
    },
    // Text type filter
    {
      id: 'description',
      name: 'Description',
      type: 'text',
    },
  ],
  // Example of pre-selected filters that would be applied
  // This is just for demonstration - the actual application would happen in the decorator
};

// This story demonstrates how to initialize the component with pre-selected filters
WithPreselectedFilters.decorators = [
  (Story) => {
    // We need to use React.useEffect to set the applied filters after the component mounts
    // This simulates a real-world scenario where filters might be loaded from a URL or state
    React.useEffect(() => {
      const filtersComponent = document.querySelector('[data-testid="workflow-action-panel-filters"]');
      if (filtersComponent) {
        // In a real implementation, you would use props or context to set initial filters
        // This is just for demonstration purposes in Storybook
        console.log('Component mounted - in a real app, you would set initial filters here');
      }
    }, []);
    return <Story />;
  },
];
