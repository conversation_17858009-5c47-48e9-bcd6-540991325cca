import React from 'react';
import { ComponentStory, ComponentMeta } from '@storybook/react';
import { WorkflowActionPanelSearch } from './WorkflowActionPanelSearch';

export default {
  title: 'Fragments/WorkflowActionPanelSearch',
  component: WorkflowActionPanelSearch,
} as ComponentMeta<typeof WorkflowActionPanelSearch>;

const Template: ComponentStory<typeof WorkflowActionPanelSearch> = (args) => (
  <WorkflowActionPanelSearch {...args} />
);

export const Primary = Template.bind({});

Primary.args = {
    searchUrl: 'https://jsonplaceholder.typicode.com/posts',
    setSearchResults: (results: any) => console.log(results),
    token: 'your-token',
    tokenPrefix: 'Bearer',
    items: [],
};


