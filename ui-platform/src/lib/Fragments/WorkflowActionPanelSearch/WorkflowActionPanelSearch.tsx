import styled from "styled-components";
import { SearchInput } from "../../Components/Inputs/SearchInput/SearchInput";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Switch } from "../../Components/Inputs/Switch/Switch";
import { useFilteringEngineStore } from "../../Engine";

const PanelContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  overflow: hidden;
  border-radius: 10px;
`;

// const Title = styled.h2`
//   color: #fff;
//   font-size: 24px;
//   font-weight: 600;
//   // text-align: center;
//   margin: 0 0 12px 0;
// `;

// const TitleUnderlineContainer = styled.div`
//   display: flex;
//   flex-direction: column;
//   // align-items: center;
//   // width: 100%;
//   align-items: center;
//   margin-bottom: 0;
//   max-width: 320px;
// `;

// const Underline = styled.div`
//   width: 270px;
//   height: 2px;
//   background: #2e8bb8;
//   margin: 0 0 24px 0;
//   border-radius: 2px;
// `;

const SearchRow = styled.div`
  display: flex;
  width: 100%;
  justify-content: center;
  margin-bottom: 16px;
  padding: 0 16px;
`;

const WorkflowSearch = styled(SearchInput)`
  width: 90%;
box-sizing: border-box;
  border: 1px solid #6c757d;
  color: #fff;
  font-size: 16px;
  padding-right: 36px;
  margin-left: 5px;
`;

const SwitchRow = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 8px;
  margin-bottom: 0;
  width: 100%;
  padding: 0 16px;
`;

const SwitchLabel = styled.span`
  color: #bfc3c5;
  font-size: 15px;
  margin-left: 12px;
  user-select: none;
  padding-top: 6px;
`;

interface Props {
  searchUrl: string;
  token: string;
  tokenPrefix: string;
}



export function WorkflowActionPanelSearch({searchUrl, token, tokenPrefix,}: Props) {
  //
  const [setFilterFunctions, clearFilterFunctions] = useFilteringEngineStore(state => [
    state.setFilterFunctions,
    state.clearFilterFunctions,
  ]);

  // Local component state
  const [isClosedClaim, setIsClosedClaim] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const abortControllerRef = useRef<AbortController | null>(null);
  
  // Handle search term changes
  const handleSearchTermChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newTerm = e.target.value;
    setSearchTerm(newTerm);
    
    // If search term is cleared, also clear filters
    if (newTerm === '') {
      clearFilterFunctions();
    }
  }, [clearFilterFunctions]);


  // Prevent event propagation for all interactions within the panel
  // const handlePanelInteraction = (event: React.MouseEvent | React.KeyboardEvent) => {
  //   event.stopPropagation();
  // };


    const performSearch = async (term: string, isClosedClaim: boolean) => {
      
      if (!term || !searchUrl) {
        setIsSearching(false);
        return;
      }
      
      setIsSearching(true);
      try {
        // Cancel any ongoing search request
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
        
        // Create a new abort controller for this request
        const abortController = new AbortController();
        abortControllerRef.current = abortController;
        
        const response = await fetch(`${searchUrl}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            authorization: `${tokenPrefix} ${token}`,
          },
          body: JSON.stringify({
            search: term,
            active: !isClosedClaim,
          }),
          signal: abortController.signal,
        });
        
        if (!response.ok) throw new Error('Search request failed');
        const data = await response.json();
        const searchResults = data.payload || [];
        setFilterFunctions([{ name: 'search_results', filterFn: () => true, getSearchResults: () => searchResults}])
      } catch (error) {
        if ((error as any)?.name === 'AbortError') return; // build type error fix
        console.error('Search error:', error);
        setIsSearching(false);
      } finally {
        setIsSearching(false);
      }
    };


    // Clear filters on unmount
    useEffect(() => {
      () => {
        clearFilterFunctions(); 
      }
    }, []);

    return (
        <PanelContainer 
        // onClick={handlePanelInteraction} 
        // onKeyDown={handlePanelInteraction}
        >
          <SearchRow>
            <WorkflowSearch
              placeholder="Search"
              value={searchTerm}
              onChange={handleSearchTermChange}
              onSubmit={(e) => {
                e.preventDefault();
                performSearch(searchTerm, isClosedClaim);
              }}
              onClear={() => {
                setSearchTerm('');
                clearFilterFunctions();
              }}
              isLoading={isSearching}
            />
          </SearchRow>
          <SwitchRow>
            <Switch
              enabled="On"
              disabled="Off"
              name="isClosedClaim"
              isOn={isClosedClaim}
              onChange={setIsClosedClaim}
            />
            <SwitchLabel>Include closed claims</SwitchLabel>
          </SwitchRow>
        </PanelContainer>
    );
}