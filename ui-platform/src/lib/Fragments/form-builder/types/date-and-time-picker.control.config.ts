import { Control, FieldError, RegisterOptions } from 'react-hook-form';
import { BaseControlConfig } from './base.control.config';
import { IconTypes } from '../../../Components/Icons';

export interface DateAndTimePickerControlConfig extends BaseControlConfig {
  type: 'date-and-time-picker';
  placeholder?: string;
  icon?: IconTypes;
  className?: string;
  iconPosition?: 'left' | 'right' | 'none';
  fieldError?: FieldError | undefined | null;
  rules?: RegisterOptions;
  control?: Control;
  value?: string; // ISO date string with time
  state?: 'default' | 'display-only';
  onChange?: (value: string) => void;
  validMinutes?: number[];
  // DatePicker specific props
  mode?: 'single'; // Only single mode supported for date-time picker
  numberOfMonths?: number;
  disablePast?: boolean;
  disabledDates?: string[];
  disableFuture?: boolean;
  weekendSelectable?: boolean;
}
