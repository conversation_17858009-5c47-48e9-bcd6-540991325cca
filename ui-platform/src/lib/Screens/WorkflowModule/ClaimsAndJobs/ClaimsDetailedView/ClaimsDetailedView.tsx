import React, { useMemo } from 'react';
import {
  StateShell,
} from '../../../../Shells';
import { Pagination } from '../../../../Fragments/Pagination/Pagination';
import styled from 'styled-components';
import { ScrollableContent } from '../../../../Components/Scrollbar/Scrollbar';
import { useListPagination2 } from '../../../../Components';
import {  ClaimCardList } from '../../../../Fragments';
import { StateConfig, useFilteringEngineStore } from '../../../../Engine';




const ModuleContent = styled.div`
  background: ${(props) => props.theme.ColorsBackgroundModule};
  padding-top: 2rem;
  padding-left: 1rem;
  padding-right: 1rem;
  box-sizing: border-box;
  border-radius: 0 7px 7px 7px;
  grid-area: Module-Content;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
`;

const FiltersWrapper = styled.div`
  position: relative;
  z-index: 2;
`;

const JobListWrapper = styled.div`
  position: relative;
  z-index: 1;
  margin-top: 1rem;
`;

const ViewShellPagination = styled(Pagination)`
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
`;

const Content = styled(ScrollableContent)<{
  items?: any[];
  scrollable?: boolean;
}>`
  max-height: 100% !important;
  padding-right: ${(props) => props.theme.SpacingXl};
  height: calc(100vh - 2rem - 72px);

  ${(props) =>
    props?.scrollable || (props?.items && props?.items.length > 0)
      ? 'padding-bottom: 4rem; box-sizing: border-box'
      : ''};
  ${(props) => (!props?.scrollable ? 'overflow: hidden;' : '')};
`;



interface Props {
  claims: any[];
  displayLogo?: boolean;
  itemsPerPage?: number;
  jobCardNumberPrefix: string;
  searchUrl?: string;
  ClaimLinkRouter: any;
  JobLinkRouter: any;
  actionPanels: StateConfig['actionPanels'];
  getClaimMenuItems: (claim: any) => {icon: string; label: string; path: string}[];
  getJobMenuItems: (job: any) => {icon: string; label: string; path: string}[];
}

export function ClaimsDetailedView({ ClaimLinkRouter, JobLinkRouter, claims, actionPanels, itemsPerPage, getClaimMenuItems, getJobMenuItems, displayLogo}: Props) {
 
  //
  const getFilteredData = useFilteringEngineStore(state => state.getFilteredData);
  const filterFunctions = useFilteringEngineStore(state => state.filterFunctions);

  const filteredClaims = useMemo(() => {
    console.log("Filter Functions Count", filterFunctions.length);
    if (filterFunctions.length === 1 && filterFunctions[0].name === 'search_results') {
      return filterFunctions[0]?.getSearchResults?.() || [];
    }
    return getFilteredData(claims);
  }, [claims, getFilteredData, filterFunctions.length]);
  
  // Handle pagination
  const { pages, currentPage, pageItems, ...rest } = useListPagination2({
    items: filteredClaims || [],
    itemsPerPage: itemsPerPage || 10,
  });

  const config: StateConfig = {
    title: { 
        template: '', 
        
    },
    fetchCalls: [],
    onEnter: [],
    onLeave: [],
    defaultScreen: '',
    screens: {},
    actionPanels,
};
  
  return (
      <StateShell
        callClientAction={() => {}}
        stateConfig={config}
        clientDataObject={{}}
        fakeUseNavigation={{ state: 'idle' }}
      >
        <ModuleContent data-testid="workflow-view-shell-module-content">
          <Content
            data-testid="workflow-view-shell-content"
            items={pageItems}
            scrollable={true}
          >

            <JobListWrapper>
              <ClaimCardList claims={pageItems} getClaimMenuItems={getClaimMenuItems} getJobMenuItems={getJobMenuItems} ClaimLinkRouter={ClaimLinkRouter} JobLinkRouter={JobLinkRouter} displayLogo={displayLogo} />
            </JobListWrapper>
          </Content>
          <ViewShellPagination
            pages={pages}
            currentPage={currentPage}
            {...rest}
          />
        </ModuleContent>
      </StateShell>
  );
}
