import { MappedClaim } from "../../../Engine/models/mapped-claim";



export const sampleClaims: MappedClaim[] = [
    {
        "id": 1260,
        "applicant": {
            "first_name": "King",
            "surname": "<PERSON>",
            "id_number": "0515123232121",
            "claimantpoliceynum": "011221041"
        },
        "application_creator": {
            "id": 92,
            "full_name": "Lenin Lobi",
            "roles": [
                35,
                34,
                1
            ]
        },
        "application_date": "2025-06-25T13:47:13.057986",
        "state": 2,
        "state_change_date": "2025-06-25T13:47:13.055389",
        "location": 13624,
        "modified_date": "2025-06-26T12:13:44.381341",
        "mid": "KK25/tr432",
        "sub_section": 3,
        "jobs": [
            {
                "id": 2843,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.151679900",
                "long": "27.918006200",
                "claim": {
                    "id": 1260,
                    "mid": "KK25/tr432",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "King",
                        "surname": "Kobe"
                    },
                    "property_city": "Johannesburg",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "14 Ellis Street",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "dmFW24AC2Ewu2dD8Yae3ESGsQ2yq9rMaAMKE1vF3u364ARXuN3MDFJ",
                "valid_job": 11,
                "updated": "2025-06-26T12:13:44.331859",
                "on_site": null,
                "distance": null,
                "job_creator": 92,
                "skill": 86,
                "sp": 2,
                "team_leader": null,
                "area": 1,
                "state": 45,
                "supplier_type": 9,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            },
            {
                "id": 2844,
                "appointment": {
                    "id": 1738,
                    "job": 2844,
                    "state": 4,
                    "range_start": "2025-06-26T08:00:00",
                    "range_end": null,
                    "appointment_type": 4,
                    "reason": null
                },
                "note_count": 1,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.151679900",
                "long": "27.918006200",
                "claim": {
                    "id": 1260,
                    "mid": "KK25/tr432",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "King",
                        "surname": "Kobe"
                    },
                    "property_city": "Johannesburg",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "14 Ellis Street",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "2QJ68fJqYsWN8zRmfEEBz89TweDuqnb9K9HPxBCJCDXFdKNPkMDYXP",
                "valid_job": null,
                "updated": "2025-06-26T09:47:57.662861",
                "on_site": true,
                "distance": null,
                "job_creator": 92,
                "skill": 1,
                "sp": 224,
                "team_leader": 86,
                "area": 1,
                "state": 288,
                "supplier_type": 1,
                "forced_location": 13625,
                "assessor": null,
                "authorizer": null,
                "location": null
            }
        ],
        "note_count": 1,
        "repudiation": null,
        "deceased": [],
        "form_start": "2025-06-25T13:47:13.057563",
        "assessor": null,
        "cat_code": null,
        "claim_type_id": 1,
        "private_banking": 0,
        "unread_note_count": null,
        "source": "KingPrice",
        "source_id": 3,
        "source_key": "KPC"
    },
    {
        "id": 1259,
        "applicant": {
            "first_name": "Shaun",
            "surname": "SMSs",
            "id_number": "*************",
            "claimantpoliceynum": "**********"
        },
        "application_creator": {
            "id": 98,
            "full_name": "Lakeisha",
            "roles": [
                35,
                34,
                1
            ]
        },
        "application_date": "2025-06-25T13:40:52.814788",
        "state": 2,
        "state_change_date": "2025-06-25T13:40:52.812335",
        "location": 13623,
        "modified_date": "2025-06-26T09:54:36.514776",
        "mid": "Hy25/54321",
        "sub_section": 3,
        "jobs": [
            {
                "id": 2842,
                "appointment": {
                    "id": 1737,
                    "job": 2842,
                    "state": 4,
                    "range_start": "2025-06-26T09:00:00",
                    "range_end": null,
                    "appointment_type": 4,
                    "reason": null
                },
                "note_count": 1,
                "unread_note_count": null,
                "claim_type_id": 2,
                "assessor_name": "",
                "lat": "-26.151679900",
                "long": "27.918006200",
                "claim": {
                    "id": 1259,
                    "mid": "Hy25/54321",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Shaun",
                        "surname": "SMSs"
                    },
                    "property_city": "Johannesburg",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "14 Ellis Street",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "KAbMfjrdtbjKLHuewbjfVY8MDdk82yWWx74VLMG4mN4smDPAEkgTbL",
                "valid_job": null,
                "updated": "2025-06-26T09:54:36.468632",
                "on_site": true,
                "distance": null,
                "job_creator": 98,
                "skill": 1,
                "sp": 224,
                "team_leader": 86,
                "area": 1,
                "state": 288,
                "supplier_type": 1,
                "forced_location": 13626,
                "assessor": null,
                "authorizer": null,
                "location": null
            },
            {
                "id": 2841,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 2,
                "assessor_name": "",
                "lat": "-26.151679900",
                "long": "27.918006200",
                "claim": {
                    "id": 1259,
                    "mid": "Hy25/54321",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Shaun",
                        "surname": "SMSs"
                    },
                    "property_city": "Johannesburg",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "14 Ellis Street",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "LpJL4KPg5fN7VD3JQLCE2pBtTGmp6H1TA5kTYrzXjrMy2mxexpDpmM",
                "valid_job": 11,
                "updated": "2025-06-25T13:40:52.884016",
                "on_site": null,
                "distance": null,
                "job_creator": 98,
                "skill": 86,
                "sp": 2,
                "team_leader": null,
                "area": 1,
                "state": 218,
                "supplier_type": 9,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            }
        ],
        "note_count": 1,
        "repudiation": null,
        "deceased": [],
        "form_start": "2025-06-25T13:40:52.814359",
        "assessor": null,
        "cat_code": null,
        "claim_type_id": 2,
        "private_banking": 0,
        "unread_note_count": null,
        "source": "KingPrice",
        "source_id": 3,
        "source_key": "KPC"
    },
    {
        "id": 1258,
        "applicant": {
            "first_name": "Shaun",
            "surname": "Testing",
            "id_number": "*************",
            "claimantpoliceynum": "*********"
        },
        "application_creator": {
            "id": 96,
            "full_name": "Kevin Van Wyk",
            "roles": [
                5,
                6,
                8,
                34,
                1,
                27,
                44,
                37
            ]
        },
        "application_date": "2025-06-25T13:06:49.157309",
        "state": 2,
        "state_change_date": "2025-06-25T13:06:49.154771",
        "location": 13622,
        "modified_date": "2025-06-25T13:30:28.823748",
        "mid": "uB25/0987HI",
        "sub_section": 3,
        "jobs": [
            {
                "id": 2840,
                "appointment": {
                    "id": 1736,
                    "job": 2840,
                    "state": 1,
                    "range_start": "2025-06-26T11:00:00",
                    "range_end": null,
                    "appointment_type": 5,
                    "reason": null
                },
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 2,
                "assessor_name": "",
                "lat": "-26.151679900",
                "long": "27.918006200",
                "claim": {
                    "id": 1258,
                    "mid": "uB25/0987HI",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Shaun",
                        "surname": "Testing"
                    },
                    "property_city": "Johannesburg",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "14 Ellis Street",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "9EysFvgSdevyFT7eFawEdd2sKn7GjeR7v6v1xqvaq27mKhxuXfzkkP",
                "valid_job": null,
                "updated": "2025-06-25T13:30:28.764713",
                "on_site": null,
                "distance": null,
                "job_creator": 96,
                "skill": 1,
                "sp": 5,
                "team_leader": 55,
                "area": 1,
                "state": 32,
                "supplier_type": 1,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            },
            {
                "id": 2839,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 2,
                "assessor_name": "",
                "lat": "-26.151679900",
                "long": "27.918006200",
                "claim": {
                    "id": 1258,
                    "mid": "uB25/0987HI",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Shaun",
                        "surname": "Testing"
                    },
                    "property_city": "Johannesburg",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "14 Ellis Street",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "aSDL2GAkt1qTXqkBECwBq5VSY6kDagpmR7xsQCLHVD5kKD4BjEv6mz",
                "valid_job": 11,
                "updated": "2025-06-25T13:06:49.227394",
                "on_site": null,
                "distance": null,
                "job_creator": 96,
                "skill": 86,
                "sp": 2,
                "team_leader": null,
                "area": 1,
                "state": 218,
                "supplier_type": 9,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            }
        ],
        "note_count": 0,
        "repudiation": null,
        "deceased": [],
        "form_start": "2025-06-25T13:06:49.156898",
        "assessor": null,
        "cat_code": null,
        "claim_type_id": 2,
        "private_banking": 0,
        "unread_note_count": null,
        "source": "KingPrice",
        "source_id": 3,
        "source_key": "KPC"
    },
    {
        "id": 1257,
        "applicant": {
            "first_name": "Killian",
            "surname": "Mpappe",
            "id_number": "*************",
            "claimantpoliceynum": "KM2545855"
        },
        "application_creator": {
            "id": 84,
            "full_name": "BR CH",
            "roles": [
                6,
                11,
                34,
                1
            ]
        },
        "application_date": "2025-06-24T13:24:53.553116",
        "state": 3,
        "state_change_date": "2025-06-24T13:24:53.550691",
        "location": 13618,
        "modified_date": "2025-06-24T14:11:21.287390",
        "mid": "KM554588",
        "sub_section": 3,
        "jobs": [
            {
                "id": 2833,
                "appointment": {
                    "id": 1732,
                    "job": 2833,
                    "state": 4,
                    "range_start": "2025-06-24T17:15:00",
                    "range_end": null,
                    "appointment_type": 5,
                    "reason": null
                },
                "note_count": 2,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152466400",
                "long": "27.921942700",
                "claim": {
                    "id": 1257,
                    "mid": "KM554588",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Killian",
                        "surname": "Mpappe"
                    },
                    "property_city": "Johannesburg",
                    "property_complex": "Quadram",
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Roodepoort"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Roodepoort",
                "address": "Constanita",
                "postal_code": "1709",
                "claim_value": "1325.65",
                "mid": null,
                "ping_count": 1,
                "token": "B49VRVPnMuaEz6aPAcRFkH49chGBsfjYJ7cR2WYqauAre4YF5RCtkY",
                "valid_job": 1,
                "updated": "2025-06-24T14:11:21.231064",
                "on_site": true,
                "distance": null,
                "job_creator": 84,
                "skill": 1,
                "sp": 224,
                "team_leader": 86,
                "area": 1,
                "state": 226,
                "supplier_type": 1,
                "forced_location": 13619,
                "assessor": null,
                "authorizer": 4,
                "location": null
            },
            {
                "id": 2832,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152466400",
                "long": "27.921942700",
                "claim": {
                    "id": 1257,
                    "mid": "KM554588",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Killian",
                        "surname": "Mpappe"
                    },
                    "property_city": "Johannesburg",
                    "property_complex": "Quadram",
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Roodepoort"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Roodepoort",
                "address": "Constanita",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "YzjCjJPFRVnYhxEkwSqp84LprEt7SzuhzKxHFx5snWsQn5N9HLAfcv",
                "valid_job": 11,
                "updated": "2025-06-24T13:24:53.622469",
                "on_site": null,
                "distance": null,
                "job_creator": 84,
                "skill": 86,
                "sp": 2,
                "team_leader": null,
                "area": 1,
                "state": 218,
                "supplier_type": 9,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            },
            {
                "id": 2834,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152466400",
                "long": "27.921942700",
                "claim": {
                    "id": 1257,
                    "mid": "KM554588",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Killian",
                        "surname": "Mpappe"
                    },
                    "property_city": "Johannesburg",
                    "property_complex": "Quadram",
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Roodepoort"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Roodepoort",
                "address": "Constanita",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "cYr4cMhQSbJ1rV8s2ucHpw2yDWAydpmhjCqemEhvT47VkPS5r7e4hy",
                "valid_job": null,
                "updated": "2025-06-24T13:56:43.226379",
                "on_site": null,
                "distance": null,
                "job_creator": 84,
                "skill": 39,
                "sp": 19,
                "team_leader": null,
                "area": 1,
                "state": 222,
                "supplier_type": 4,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            }
        ],
        "note_count": 2,
        "repudiation": null,
        "deceased": [],
        "form_start": "2025-06-24T13:24:53.552812",
        "assessor": null,
        "cat_code": null,
        "claim_type_id": 1,
        "private_banking": 0,
        "unread_note_count": null,
        "source": "KingPrice",
        "source_id": 3,
        "source_key": "KPC"
    },
    {
        "id": 1256,
        "applicant": {
            "first_name": "Mamelodi",
            "surname": "Sundowns",
            "id_number": "*************",
            "claimantpoliceynum": "MS1254686"
        },
        "application_creator": {
            "id": 84,
            "full_name": "BR CH",
            "roles": [
                6,
                11,
                34,
                1
            ]
        },
        "application_date": "2025-06-23T09:34:33.293212",
        "state": 95,
        "state_change_date": "2025-06-23T09:34:33.290854",
        "location": 13617,
        "modified_date": "2025-06-26T12:50:04.723010",
        "mid": "MS001010",
        "sub_section": 3,
        "jobs": [
            {
                "id": 2831,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 13,
                "assessor_name": "",
                "lat": "-26.152748400",
                "long": "27.921953700",
                "claim": {
                    "id": 1256,
                    "mid": "MS001010",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Mamelodi",
                        "surname": "Sundowns"
                    },
                    "property_city": "Johannesburg",
                    "property_complex": "Quadram",
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Roodeppoort"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Roodeppoort",
                "address": "Constantia",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "D93n7zBDGAbnwxcALFHQ1BbEqjaauV4eTSVeaLMTAtaPCC3rCHXk1w",
                "valid_job": null,
                "updated": "2025-06-23T09:34:33.493928",
                "on_site": null,
                "distance": null,
                "job_creator": 84,
                "skill": 44,
                "sp": null,
                "team_leader": null,
                "area": 1,
                "state": 88,
                "supplier_type": 6,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            },
            {
                "id": 2837,
                "appointment": {
                    "id": 1733,
                    "job": 2837,
                    "state": 1,
                    "range_start": "2025-06-24T18:00:00",
                    "range_end": null,
                    "appointment_type": 5,
                    "reason": null
                },
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 13,
                "assessor_name": "",
                "lat": "-26.152748400",
                "long": "27.921953700",
                "claim": {
                    "id": 1256,
                    "mid": "MS001010",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Mamelodi",
                        "surname": "Sundowns"
                    },
                    "property_city": "Johannesburg",
                    "property_complex": "Quadram",
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Roodeppoort"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Roodeppoort",
                "address": "Constantia",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "eJqQX6N6y8u79h6Erxk3PRNKdHrMR8LNk1j8z1uVSb2WTpMYsjnFEt",
                "valid_job": null,
                "updated": "2025-06-24T14:40:01.504630",
                "on_site": null,
                "distance": null,
                "job_creator": 84,
                "skill": 2,
                "sp": 224,
                "team_leader": null,
                "area": 1,
                "state": 21,
                "supplier_type": 1,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            },
            {
                "id": 2830,
                "appointment": {},
                "note_count": 1,
                "unread_note_count": null,
                "claim_type_id": 13,
                "assessor_name": "",
                "lat": "-26.152748400",
                "long": "27.921953700",
                "claim": {
                    "id": 1256,
                    "mid": "MS001010",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Mamelodi",
                        "surname": "Sundowns"
                    },
                    "property_city": "Johannesburg",
                    "property_complex": "Quadram",
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Roodeppoort"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Roodeppoort",
                "address": "Constantia",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "htz69gV632m1eyJCkqwCXupYndjV7kqRgPVJFK5dtSXvcL7k3ArSqJ",
                "valid_job": 11,
                "updated": "2025-06-23T09:34:33.368545",
                "on_site": null,
                "distance": null,
                "job_creator": 84,
                "skill": 86,
                "sp": 2,
                "team_leader": null,
                "area": 1,
                "state": 218,
                "supplier_type": 9,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            }
        ],
        "note_count": 1,
        "repudiation": null,
        "deceased": [],
        "form_start": "2025-06-23T09:34:33.292913",
        "assessor": null,
        "cat_code": null,
        "claim_type_id": 13,
        "private_banking": 0,
        "unread_note_count": null,
        "source": "KingPrice",
        "source_id": 3,
        "source_key": "KPC"
    },
    {
        "id": 1255,
        "applicant": {
            "first_name": "jhgfdsjh",
            "surname": "jhgfdsjh",
            "id_number": "*************",
            "claimantpoliceynum": "*************"
        },
        "application_creator": {
            "id": 1,
            "full_name": null,
            "roles": [
                6,
                11,
                34,
                1,
                27
            ]
        },
        "application_date": "2025-06-20T14:51:10.606030",
        "state": 2,
        "state_change_date": "2025-06-20T14:51:10.604082",
        "location": 13616,
        "modified_date": "2025-06-20T17:00:35.394261",
        "mid": "Test52626266",
        "sub_section": 3,
        "jobs": [
            {
                "id": 2829,
                "appointment": {
                    "id": 1731,
                    "job": 2829,
                    "state": 1,
                    "range_start": "2025-06-20T17:00:00",
                    "range_end": null,
                    "appointment_type": 5,
                    "reason": null
                },
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.153082700",
                "long": "27.920034300",
                "claim": {
                    "id": 1255,
                    "mid": "Test52626266",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "jhgfdsjh",
                        "surname": "jhgfdsjh"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Florida"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Florida",
                "address": "50 constantia boulevard",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "avsrGz5QEu4rqPYnSpbyv8apeHemw5DRty9xFdpc7ehSHD2awXP9R9",
                "valid_job": null,
                "updated": "2025-06-20T14:57:01.457641",
                "on_site": null,
                "distance": null,
                "job_creator": 1,
                "skill": 1,
                "sp": 4,
                "team_leader": null,
                "area": 1,
                "state": 21,
                "supplier_type": 1,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            },
            {
                "id": 2828,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.153082700",
                "long": "27.920034300",
                "claim": {
                    "id": 1255,
                    "mid": "Test52626266",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "jhgfdsjh",
                        "surname": "jhgfdsjh"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Florida"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Florida",
                "address": "50 constantia boulevard",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "PBmpVPmFWtgfmSaqEWLPt7gX6NGu9uXehYcRbPpBjNmWN8xkSfsJY9",
                "valid_job": 11,
                "updated": "2025-06-20T14:51:10.664901",
                "on_site": null,
                "distance": null,
                "job_creator": 1,
                "skill": 86,
                "sp": 2,
                "team_leader": null,
                "area": 1,
                "state": 218,
                "supplier_type": 9,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            }
        ],
        "note_count": 0,
        "repudiation": null,
        "deceased": [],
        "form_start": "2025-06-20T14:51:10.605779",
        "assessor": null,
        "cat_code": null,
        "claim_type_id": 1,
        "private_banking": 0,
        "unread_note_count": null,
        "source": "KingPrice",
        "source_id": 3,
        "source_key": "KPC"
    },
    {
        "id": 1254,
        "applicant": {
            "first_name": "Ttest.........jbhvgcf",
            "surname": "Ttest.........jbhvgcf",
            "id_number": "*************",
            "claimantpoliceynum": "Ttest2458"
        },
        "application_creator": {
            "id": 1,
            "full_name": null,
            "roles": [
                6,
                11,
                34,
                1,
                27
            ]
        },
        "application_date": "2025-06-20T14:47:47.712440",
        "state": 2,
        "state_change_date": "2025-06-20T14:47:47.710053",
        "location": 13615,
        "modified_date": "2025-06-21T12:00:37.713920",
        "mid": "Ttest2458",
        "sub_section": 3,
        "jobs": [
            {
                "id": 2827,
                "appointment": {
                    "id": 1730,
                    "job": 2827,
                    "state": 1,
                    "range_start": "2025-06-20T17:00:00",
                    "range_end": null,
                    "appointment_type": 5,
                    "reason": null
                },
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.153082700",
                "long": "27.920034300",
                "claim": {
                    "id": 1254,
                    "mid": "Ttest2458",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Ttest.........jbhvgcf",
                        "surname": "Ttest.........jbhvgcf"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Florida"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Florida",
                "address": "50 constantia boulevard",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "WYqx313a4Ek1TfkdSp3MXvA52MNG531vmyM3xCHBKFY8yTYKKFMPLt",
                "valid_job": null,
                "updated": "2025-06-20T14:53:01.387838",
                "on_site": null,
                "distance": null,
                "job_creator": 1,
                "skill": 1,
                "sp": 4,
                "team_leader": null,
                "area": 1,
                "state": 21,
                "supplier_type": 1,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            },
            {
                "id": 2826,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.153082700",
                "long": "27.920034300",
                "claim": {
                    "id": 1254,
                    "mid": "Ttest2458",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Ttest.........jbhvgcf",
                        "surname": "Ttest.........jbhvgcf"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Florida"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Florida",
                "address": "50 constantia boulevard",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "5H3KzqJA7dRjB8mVKdQvCTk88mzqgX8Jv9CKX3pEhVTtKtySp6fLWP",
                "valid_job": 11,
                "updated": "2025-06-20T14:47:47.782277",
                "on_site": null,
                "distance": null,
                "job_creator": 1,
                "skill": 86,
                "sp": 2,
                "team_leader": null,
                "area": 1,
                "state": 218,
                "supplier_type": 9,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            }
        ],
        "note_count": 0,
        "repudiation": null,
        "deceased": [],
        "form_start": "2025-06-20T14:47:47.712150",
        "assessor": null,
        "cat_code": null,
        "claim_type_id": 1,
        "private_banking": 0,
        "unread_note_count": null,
        "source": "KingPrice",
        "source_id": 3,
        "source_key": "KPC"
    },
    {
        "id": 1253,
        "applicant": {
            "first_name": "Lava",
            "surname": "Testing New OTP Solution Three",
            "id_number": "*************",
            "claimantpoliceynum": "*********"
        },
        "application_creator": {
            "id": 1,
            "full_name": null,
            "roles": [
                6,
                11,
                34,
                1,
                27
            ]
        },
        "application_date": "2025-06-20T13:57:23.121793",
        "state": 3,
        "state_change_date": "2025-06-20T13:57:23.119345",
        "location": 13613,
        "modified_date": "2025-06-20T14:45:57.621752",
        "mid": "KP*********",
        "sub_section": 3,
        "jobs": [
            {
                "id": 2824,
                "appointment": {
                    "id": 1729,
                    "job": 2824,
                    "state": 6,
                    "range_start": "2025-06-23T12:30:00",
                    "range_end": null,
                    "appointment_type": 6,
                    "reason": null
                },
                "note_count": 1,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152662200",
                "long": "27.921810800",
                "claim": {
                    "id": 1253,
                    "mid": "KP*********",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Lava",
                        "surname": "Testing New OTP Solution Three"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "50 Constantia Boulevard",
                "postal_code": "1709",
                "claim_value": "2575.65",
                "mid": null,
                "ping_count": 1,
                "token": "4MDxznWeQEgk822rV7nPQHvXkYf8K4Hr21QgJ1tMu2FYnKAMvAS3dT",
                "valid_job": 1,
                "updated": "2025-06-20T14:45:57.547605",
                "on_site": true,
                "distance": null,
                "job_creator": 1,
                "skill": 1,
                "sp": 4,
                "team_leader": 26,
                "area": 1,
                "state": 28,
                "supplier_type": 1,
                "forced_location": 13614,
                "assessor": null,
                "authorizer": 4,
                "location": null
            },
            {
                "id": 2823,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152662200",
                "long": "27.921810800",
                "claim": {
                    "id": 1253,
                    "mid": "KP*********",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Lava",
                        "surname": "Testing New OTP Solution Three"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "50 Constantia Boulevard",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "P72D5fnzEkHdJcpeY3huaNETbGm8RjQyLEadgGG1fV4TDytx3z2ea7",
                "valid_job": 11,
                "updated": "2025-06-20T13:57:23.185054",
                "on_site": null,
                "distance": null,
                "job_creator": 1,
                "skill": 86,
                "sp": 2,
                "team_leader": null,
                "area": 1,
                "state": 218,
                "supplier_type": 9,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            },
            {
                "id": 2825,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152662200",
                "long": "27.921810800",
                "claim": {
                    "id": 1253,
                    "mid": "KP*********",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Lava",
                        "surname": "Testing New OTP Solution Three"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "50 Constantia Boulevard",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "BxxNCyKDswtArLrpx7JcDT6M1DEu59tKtRFNaSrWYg8veSqY9fLnJb",
                "valid_job": null,
                "updated": "2025-06-20T14:40:15.925988",
                "on_site": null,
                "distance": null,
                "job_creator": 1,
                "skill": 39,
                "sp": 19,
                "team_leader": null,
                "area": 1,
                "state": 222,
                "supplier_type": 4,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            }
        ],
        "note_count": 1,
        "repudiation": null,
        "deceased": [],
        "form_start": "2025-06-20T13:57:23.121512",
        "assessor": null,
        "cat_code": null,
        "claim_type_id": 1,
        "private_banking": 0,
        "unread_note_count": null,
        "source": "KingPrice",
        "source_id": 3,
        "source_key": "KPC"
    },
    {
        "id": 1252,
        "applicant": {
            "first_name": "Lava",
            "surname": "Testing OTP Solution Two",
            "id_number": "*************",
            "claimantpoliceynum": "********"
        },
        "application_creator": {
            "id": 1,
            "full_name": null,
            "roles": [
                6,
                11,
                34,
                1,
                27
            ]
        },
        "application_date": "2025-06-20T13:02:45.309346",
        "state": 3,
        "state_change_date": "2025-06-20T13:02:45.307231",
        "location": 13611,
        "modified_date": "2025-06-20T13:51:07.458083",
        "mid": "KP********",
        "sub_section": 3,
        "jobs": [
            {
                "id": 2821,
                "appointment": {
                    "id": 1728,
                    "job": 2821,
                    "state": 6,
                    "range_start": "2025-06-23T14:30:00",
                    "range_end": null,
                    "appointment_type": 6,
                    "reason": "Emergency repairs done"
                },
                "note_count": 1,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152662200",
                "long": "27.921810800",
                "claim": {
                    "id": 1252,
                    "mid": "KP********",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Lava",
                        "surname": "Testing OTP Solution Two"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "50 Constantia Boulevard",
                "postal_code": "1709",
                "claim_value": "2575.65",
                "mid": null,
                "ping_count": 1,
                "token": "kVkQagdb5FQfGusYDETML6BFBNcLQYSAguASCNK7nYubw2yzzSbdWQ",
                "valid_job": 1,
                "updated": "2025-06-20T13:51:07.410683",
                "on_site": true,
                "distance": null,
                "job_creator": 1,
                "skill": 1,
                "sp": 4,
                "team_leader": 26,
                "area": 1,
                "state": 28,
                "supplier_type": 1,
                "forced_location": 13612,
                "assessor": null,
                "authorizer": 4,
                "location": null
            },
            {
                "id": 2820,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152662200",
                "long": "27.921810800",
                "claim": {
                    "id": 1252,
                    "mid": "KP********",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Lava",
                        "surname": "Testing OTP Solution Two"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "50 Constantia Boulevard",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "7HTXJLPXWr4a67TqMEgvCk5vfHm3MavtNVAvds2hM6H86y2dTfeayK",
                "valid_job": 11,
                "updated": "2025-06-20T13:02:45.375021",
                "on_site": null,
                "distance": null,
                "job_creator": 1,
                "skill": 86,
                "sp": 2,
                "team_leader": null,
                "area": 1,
                "state": 218,
                "supplier_type": 9,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            },
            {
                "id": 2822,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152662200",
                "long": "27.921810800",
                "claim": {
                    "id": 1252,
                    "mid": "KP********",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Lava",
                        "surname": "Testing OTP Solution Two"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "50 Constantia Boulevard",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "Q5frmdThTj6fvwjjvQ8ySmFFvucSeqQkmM7KLWSC6F9vtQjxm74eK6",
                "valid_job": null,
                "updated": "2025-06-20T13:34:29.708872",
                "on_site": null,
                "distance": null,
                "job_creator": 1,
                "skill": 39,
                "sp": 19,
                "team_leader": null,
                "area": 1,
                "state": 222,
                "supplier_type": 4,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            }
        ],
        "note_count": 1,
        "repudiation": null,
        "deceased": [],
        "form_start": "2025-06-20T13:02:45.309071",
        "assessor": null,
        "cat_code": null,
        "claim_type_id": 1,
        "private_banking": 0,
        "unread_note_count": null,
        "source": "KingPrice",
        "source_id": 3,
        "source_key": "KPC"
    },
    {
        "id": 1251,
        "applicant": {
            "first_name": "Lava",
            "surname": "Testing New OTP Solution",
            "id_number": "*************",
            "claimantpoliceynum": "********"
        },
        "application_creator": {
            "id": 1,
            "full_name": null,
            "roles": [
                6,
                11,
                34,
                1,
                27
            ]
        },
        "application_date": "2025-06-20T11:15:43.085350",
        "state": 3,
        "state_change_date": "2025-06-20T11:15:43.082680",
        "location": 13609,
        "modified_date": "2025-06-20T12:57:40.548271",
        "mid": "KP********",
        "sub_section": 3,
        "jobs": [
            {
                "id": 2818,
                "appointment": {
                    "id": 1726,
                    "job": 2818,
                    "state": 6,
                    "range_start": "2025-06-23T13:30:00",
                    "range_end": null,
                    "appointment_type": 6,
                    "reason": ""
                },
                "note_count": 1,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152662200",
                "long": "27.921810800",
                "claim": {
                    "id": 1251,
                    "mid": "KP********",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Lava",
                        "surname": "Testing New OTP Solution"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "50 Constantia Boulevard",
                "postal_code": "1709",
                "claim_value": "2575.65",
                "mid": null,
                "ping_count": 1,
                "token": "JWgTkHyaYsKjxde7VgmcFxVgvbDW95jd8uaNzeYWzsjNeypxnSeduM",
                "valid_job": 1,
                "updated": "2025-06-20T12:57:40.494961",
                "on_site": true,
                "distance": null,
                "job_creator": 1,
                "skill": 1,
                "sp": 4,
                "team_leader": 26,
                "area": 1,
                "state": 28,
                "supplier_type": 1,
                "forced_location": 13610,
                "assessor": null,
                "authorizer": 4,
                "location": null
            },
            {
                "id": 2817,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152662200",
                "long": "27.921810800",
                "claim": {
                    "id": 1251,
                    "mid": "KP********",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Lava",
                        "surname": "Testing New OTP Solution"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "50 Constantia Boulevard",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "PGkLsH64RfNurmBwnJFd6hNdwTXfq9TVdwNwf8eG4WauzJsraxd3Na",
                "valid_job": 11,
                "updated": "2025-06-20T11:15:43.148465",
                "on_site": null,
                "distance": null,
                "job_creator": 1,
                "skill": 86,
                "sp": 2,
                "team_leader": null,
                "area": 1,
                "state": 218,
                "supplier_type": 9,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            },
            {
                "id": 2819,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152662200",
                "long": "27.921810800",
                "claim": {
                    "id": 1251,
                    "mid": "KP********",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Lava",
                        "surname": "Testing New OTP Solution"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Constantia Kloof"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Constantia Kloof",
                "address": "50 Constantia Boulevard",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "kj8wh6YbLSwByBXDNRuKXpK5En4mWqgJhwg2pYS1VFpM7WAEPqzqzx",
                "valid_job": null,
                "updated": "2025-06-20T12:26:46.769937",
                "on_site": null,
                "distance": null,
                "job_creator": 1,
                "skill": 39,
                "sp": 19,
                "team_leader": null,
                "area": 1,
                "state": 222,
                "supplier_type": 4,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            }
        ],
        "note_count": 1,
        "repudiation": null,
        "deceased": [],
        "form_start": "2025-06-20T11:15:43.084963",
        "assessor": null,
        "cat_code": null,
        "claim_type_id": 1,
        "private_banking": 0,
        "unread_note_count": null,
        "source": "KingPrice",
        "source_id": 3,
        "source_key": "KPC"
    },
    {
        "id": 1250,
        "applicant": {
            "first_name": "TESTREASONS",
            "surname": "TESTREASONS",
            "id_number": "*************",
            "claimantpoliceynum": "********"
        },
        "application_creator": {
            "id": 1,
            "full_name": null,
            "roles": [
                6,
                11,
                34,
                1,
                27
            ]
        },
        "application_date": "2025-06-19T14:19:48.853225",
        "state": 2,
        "state_change_date": "2025-06-19T14:19:48.851067",
        "location": 13608,
        "modified_date": "2025-06-20T12:00:49.450460",
        "mid": "TESTREASONS1",
        "sub_section": 3,
        "jobs": [
            {
                "id": 2815,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.153082700",
                "long": "27.920034300",
                "claim": {
                    "id": 1250,
                    "mid": "TESTREASONS1",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "TESTREASONS",
                        "surname": "TESTREASONS"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Florida"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Florida",
                "address": "50 constantia boulevard",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "c5W6B3rkKv3HGzGCXFebNEN9btQxCdnuxCGRVWYa6LC1f5aSs6pPfz",
                "valid_job": 11,
                "updated": "2025-06-20T08:12:33.206582",
                "on_site": null,
                "distance": null,
                "job_creator": 1,
                "skill": 86,
                "sp": 2,
                "team_leader": null,
                "area": 1,
                "state": 45,
                "supplier_type": 9,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            },
            {
                "id": 2816,
                "appointment": {
                    "id": 1724,
                    "job": 2816,
                    "state": 1,
                    "range_start": "2025-06-19T16:30:00",
                    "range_end": null,
                    "appointment_type": 5,
                    "reason": null
                },
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.153082700",
                "long": "27.920034300",
                "claim": {
                    "id": 1250,
                    "mid": "TESTREASONS1",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "TESTREASONS",
                        "surname": "TESTREASONS"
                    },
                    "property_city": "Roodepoort",
                    "property_complex": null,
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Florida"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Florida",
                "address": "50 constantia boulevard",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "dKNa8djsGhfuXLj6PeFr9m9JCLyJFMjsEW892hapcT1cuk3sEqgDHJ",
                "valid_job": null,
                "updated": "2025-06-19T14:25:02.205884",
                "on_site": null,
                "distance": null,
                "job_creator": 1,
                "skill": 1,
                "sp": 4,
                "team_leader": null,
                "area": 1,
                "state": 21,
                "supplier_type": 1,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            }
        ],
        "note_count": 0,
        "repudiation": null,
        "deceased": [],
        "form_start": "2025-06-19T14:19:48.852974",
        "assessor": null,
        "cat_code": null,
        "claim_type_id": 1,
        "private_banking": 0,
        "unread_note_count": null,
        "source": "KingPrice",
        "source_id": 3,
        "source_key": "KPC"
    },
    {
        "id": 1249,
        "applicant": {
            "first_name": "Themba",
            "surname": "Bavuma",
            "id_number": "*************",
            "claimantpoliceynum": "TB12545888"
        },
        "application_creator": {
            "id": 84,
            "full_name": "BR CH",
            "roles": [
                6,
                11,
                34,
                1
            ]
        },
        "application_date": "2025-06-19T08:21:23.807975",
        "state": 2,
        "state_change_date": "2025-06-19T08:21:23.805828",
        "location": 13606,
        "modified_date": "2025-06-24T14:55:48.647516",
        "mid": "TB0000125",
        "sub_section": 3,
        "jobs": [
            {
                "id": 2814,
                "appointment": {
                    "id": 1723,
                    "job": 2814,
                    "state": 1,
                    "range_start": "2025-06-19T12:00:00",
                    "range_end": null,
                    "appointment_type": 5,
                    "reason": null
                },
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152868800",
                "long": "27.921857100",
                "claim": {
                    "id": 1249,
                    "mid": "TB0000125",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Themba",
                        "surname": "Bavuma"
                    },
                    "property_city": "Johannesbrg",
                    "property_complex": "quadram",
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Roodepoort"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Roodepoort",
                "address": "constantia",
                "postal_code": "",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "KVemmwTNDRvXKFewdnSjha828aX5JrGa1tYhmLdVeMug7FrwSsm2Le",
                "valid_job": null,
                "updated": "2025-06-24T14:55:48.599449",
                "on_site": null,
                "distance": null,
                "job_creator": 84,
                "skill": 1,
                "sp": 224,
                "team_leader": 86,
                "area": 1,
                "state": 32,
                "supplier_type": 1,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            },
            {
                "id": 2813,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152868800",
                "long": "27.921857100",
                "claim": {
                    "id": 1249,
                    "mid": "TB0000125",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Themba",
                        "surname": "Bavuma"
                    },
                    "property_city": "Johannesbrg",
                    "property_complex": "quadram",
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "Roodepoort"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "Roodepoort",
                "address": "constantia",
                "postal_code": "",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "Vsyqmfe34rBhVPekdk3N67kes7peFfMWEdj57n4SA99RF8ujHPAvVj",
                "valid_job": 11,
                "updated": "2025-06-20T10:46:07.878034",
                "on_site": null,
                "distance": null,
                "job_creator": 84,
                "skill": 86,
                "sp": 2,
                "team_leader": null,
                "area": 1,
                "state": 45,
                "supplier_type": 9,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            }
        ],
        "note_count": 0,
        "repudiation": null,
        "deceased": [],
        "form_start": "2025-06-19T08:21:23.807715",
        "assessor": null,
        "cat_code": null,
        "claim_type_id": 1,
        "private_banking": 0,
        "unread_note_count": null,
        "source": "KingPrice",
        "source_id": 3,
        "source_key": "KPC"
    },
    {
        "id": 1248,
        "applicant": {
            "first_name": "Dale",
            "surname": "Steyn",
            "id_number": "*************",
            "claimantpoliceynum": "DS0012458"
        },
        "application_creator": {
            "id": 84,
            "full_name": "BR CH",
            "roles": [
                6,
                11,
                34,
                1
            ]
        },
        "application_date": "2025-06-19T07:30:53.947256",
        "state": 2,
        "state_change_date": "2025-06-19T07:30:53.945258",
        "location": 13605,
        "modified_date": "2025-06-20T11:20:08.188878",
        "mid": "DS0125000",
        "sub_section": 3,
        "jobs": [
            {
                "id": 2811,
                "appointment": {},
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152748400",
                "long": "27.921953700",
                "claim": {
                    "id": 1248,
                    "mid": "DS0125000",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Dale",
                        "surname": "Steyn"
                    },
                    "property_city": "johannesburg",
                    "property_complex": "quadram",
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "roodepoort"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "roodepoort",
                "address": "constantia",
                "postal_code": "1709",
                "claim_value": "100.00",
                "mid": null,
                "ping_count": 1,
                "token": "fqvuvFLcTVg5xW6Vaezet7uV5YCGRBTV43PucQXnRqvBM8cTCwfFWT",
                "valid_job": 11,
                "updated": "2025-06-20T11:20:08.142651",
                "on_site": null,
                "distance": null,
                "job_creator": 84,
                "skill": 86,
                "sp": 2,
                "team_leader": null,
                "area": 1,
                "state": 50,
                "supplier_type": 9,
                "forced_location": null,
                "assessor": null,
                "authorizer": 15,
                "location": null
            },
            {
                "id": 2812,
                "appointment": {
                    "id": 1722,
                    "job": 2812,
                    "state": 1,
                    "range_start": "2025-06-19T18:00:00",
                    "range_end": null,
                    "appointment_type": 5,
                    "reason": null
                },
                "note_count": 0,
                "unread_note_count": null,
                "claim_type_id": 1,
                "assessor_name": "",
                "lat": "-26.152748400",
                "long": "27.921953700",
                "claim": {
                    "id": 1248,
                    "mid": "DS0125000",
                    "is_cat": false,
                    "cat_code": null,
                    "applicant": {
                        "first_name": "Dale",
                        "surname": "Steyn"
                    },
                    "property_city": "johannesburg",
                    "property_complex": "quadram",
                    "property_complex_block": "",
                    "property_complex_unit_number": "",
                    "property_street_name": "",
                    "property_street_number": "",
                    "property_suburb": "roodepoort"
                },
                "source": "KingPrice",
                "source_id": 3,
                "source_key": "KPC",
                "suburb": "roodepoort",
                "address": "constantia",
                "postal_code": "1709",
                "claim_value": null,
                "mid": null,
                "ping_count": 1,
                "token": "ssFL6THCPvP1aCQ5k6r3m8xfxdcDdKchxzCLPn7XCRPJYjS82Bjaaw",
                "valid_job": null,
                "updated": "2025-06-19T07:36:02.048956",
                "on_site": null,
                "distance": null,
                "job_creator": 84,
                "skill": 1,
                "sp": 224,
                "team_leader": null,
                "area": 1,
                "state": 21,
                "supplier_type": 1,
                "forced_location": null,
                "assessor": null,
                "authorizer": null,
                "location": null
            }
        ],
        "note_count": 0,
        "repudiation": null,
        "deceased": [],
        "form_start": "2025-06-19T07:30:53.946979",
        "assessor": null,
        "cat_code": null,
        "claim_type_id": 1,
        "private_banking": 0,
        "unread_note_count": null,
        "source": "KingPrice",
        "source_id": 3,
        "source_key": "KPC"
    },
]