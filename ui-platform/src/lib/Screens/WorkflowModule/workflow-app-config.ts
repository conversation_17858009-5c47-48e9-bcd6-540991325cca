import { AppConfig } from "../../../lib/Engine/models/app.config";


export const moduleTabs = [
  { name: 'Claims & Jobs', path: '/workflow/list-view' },
  { name: 'Calendar', path: '/workflow/calendar-view' },
  { name: 'Map', path: '/workflow/map-view' },
  {
    name: 'Copilot Work',
    path: '/workflow/copilot-view',
  },
]

export const workflowAppConfig: AppConfig = {
    toolBarConfig: {
        modulesList: [
          { name: 'Workflow', path: '/workflow' },
          {name: 'Teams', path: '/teams' },
          {name: 'Dashboard', path: '/dashboard' },
          {name: 'Working Capital', path: '/working-capital' },
        
        ],
        buttonText: 'New Job',
        username: '<PERSON>hab<PERSON>',
        email: '<EMAIL>',
        // avatarHandler: (e) => {
        //   console.log('User avatar clicked', e);
        // },
        // optionsHandler: (e) => {
        //   console.log('Options Btn clicked', e);
        // },
        menuItems: [
          {
            label: 'Switch Accounts',
            menuItems: [
                {
                    label: 'Profile Settings',
                    icon: 'bell-02',
                    // onClick: (e) => {
                    //   console.log('Click event registered:', e);
                    // },
                  }
            ],
          },
          {
            label: 'Logout',
            menuItems: [
                {
                    label: 'Access',
                  icon: 'bell-02',
                  // onClick: () => {
                  //   alert('You have been logged out');
                  // },
                }
            ],
          },
          {
            label: 'No Action',
            menuItems: [
                {
                    label: 'Disabled Action',
                    icon: 'x-xircle',
                    disabled: true,
                }
            ],
          },
        ],
      },
    fetchCalls: [
        {key: 'posts', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos'},
    ],
    onEnter: [],
    onLeave: [],
    workflow: {
      defaultWorkflowType: 'claims-and-jobs',
      workflowTypes: {
        'claims-and-jobs': {
          fetchCalls: [
              {key: 'claims', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos'},
  
          ],
          onEnter: [],
          onLeave: [],
          defaultView: 'details',
          views: {
              details: {
                  layout: {},
                  onEnter: [],
                  onLeave: [],
                  fetchCalls: [],
                  fragments: [
                      // Filter Fragment
                      // Claim card detail list fragment
                      {component: 'TestFilteringAndPagination', props: {items: '$pageItems'}, layout: {}}
                  ],
                  filterConfig: {
                      inputCollection: 'claims',
                      outputCollection: 'filteredClaims',
                      filterConditionsSource: 'workflowFilters',
                      sortingCriteriaSource: 'workflowSorts'
                  },
                  paginationConfig: {
                      dataSource: 'filteredClaims',
                      itemsPerPage: 15,
                  }
              },
          },
          actionPanels:  [
            {
              icon: 'file-check-02',
              title: 'Available Jobs', //?actionPanel=Messages--bell-02

              // fetchCalls: [],
              layout: {},
              onEnter: [],
              onLeave: [],
              fragments: [
                {
                  component: 'ActionPanelAvailableJobsFragment',
                  props: {},
                  layout: {}
                }
              ],
              actionLevel: 'bottomControls',
            },
            {
              icon: 'bell-02',
              title: 'Reminders', //?actionPanel=Messages--bell-02

              // fetchCalls: [],
              layout: {},
              onEnter: [],
              onLeave: [],
              fragments: [
                {
                  component: 'ReminderView',
                  layout: {
                    marginTop: '10px',
                    marginLeft: '10px',
                    marginRight: '10px',
                  },
                  props: {
                    //reminders: [],
                    toolbar: {
                      buttonText: 'CREATE REMINDER',
                      onClick: [
                        {
                          type: 'clientAction',
                          action: 'triggerModal',
                          payload: [
                            {
                              display: true,
                              type: 'warning',
                              heading: 'Add New Reminder',
                              headingType: 'page-heading',
                              layout: {},
                              onEnter: [],
                              onLeave: [],
                              fragments: [
                                {
                                  component: 'FormBuilder',
                                  layout: {
                                    display: 'grid',
                                    justifyItems: 'center',
                                    margin: '0 auto',
                                  },
                                  props: {
                                    config: {
                                      style: {
                                        display: 'grid',
                                        gridTemplateColumns: 'repeat(2, 1fr)',
                                        rowGap: '1rem',
                                        columnGap: '1rem',
                                        width: 'calc(100vw * 0.5)',
                                        maxWidth: '819px',
                                      },
                                      controls: [
                                        {
                                          type: 'plain-text',
                                          name: 'title_of_reminder',
                                          label: 'Title of Reminder',
                                          css: {
                                            wrapper: {
                                              gridColumn: '1',
                                              gridRow: '1',
                                              width: '100%',
                                            },
                                          },
                                        },
                                        {
                                          type: 'single-select',
                                          name: 'channel',
                                          label: 'Channel',
                                          labelProp: 'label',
                                          valueProp: 'value',
                                          options: {
                                            source: 'literal',
                                            data: [
                                              {
                                                value: 1,
                                                label: 'General',
                                              },
                                              {
                                                value: 2,
                                                label: 'Phone',
                                              },
                                              {
                                                value: 3,
                                                label: 'Email',
                                              },
                                            ],
                                          },
                                          css: {
                                            wrapper: {
                                              wrapper: {
                                                gridColumn: '2',
                                                gridRow: '1',
                                                width: '100%',
                                              },
                                            },
                                          },
                                        },
                                        {
                                          type: 'datepicker',
                                          name: 'reminder_date',
                                          label: 'Date',
                                          placeholder: 'Select date for reminder',
                                          css: {
                                            wrapper: {
                                              gridColumn: '1',
                                              gridRow: '2',
                                            },
                                          },
                                        },
                                        {
                                          type: 'timepicker',
                                          name: 'reminder_time',
                                          label: 'Time',
                                          css: { 
                                            wrapper: 
                                            { 
                                              gridColumn: 2, 
                                              gridRow: 2, 
                                            } 
                                          },
                                          placeholder: '--:--',
                                        },
                                        {
                                          type: 'textarea',
                                          name: 'reminder_message',
                                          label: 'Reminder Message',
                                          css: {
                                            wrapper: {
                                              gridColumn: '1 / span 2',
                                              gridRow: '3',
                                              width: '100%',
                                            },
                                          },
                                        },
                                        {
                                          type: 'plain-text',
                                          name: 'link_to_claim',
                                          label: 'Link to claim',
                                          icon: 'search-sm',
                                          position: 'right',
                                          css: {
                                            wrapper: {
                                              gridColumn: '1 / span 2',
                                              gridRow: '4',
                                              width: '100%',
                                            },
                                          },
                                        },
                                      ],
                                    },
                                  },
                                },
                                {
                                  component: 'ButtonRow',
                                  layout: {
                                    width: 'fit-content',
                                    margin: 'auto',
                                  },
                                  props: {
                                    buttons: [
                                      {
                                        btnValue: 'Cancel',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                            // payload: [
                                            //   {
                                            //     "display": false,
                                            //   },
                                            // ],
                                          },
                                        ],
                                      },
                                      {
                                        btnValue: 'Add Reminder',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'submitAsync',
                                            payload: {
                                              calls: [{
                                                key: 'addReminder',
                                                data: {
                                                  title: "{formDataRaw.title_of_reminder}",
                                                  channel: "{formDataRaw.channel}",
                                                  date: "{formDataRaw.reminder_date}",
                                                  time: "{formDataRaw.reminder_time}",
                                                  message: "{formDataRaw.reminder_message}",
                                                  link: "{formDataRaw.link_to_claim}",
                                                }
                                              }],
                                              onFinish: {
                                                type: 'clientAction',
                                                action: 'closeModal',
                                                payload: [
                                                  {
                                                    display: false,
                                                  },
                                                ],
                                              }
                                            },
                                          },
                                        
                                        ],
                                      },
                                    ],
                                  },
                                },
                              ],
                              navs: [],
                            },
                          ],
                        },
                      ],
                    },
                  },
                },
              ],
              actionLevel: 'bottomControls',
            },
            {
                  icon: "trash-01",
                  title: "Scratch Pad", //?actionPanel=Messages--bell-02
                  layout: {},
                  onEnter: [],
                  onLeave: [],
                  fragments: [
                    {
                      component: "ScratchPadView",
                      layout: { marginLeft: "10px", marginRight: "10px" },
                      props: {
                        titlePlaceholder: "Heading",
                        icon: "trash-01",
                        iconHandler: (data: { heading: string; body: string }) =>
                          console.log(
                            "got data: Heading - " + data.heading + " Body - " + data.body
                          ),
                        placeHolder: "Text here...",
                      },
                    },
                  ],
                  actionLevel: "bottomControls",
             }
          ],
       },
      },
    },
    states: {
        workflow: {
            title: { 
                template: 'Payment preview completed {toUpperCase(todo.completed)} with userId of {foo.userId} count is {toUpperCase(todo.title)} with count {count(todo.title)}', 
                
            },
            fetchCalls: [ ],
            onEnter: [],
            onLeave: [],
            defaultScreen: 'preview',
            screens: { 
                // screen
                preview: {
                    layout: {
                        backgroundColor: 'coral',
                    },
                    onEnter: [
                        // { type: 'clientAction', action: 'storeInLocalStorage', payload: { key: 'bar', value: '@form:screenInput' } },
                    ],
                    onLeave: [],
                    fetchCalls: [],
                    fragments: [
                        {
                            component: 'FormBuilder',
                            props: {
                                config: {
                                    style: {},
                                    controls: [
                                        {
                                            name: 'first_name',
                                            label: 'First Name'
                                        }
                                    ]
                                }
                            },
                            layout: {},

                        },
                        {
                            component: 'FormBuilder',
                            props: {
                                config: {
                                    style: {},
                                    controls: [
                                        {
                                            name: 'first_name',
                                            label: 'First Name'
                                        }
                                    ]
                                }
                            },
                            layout: {},

                        }
                    ],
                    navs: [
                        { label: 'Go Back', position: 'left' },
                        { label: 'Summary', toScreen: 'summary', position: 'left' },
                    ],
                },
                // end screen
                  // screen
                  summary: {
                    layout: {
                        backgroundColor: 'thistle'
                    },
                    onEnter: [
                        // { type: 'clientAction', action: 'storeInLocalStorage', payload: { key: 'bar', value: '@form:screenInput' } },
                    ],
                    onLeave: [],
                    fetchCalls: [],
                    fragments: [
     
                    ],
                    navs: [
                        { label: 'Go Back To..', toScreen: 'preview', position: 'left' },
                        // { label: 'Next', onClick: [], position: 'left' },
                    ],
                },
                // end screen
            },
            actionPanels:  [
                {
                    icon: 'file-check-02',
                    title: 'Available Jobs', //?actionPanel=Messages--bell-02
      
                    // fetchCalls: [],
                    layout: {},
                    onEnter: [],
                    onLeave: [],
                    fragments: [
                      {
                        component: 'ActionPanelAvailableJobsFragment',
                        props: {},
                        layout: {}
                      }
                    ],
                    actionLevel: 'bottomControls',
                  },
                  {
                    icon: 'bell-02',
                    title: 'Reminders', //?actionPanel=Messages--bell-02
      
                    // fetchCalls: [],
                    layout: {},
                    onEnter: [],
                    onLeave: [],
                    fragments: [
                      {
                        component: 'ReminderView',
                        layout: {
                          marginTop: '10px',
                          marginLeft: '10px',
                          marginRight: '10px',
                        },
                        props: {
                          //reminders: [],
                          toolbar: {
                            buttonText: 'CREATE REMINDER',
                            onClick: [
                              {
                                type: 'clientAction',
                                action: 'triggerModal',
                                payload: [
                                  {
                                    display: true,
                                    type: 'warning',
                                    heading: 'Add New Reminder',
                                    headingType: 'page-heading',
                                    layout: {},
                                    onEnter: [],
                                    onLeave: [],
                                    fragments: [
                                      {
                                        component: 'FormBuilder',
                                        layout: {
                                          display: 'grid',
                                          justifyItems: 'center',
                                          margin: '0 auto',
                                        },
                                        props: {
                                          config: {
                                            style: {
                                              display: 'grid',
                                              gridTemplateColumns: 'repeat(2, 1fr)',
                                              rowGap: '1rem',
                                              columnGap: '1rem',
                                              width: 'calc(100vw * 0.5)',
                                              maxWidth: '819px',
                                            },
                                            controls: [
                                              {
                                                type: 'plain-text',
                                                name: 'title_of_reminder',
                                                label: 'Title of Reminder',
                                                css: {
                                                  wrapper: {
                                                    gridColumn: '1',
                                                    gridRow: '1',
                                                    width: '100%',
                                                  },
                                                },
                                              },
                                              {
                                                type: 'single-select',
                                                name: 'channel',
                                                label: 'Channel',
                                                labelProp: 'label',
                                                valueProp: 'value',
                                                options: {
                                                  source: 'literal',
                                                  data: [
                                                    {
                                                      value: 1,
                                                      label: 'General',
                                                    },
                                                    {
                                                      value: 2,
                                                      label: 'Phone',
                                                    },
                                                    {
                                                      value: 3,
                                                      label: 'Email',
                                                    },
                                                  ],
                                                },
                                                css: {
                                                  wrapper: {
                                                    wrapper: {
                                                      gridColumn: '2',
                                                      gridRow: '1',
                                                      width: '100%',
                                                    },
                                                  },
                                                },
                                              },
                                              {
                                                type: 'datepicker',
                                                name: 'reminder_date',
                                                label: 'Date',
                                                placeholder: 'Select date for reminder',
                                                css: {
                                                  wrapper: {
                                                    gridColumn: '1',
                                                    gridRow: '2',
                                                  },
                                                },
                                              },
                                              {
                                                type: 'timepicker',
                                                name: 'reminder_time',
                                                label: 'Time',
                                                css: { 
                                                  wrapper: 
                                                  { 
                                                    gridColumn: 2, 
                                                    gridRow: 2, 
                                                  } 
                                                },
                                                placeholder: '--:--',
                                              },
                                              {
                                                type: 'textarea',
                                                name: 'reminder_message',
                                                label: 'Reminder Message',
                                                css: {
                                                  wrapper: {
                                                    gridColumn: '1 / span 2',
                                                    gridRow: '3',
                                                    width: '100%',
                                                  },
                                                },
                                              },
                                              {
                                                type: 'plain-text',
                                                name: 'link_to_claim',
                                                label: 'Link to claim',
                                                icon: 'search-sm',
                                                position: 'right',
                                                css: {
                                                  wrapper: {
                                                    gridColumn: '1 / span 2',
                                                    gridRow: '4',
                                                    width: '100%',
                                                  },
                                                },
                                              },
                                            ],
                                          },
                                        },
                                      },
                                      {
                                        component: 'ButtonRow',
                                        layout: {
                                          width: 'fit-content',
                                          margin: 'auto',
                                        },
                                        props: {
                                          buttons: [
                                            {
                                              btnValue: 'Cancel',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                  // payload: [
                                                  //   {
                                                  //     "display": false,
                                                  //   },
                                                  // ],
                                                },
                                              ],
                                            },
                                            {
                                              btnValue: 'Add Reminder',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'submitAsync',
                                                  payload: {
                                                    calls: [{
                                                      key: 'addReminder',
                                                      data: {
                                                        title: "{formDataRaw.title_of_reminder}",
                                                        channel: "{formDataRaw.channel}",
                                                        date: "{formDataRaw.reminder_date}",
                                                        time: "{formDataRaw.reminder_time}",
                                                        message: "{formDataRaw.reminder_message}",
                                                        link: "{formDataRaw.link_to_claim}",
                                                      }
                                                    }],
                                                    onFinish: {
                                                      type: 'clientAction',
                                                      action: 'closeModal',
                                                      payload: [
                                                        {
                                                          display: false,
                                                        },
                                                      ],
                                                    }
                                                  },
                                                },
                                              
                                              ],
                                            },
                                          ],
                                        },
                                      },
                                    ],
                                    navs: [],
                                  },
                                ],
                              },
                            ],
                          },
                        },
                      },
                    ],
                    actionLevel: 'bottomControls',
                  },
                  {
                        icon: "edit-05",
                        title: "Scratch Pad", //?actionPanel=Messages--bell-02
                        layout: {},
                        onEnter: [],
                        onLeave: [],
                        fragments: [
                          {
                            component: "ScratchPadView",
                            layout: { marginLeft: "10px", marginRight: "10px" },
                            props: {
                              titlePlaceholder: "Heading / Title here..",
                              icon: "trash-01",
                              iconHandler: (data: { heading: string; body: string }) =>
                                console.log(
                                  "got data: Heading - " + data.heading + " Body - " + data.body
                                ),
                              placeHolder: "Text here...",
                            },
                          },
                        ],
                        actionLevel: "bottomControls",
                   }
              ],
        }
    }
};