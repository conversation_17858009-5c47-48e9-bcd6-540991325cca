import { DrillableObject, drillDown, PathString, PathValue } from './drillDown';



/**
 * Given a list of items, apply a filter criteria and then deduplicate the result, keeping only the "latest"
 * item according to the provided comparison function.
 *
 * @param list The list of items to filter and deduplicate.
 * @param filterCriteria A function that takes an item and returns true if it should be included in the result.
 * @param deduplicationKey A function that takes an item and returns a string key to deduplicate on.
 * @param compareForLatest A function that takes two items and returns a number indicating their relative order.
 *   This should behave like the compare function passed to Array.sort.
 * @returns A new list of items, filtered and deduplicated, with each item being the "latest" one according
 *   to the compareForLatest function.
 */
export function filterAndDeduplicateLatest<T extends DrillableObject>(
  list: T[],
  filterCriteria: (item: T) => boolean,
  deduplicationKey: (item: T) => string,
  compareForLatest: (a: T, b: T) => number // Like Array.sort compareFn
): T[] {
  const filtered = list.filter(filterCriteria);

  return Array.from(
    filtered.reduce((acc, item) => {
      const key = deduplicationKey(item);
      const existing = acc.get(key);

      // Keep item if no existing or if this one is "later"
      if (!existing || compareForLatest(item, existing) > 0) {
        acc.set(key, item);
      }

      return acc;
    }, new Map<string, T>())
  ).map(([, item]) => item);
}

/**
 * Given a list of items, apply a filter criteria and then deduplicate the result, keeping only the "latest"
 * item according to the provided comparison function.
 *
 * If a comparisonPath is provided, it will be used to extract a value from each item to compare.
 * If no comparisonPath is provided, the comparison function will be called with the entire item objects.
 *
 * @param list The list of items to filter and deduplicate.
 * @param filterCriteria A function that takes an item and returns true if it should be included in the result.
 * @param deduplicationKey A function that takes an item and returns a string key to deduplicate on.
 * @param comparisonPath An optional path string to extract a value for comparison.
 * @param comparisonFn An optional comparison function. If not provided, a default comparison function will be used.
 *   This should behave like the compare function passed to Array.sort.
 * @returns A new list of items, filtered and deduplicated, with each item being the "latest" one according
 *   to the compareForLatest function.
 */
export function filterAndDeduplicateByPath<
  T extends DrillableObject,
  P extends PathString<T, 5>
>(
  list: T[],
  filterCriteria: (item: T) => boolean,
  deduplicationKey: (item: T) => string,
  comparisonPath?: P,
  comparisonFn?: (
    a: PathValue<T, P> | undefined,
    b: PathValue<T, P> | undefined
  ) => number
): T[] {
  const filtered = list.filter(filterCriteria);

  const defaultCompareFn = (a: any, b: any) => {
    if (a === undefined && b === undefined) return 0;
    if (a === undefined) return -1;
    if (b === undefined) return 1;

    const aDate = new Date(a);
    const bDate = new Date(b);

    if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {
      return aDate.getTime() - bDate.getTime();
    }

    return String(a).localeCompare(String(b));
  };

  return Array.from(
    filtered.reduce((acc, item) => {
      const key = deduplicationKey(item);
      const existing = acc.get(key);

      if (!existing) {
        acc.set(key, item);
      } else {
        // Compare items to determine which is "latest"
        let comparison = 0;

        if (comparisonPath) {
          const itemValue = drillDown(item, comparisonPath);
          const existingValue = drillDown(existing, comparisonPath);
          comparison = (comparisonFn || defaultCompareFn)(
            itemValue,
            existingValue
          );
        } else if (comparisonFn) {
          // If no path but custom comparison function, compare whole objects
          comparison = comparisonFn(
            item as PathValue<T, P>,
            existing as PathValue<T, P>
          );
        }

        // Keep the "later" one (positive comparison means item > existing)
        if (comparison > 0) {
          acc.set(key, item);
        }
      }

      return acc;
    }, new Map<string, T>())
  ).map(([, item]) => item);
}

/**
 * Given a list of items, returns a new list with only the latest certification
 * for each {skill, issuer} pair. A certification is considered "latest" if its
 * `created` property has a later date than any other certification with the same
 * {skill, issuer} pair.
 *
 * The `created` property must be a string in ISO 8601 format.
 *
 * @example
 * const certs = [
 *   { created: '2022-01-01', meta: { certification: { skill: 'A', issuer: 'B' } } },
 *   { created: '2022-01-02', meta: { certification: { skill: 'A', issuer: 'B' } } },
 *   { created: '2022-01-03', meta: { certification: { skill: 'C', issuer: 'D' } } },
 *   { created: '2022-01-04', meta: { certification: { skill: 'A', issuer: 'B' } } },
 * ];
 *
 * const latestCerts = filterLatestCertifications(certs);
 * // latestCerts is now [{ created: '2022-01-04', ... }]
 */
export function filterLatestCertifications<
  T extends DrillableObject & {
    created: string;
    meta: { certification?: { skill: any; issuer: any } };
  }
>(list: T[]): T[] {
  return filterAndDeduplicateByPath(
    list,
    (item) => !!item.meta.certification,
    (item) =>
      `${item.meta.certification?.skill}-${item.meta.certification?.issuer}`,
    'created' as PathString<T[number], 5>
  );
}

// Example usage functions
export function exampleUsages() {
  const company_documentation = [
    {
      id: '9691645b-fe53-492d-ab17-923b86c44242',
      purpose: 'Advanced Plumbing',
      filename: 'Screenshot 2025-05-16 at 09.49.05.png',
      created: '2025-06-05T01:02:03.625496+02:00',
      meta: {
        certification: {
          skill: 113,
          issuer: 7,
          issued_at: '2025-6-3',
          valid_until: '2025-6-5',
          certificate_number: '099876',
        },
      },
      thumbnail: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAA...',
    },
    {
      id: '40db7ef4-cf86-4bb1-b4c2-76e272f12520',
      purpose: 'Air Conditioning',
      filename: 'Screenshot 2025-05-16 at 09.49.05.png',
      created: '2025-06-05T01:01:45.720301+02:00',
      meta: {
        certification: {
          skill: 38,
          issuer: 29,
          issued_at: '2025-6-5',
          valid_until: '2025-6-5',
          certificate_number: '098689',
        },
      },
      thumbnail:
        'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc...',
    },
    {
      id: '40db7ef4-cf86-4bb1-b4c2-76e272f12520',
      purpose: 'Air Conditioning',
      filename: 'Screenshot 2025-05-16 at 09.49.05.png',
      created: '2025-06-04T01:01:45.720301+02:00',
      meta: {
        certification: {
          skill: 38,
          issuer: 29,
          issued_at: '2025-6-5',
          valid_until: '2025-6-5',
          certificate_number: '098689',
        },
      },
      thumbnail:
        'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc...',
    },
    {
      id: '4a237352-63a9-4c3a-bfd8-4ddfb7668529',
      purpose: 'Reg BBEEE certificate',
      filename: 'Screenshot 2025-05-16 at 09.49.05.png',
      created: '2025-06-05T01:02:53.378729+02:00',
      meta: {},
      thumbnail:
        'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hy...',
    },
  ];

  const files = [...company_documentation]; // Same data for this example

  // Example 1: Basic usage with custom compare function
  const latestFiles = filterAndDeduplicateLatest(
    files,
    (item) => !!item.meta.certification,
    (item) =>
      `${item?.meta?.certification?.skill}-${item?.meta?.certification?.issuer}`,
    (a, b) => new Date(a.created).getTime() - new Date(b.created).getTime()
  );

  // Example 2: Using path-based comparison
  const latestCerts = filterAndDeduplicateByPath(
    company_documentation,
    (item) => !!item.meta.certification,
    (item) =>
      `${item?.meta?.certification?.skill}-${item?.meta?.certification?.issuer}`,
    'created' // Compare by created date
  );

  // Example 3: Using drillDown for nested path comparison
  const latestCertsByIssueDate = filterAndDeduplicateByPath(
    company_documentation,
    (item) => !!item.meta.certification,
    (item) => item?.meta?.certification?.skill?.toString() || '',
    'meta.certification.issued_at' // Deep nested path using drillDown
  );

  // Example 4: Custom comparison with drillDown for complex logic
  const latestValidCerts = filterAndDeduplicateByPath(
    company_documentation,
    (item) => !!item.meta.certification,
    (item) => item?.meta?.certification?.skill?.toString() || '',
    'meta.certification.valid_until',
    (a, b) => {
      // Handle undefined values
      if (!a && !b) return 0;
      if (!a) return -1;
      if (!b) return 1;

      const aValid = new Date(a) > new Date();
      const bValid = new Date(b) > new Date();

      // Prefer valid certificates
      if (aValid && !bValid) return 1;
      if (!aValid && bValid) return -1;

      // If both valid or both invalid, compare by date
      return new Date(a).getTime() - new Date(b).getTime();
    }
  );

  // Example 5: Specialized function
  const certificates = filterLatestCertifications(company_documentation);

  // Example 6: Using drillDown for array access
  const exampleWithArrays = [
    { tags: ['urgent', 'certification'], created: '2025-06-05' },
    { tags: ['normal', 'document'], created: '2025-06-04' },
    { tags: ['urgent', 'other'], created: '2025-06-06' },
  ];

  const latestUrgentItems = filterAndDeduplicateByPath(
    exampleWithArrays,
    (item) => drillDown(item, 'tags.0') === 'urgent', // Access first tag
    (item) => drillDown(item, 'tags.0') || 'unknown',
    'created'
  );

  return {
    latestFiles,
    latestCerts,
    latestCertsByIssueDate,
    latestValidCerts,
    certificates,
    latestUrgentItems,
  };
}
