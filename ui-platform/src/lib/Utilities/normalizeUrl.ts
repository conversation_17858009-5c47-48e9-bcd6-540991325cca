export function normalizeUrl(endpoint: string, baseUrl?: string, queryParams?: string): string {
  let normalizedUrl = baseUrl || '';
  if (normalizedUrl.endsWith('/')) {
    normalizedUrl = normalizedUrl.slice(0, -1);
  }
  if (endpoint.startsWith('/')) {
    normalizedUrl += endpoint;
  } else {
    normalizedUrl += '/' + endpoint;
  }
  if (queryParams) {
    normalizedUrl += queryParams;
  }
  return normalizedUrl;
}
